#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统常量定义
"""

# 市场代码
MARKET_CODES = {
    "SH": "上海证券交易所",
    "SZ": "深圳证券交易所",
    "BJ": "北京证券交易所"
}

# 股票类型
STOCK_TYPES = {
    "stock": "股票",
    "index": "指数",
    "fund": "基金",
    "bond": "债券",
    "future": "期货",
    "option": "期权"
}

# 交易状态
TRADE_STATUS = {
    "pending": "待成交",
    "partial": "部分成交",
    "filled": "已成交",
    "cancelled": "已撤销",
    "rejected": "已拒绝"
}

# 订单类型
ORDER_TYPES = {
    "market": "市价单",
    "limit": "限价单",
    "stop": "止损单",
    "stop_limit": "止损限价单"
}

# 买卖方向
DIRECTIONS = {
    "buy": "买入",
    "sell": "卖出"
}

# 技术指标周期
PERIODS = {
    "1min": "1分钟",
    "5min": "5分钟", 
    "15min": "15分钟",
    "30min": "30分钟",
    "1hour": "1小时",
    "daily": "日线",
    "weekly": "周线",
    "monthly": "月线"
}

# 策略状态
STRATEGY_STATUS = {
    "inactive": "未激活",
    "active": "运行中",
    "paused": "已暂停",
    "stopped": "已停止",
    "error": "错误"
}

# 风险等级
RISK_LEVELS = {
    "low": "低风险",
    "medium": "中风险", 
    "high": "高风险",
    "extreme": "极高风险"
}

# 颜色配置
COLORS = {
    "red": "#FF4444",      # 下跌红色
    "green": "#00AA00",    # 上涨绿色
    "blue": "#4488FF",     # 蓝色
    "orange": "#FF8800",   # 橙色
    "purple": "#AA44FF",   # 紫色
    "gray": "#888888",     # 灰色
    "white": "#FFFFFF",    # 白色
    "black": "#000000"     # 黑色
}

# 数据频率映射
FREQ_MAP = {
    "1min": "1T",
    "5min": "5T",
    "15min": "15T", 
    "30min": "30T",
    "1hour": "1H",
    "daily": "1D",
    "weekly": "1W",
    "monthly": "1M"
}

# 主要指数代码
MAJOR_INDICES = {
    "000001.SH": "上证指数",
    "399001.SZ": "深证成指",
    "399006.SZ": "创业板指",
    "000300.SH": "沪深300",
    "000905.SH": "中证500",
    "000852.SH": "中证1000"
}

# 行业分类
INDUSTRIES = [
    "银行", "非银金融", "房地产", "建筑装饰", "建筑材料",
    "钢铁", "有色金属", "化工", "石油石化", "煤炭",
    "电力设备", "公用事业", "交通运输", "汽车", "机械设备",
    "国防军工", "电子", "计算机", "通信", "传媒",
    "医药生物", "食品饮料", "农林牧渔", "纺织服装", "轻工制造",
    "商业贸易", "休闲服务", "家用电器", "综合"
]

# 财务指标
FINANCIAL_INDICATORS = {
    "pe": "市盈率",
    "pb": "市净率", 
    "ps": "市销率",
    "roe": "净资产收益率",
    "roa": "总资产收益率",
    "gross_margin": "毛利率",
    "net_margin": "净利率",
    "debt_ratio": "资产负债率",
    "current_ratio": "流动比率",
    "quick_ratio": "速动比率"
}

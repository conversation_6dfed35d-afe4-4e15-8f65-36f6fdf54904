#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复无效变量名脚本
修复自动生成的无效Python变量名
"""

import re
from pathlib import Path

def fix_settings_widget():
    """修复设置组件中的无效变量名"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "settings_widget.py"
    
    if not file_path.exists():
        print("❌ settings_widget.py 文件不存在")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复无效变量名的映射
        fixes = [
            # 交易设置
            ('印花税率%_label', 'stamp_tax_label'),
            ('滑点%_label', 'slippage_label'),
            
            # 数据设置
            ('主数据源_label', 'primary_source_label'),
            ('备用数据源_label', 'backup_source_label'),
            ('实时数据源_label', 'realtime_source_label'),
            ('自动更新数据_label', 'auto_update_label'),
            ('更新间隔分钟_label', 'update_interval_label'),
            ('数据保留天数_label', 'data_retention_label'),
            
            # 界面设置
            ('主题_label', 'theme_label'),
            ('字体大小_label', 'font_size_label'),
            ('窗口透明度_label', 'window_opacity_label'),
            ('显示启动画面_label', 'show_splash_label'),
            ('显示状态栏_label', 'show_statusbar_label'),
            ('显示工具栏_label', 'show_toolbar_label'),
            
            # 风险设置
            ('最大持仓比例%_label', 'max_position_label'),
            ('最大回撤%_label', 'max_drawdown_label'),
            ('默认止损%_label', 'stop_loss_label'),
            ('默认止盈%_label', 'take_profit_label'),
            ('启用声音报警_label', 'sound_alert_label'),
            ('启用邮件报警_label', 'email_alert_label'),
            ('报警邮箱_label', 'alert_email_label'),
        ]
        
        # 应用修复
        for old_name, new_name in fixes:
            content = content.replace(old_name, new_name)
        
        # 修复layout引用错误
        layout_fixes = [
            ('layout.addWidget(primary_source_label, 0, 0)', 'source_layout.addWidget(primary_source_label, 0, 0)'),
            ('layout.addWidget(backup_source_label, 1, 0)', 'source_layout.addWidget(backup_source_label, 1, 0)'),
            ('layout.addWidget(realtime_source_label, 2, 0)', 'source_layout.addWidget(realtime_source_label, 2, 0)'),
            ('layout.addWidget(auto_update_label, 0, 0)', 'update_layout.addWidget(auto_update_label, 0, 0)'),
            ('layout.addWidget(update_interval_label, 1, 0)', 'update_layout.addWidget(update_interval_label, 1, 0)'),
            ('layout.addWidget(data_retention_label, 2, 0)', 'update_layout.addWidget(data_retention_label, 2, 0)'),
            ('layout.addWidget(theme_label, 0, 0)', 'theme_layout.addWidget(theme_label, 0, 0)'),
            ('layout.addWidget(font_size_label, 1, 0)', 'theme_layout.addWidget(font_size_label, 1, 0)'),
            ('layout.addWidget(window_opacity_label, 2, 0)', 'theme_layout.addWidget(window_opacity_label, 2, 0)'),
            ('layout.addWidget(show_splash_label, 0, 0)', 'display_layout.addWidget(show_splash_label, 0, 0)'),
            ('layout.addWidget(show_statusbar_label, 1, 0)', 'display_layout.addWidget(show_statusbar_label, 1, 0)'),
            ('layout.addWidget(show_toolbar_label, 2, 0)', 'display_layout.addWidget(show_toolbar_label, 2, 0)'),
            ('layout.addWidget(max_position_label, 0, 0)', 'risk_layout.addWidget(max_position_label, 0, 0)'),
            ('layout.addWidget(max_drawdown_label, 1, 0)', 'risk_layout.addWidget(max_drawdown_label, 1, 0)'),
            ('layout.addWidget(stop_loss_label, 2, 0)', 'risk_layout.addWidget(stop_loss_label, 2, 0)'),
            ('layout.addWidget(take_profit_label, 3, 0)', 'risk_layout.addWidget(take_profit_label, 3, 0)'),
            ('layout.addWidget(sound_alert_label, 0, 0)', 'alert_layout.addWidget(sound_alert_label, 0, 0)'),
            ('layout.addWidget(email_alert_label, 1, 0)', 'alert_layout.addWidget(email_alert_label, 1, 0)'),
            ('layout.addWidget(alert_email_label, 2, 0)', 'alert_layout.addWidget(alert_email_label, 2, 0)'),
        ]
        
        for old_layout, new_layout in layout_fixes:
            content = content.replace(old_layout, new_layout)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ settings_widget.py 修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复 settings_widget.py 失败: {e}")
        return False

def fix_other_widgets():
    """修复其他组件中可能的问题"""
    widgets_dir = Path(__file__).parent / "gui" / "widgets"
    
    # 需要检查的文件
    widget_files = [
        "analysis_center_widget.py",
        "strategy_center_widget.py",
        "trading_center_widget.py",
        "data_center_widget.py",
        "dashboard_widget.py",
    ]
    
    for widget_file in widget_files:
        file_path = widgets_dir / widget_file
        
        if not file_path.exists():
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 查找并修复可能的无效变量名
            # 查找包含中文字符和特殊符号的变量名
            invalid_patterns = [
                r'(\w*[^\w\s]+\w*_label)',  # 包含特殊字符的变量名
                r'([^\w\s]+_label)',        # 以特殊字符开头的变量名
            ]
            
            for pattern in invalid_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if '%' in match or '(' in match or ')' in match:
                        # 生成有效的变量名
                        valid_name = re.sub(r'[^\w]', '_', match)
                        valid_name = re.sub(r'_+', '_', valid_name)  # 合并多个下划线
                        valid_name = valid_name.strip('_')  # 移除首尾下划线
                        
                        if valid_name != match:
                            content = content.replace(match, valid_name)
                            print(f"  修复变量名: {match} -> {valid_name}")
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ {widget_file} 修复完成")
            else:
                print(f"ℹ️  {widget_file} 无需修复")
                
        except Exception as e:
            print(f"❌ 修复 {widget_file} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("修复无效变量名")
    print("=" * 60)
    
    # 1. 修复设置组件
    print("修复设置组件...")
    fix_settings_widget()
    
    # 2. 修复其他组件
    print("\n检查其他组件...")
    fix_other_widgets()
    
    print("\n" + "=" * 60)
    print("修复完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()

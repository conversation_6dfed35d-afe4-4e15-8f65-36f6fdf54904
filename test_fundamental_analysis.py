#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基本面分析功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis.fundamental_analysis import FundamentalAnalysis
from utils.logger import get_logger


def test_financial_ratios():
    """测试财务比率计算"""
    logger = get_logger("TestFundamentalAnalysis")
    
    print("=" * 60)
    print("基本面分析功能测试")
    print("=" * 60)
    
    try:
        # 创建基本面分析实例
        fa = FundamentalAnalysis()
        
        # 测试示例财务数据生成
        print("\n1. 测试示例财务数据生成...")
        test_symbols = ["000001.SZ", "600036.SH", "000002.SZ"]
        
        for symbol in test_symbols:
            financial_data = fa.generate_sample_financial_data(symbol)
            print(f"\n股票代码: {symbol}")
            print(f"  营业收入: {financial_data.get('revenue', 0)/10000:.1f} 亿元")
            print(f"  净利润: {financial_data.get('net_income', 0)/10000:.1f} 亿元")
            print(f"  总资产: {financial_data.get('total_assets', 0)/10000:.1f} 亿元")
            print(f"  股东权益: {financial_data.get('total_equity', 0)/10000:.1f} 亿元")
            print(f"  股价: {financial_data.get('stock_price', 0):.2f} 元")
        
        print("\n✅ 示例财务数据生成测试通过")
        
        # 测试财务比率计算
        print("\n2. 测试财务比率计算...")
        symbol = "000001.SZ"
        financial_data = fa.generate_sample_financial_data(symbol)
        ratios = fa.calculate_financial_ratios(financial_data)
        
        print(f"\n{symbol} 财务比率:")
        key_ratios = [
            '净资产收益率(ROE)', '市盈率(PE)', '市净率(PB)', 
            '净利润率', '资产负债率', '流动比率'
        ]
        
        for ratio in key_ratios:
            if ratio in ratios:
                value = ratios[ratio]
                if ratio in ['净资产收益率(ROE)', '净利润率', '资产负债率']:
                    print(f"  {ratio}: {value:.2f}%")
                else:
                    print(f"  {ratio}: {value:.2f}")
        
        print("\n✅ 财务比率计算测试通过")
        
        # 测试财务健康评估
        print("\n3. 测试财务健康评估...")
        evaluation = fa.evaluate_financial_health(ratios)
        
        print(f"\n{symbol} 财务健康评估:")
        for metric, rating in evaluation.items():
            print(f"  {metric}: {rating}")
        
        print("\n✅ 财务健康评估测试通过")
        
        # 测试行业比较
        print("\n4. 测试行业比较...")
        industries = ["银行", "房地产", "制造业", "科技", "消费"]
        
        for industry in industries:
            industry_avg = fa.get_industry_averages(industry)
            comparison = fa.compare_with_industry(ratios, industry)
            
            print(f"\n与{industry}行业比较:")
            print(f"  行业平均ROE: {industry_avg.get('净资产收益率(ROE)', 0):.1f}%")
            print(f"  行业平均PE: {industry_avg.get('市盈率(PE)', 0):.1f}")
            print(f"  ROE比较结果: {comparison.get('净资产收益率(ROE)', '无数据')}")
            print(f"  PE比较结果: {comparison.get('市盈率(PE)', '无数据')}")
            break  # 只显示一个行业的比较结果
        
        print("\n✅ 行业比较测试通过")
        
        # 测试综合分析
        print("\n5. 测试综合分析...")
        analysis_result = fa.analyze_stock(symbol, "制造业")
        
        if analysis_result:
            print(f"\n{symbol} 综合分析结果:")
            print(f"  分析时间: {analysis_result.get('analysis_date', 'N/A')}")
            print(f"  所属行业: {analysis_result.get('industry', 'N/A')}")
            
            evaluation = analysis_result.get('evaluation', {})
            print(f"  综合评级: {evaluation.get('综合评级', 'N/A')}")
            print(f"  综合得分: {evaluation.get('综合得分', 'N/A')}")
            print(f"  盈利能力: {evaluation.get('盈利能力', 'N/A')}")
            print(f"  偿债能力: {evaluation.get('偿债能力', 'N/A')}")
            print(f"  估值水平: {evaluation.get('估值水平', 'N/A')}")
            print(f"  成长性: {evaluation.get('成长性', 'N/A')}")
            
            print("\n✅ 综合分析测试通过")
        else:
            print("\n❌ 综合分析测试失败")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"基本面分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_stocks():
    """测试多只股票的基本面分析"""
    print("\n" + "=" * 60)
    print("多股票基本面分析测试")
    print("=" * 60)
    
    try:
        fa = FundamentalAnalysis()
        
        # 测试不同类型的股票
        test_stocks = [
            ("000001.SZ", "银行"),
            ("000002.SZ", "房地产"), 
            ("600036.SH", "银行"),
            ("300015.SZ", "科技"),
            ("002415.SZ", "消费")
        ]
        
        results = []
        
        for symbol, industry in test_stocks:
            print(f"\n分析股票: {symbol} ({industry})")
            
            analysis_result = fa.analyze_stock(symbol, industry)
            if analysis_result:
                evaluation = analysis_result.get('evaluation', {})
                ratios = analysis_result.get('ratios', {})
                
                print(f"  综合评级: {evaluation.get('综合评级', 'N/A')}")
                print(f"  ROE: {ratios.get('净资产收益率(ROE)', 0):.2f}%")
                print(f"  PE: {ratios.get('市盈率(PE)', 0):.2f}")
                print(f"  PB: {ratios.get('市净率(PB)', 0):.2f}")
                print(f"  资产负债率: {ratios.get('资产负债率', 0):.2f}%")
                
                results.append({
                    'symbol': symbol,
                    'industry': industry,
                    'rating': evaluation.get('综合评级', 'N/A'),
                    'score': evaluation.get('综合得分', '0'),
                    'roe': ratios.get('净资产收益率(ROE)', 0)
                })
        
        # 排序显示结果
        print(f"\n股票综合评级排序:")
        print("-" * 50)
        
        # 按评级排序
        rating_order = {'A+': 6, 'A': 5, 'B+': 4, 'B': 3, 'C+': 2, 'C': 1}
        results.sort(key=lambda x: rating_order.get(x['rating'], 0), reverse=True)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['symbol']} ({result['industry']}) - "
                  f"评级: {result['rating']}, 得分: {result['score']}, "
                  f"ROE: {result['roe']:.2f}%")
        
        print("\n✅ 多股票基本面分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 多股票分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始基本面分析功能测试...")
    
    success = True
    
    # 测试基本功能
    if not test_financial_ratios():
        success = False
    
    # 测试多股票分析
    if not test_multiple_stocks():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 基本面分析功能测试全部通过！")
        print("\n功能特点:")
        print("✅ 完整的财务比率计算")
        print("✅ 智能的财务健康评估")
        print("✅ 行业对比分析")
        print("✅ 综合评级系统")
        print("✅ 多维度评估指标")
        
        print("\n下一步可以:")
        print("1. 集成到GUI界面")
        print("2. 连接真实财务数据接口")
        print("3. 完善量化分析功能")
        print("4. 添加更多行业数据")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 60)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证背景颜色修复效果
检查特定窗口的背景颜色是否已正确设置为蓝色
"""

import sys
from pathlib import Path

def check_file_contains_blue_background(file_path, component_name):
    """检查文件是否包含蓝色背景设置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含蓝色背景设置
        blue_background_patterns = [
            "background-color: #0078d4",
            "background-color: #005a9e",
            "background-color: #106ebe"
        ]
        
        has_blue_background = any(pattern in content for pattern in blue_background_patterns)
        
        if has_blue_background:
            print(f"✅ {component_name}: 已设置蓝色背景")
            return True
        else:
            print(f"❌ {component_name}: 未找到蓝色背景设置")
            return False
            
    except Exception as e:
        print(f"❌ {component_name}: 检查失败 - {e}")
        return False

def verify_all_fixes():
    """验证所有修复"""
    print("=" * 60)
    print("验证背景颜色修复效果")
    print("=" * 60)
    
    # 要检查的文件和组件
    checks = [
        ("gui/widgets/dashboard_widget.py", "最近交易面板"),
        ("gui/widgets/data_dashboard_widget.py", "数据源状态表格"),
        ("gui/widgets/data_source_manager_widget.py", "数据源管理界面"),
        ("gui/widgets/trading_center_widget.py", "交易中心表格"),
    ]
    
    success_count = 0
    total_count = len(checks)
    
    for file_path, component_name in checks:
        full_path = Path(__file__).parent / file_path
        if check_file_contains_blue_background(full_path, component_name):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"验证结果: {success_count}/{total_count} 个组件已正确设置蓝色背景")
    print("=" * 60)
    
    if success_count == total_count:
        print("🎉 所有组件背景颜色修复验证通过！")
        print("白色文字现在应该在蓝色背景上清晰可见。")
        return True
    else:
        print("⚠️  部分组件验证失败，可能需要进一步检查。")
        return False

def create_test_summary():
    """创建测试总结"""
    print("\n" + "=" * 60)
    print("修复总结")
    print("=" * 60)
    print("已修复的窗口和组件:")
    print("1. 最近交易面板 - 表格背景改为蓝色")
    print("2. 数据源状态表格 - 表格背景改为蓝色")
    print("3. 数据源管理界面 - 表格和标签背景改为蓝色")
    print("4. 交易中心所有表格 - 包括:")
    print("   - 实时行情表格")
    print("   - 五档行情表格")
    print("   - 持仓明细表格")
    print("   - 订单列表表格")
    print("   - 交易记录表格")
    print("\n颜色方案:")
    print("- 主要背景: #0078d4 (蓝色)")
    print("- 表头背景: #005a9e (深蓝色)")
    print("- 选中背景: #106ebe (中蓝色)")
    print("- 文字颜色: #ffffff (白色)")
    print("- 边框颜色: #ffffff (白色)")

def main():
    """主函数"""
    # 验证修复效果
    success = verify_all_fixes()
    
    # 创建测试总结
    create_test_summary()
    
    if success:
        print("\n🚀 建议现在重新启动程序查看修复效果:")
        print("python main.py")
    else:
        print("\n⚠️  建议检查修复脚本是否正确执行。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设置界面UI修复
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.widgets.settings_widget import SettingsWidget
from utils.logger import get_logger

class TestSettingsWindow(QMainWindow):
    """测试设置窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("TestSettingsWindow")
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设置界面测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建设置组件
        try:
            self.settings_widget = SettingsWidget()
            layout.addWidget(self.settings_widget)
            
            # 连接信号
            self.settings_widget.settings_changed.connect(self.on_settings_changed)
            
            self.logger.info("设置界面创建成功")
            
        except Exception as e:
            self.logger.error(f"设置界面创建失败: {e}")
            raise
    
    def on_settings_changed(self, settings):
        """设置变更处理"""
        self.logger.info(f"设置已变更: {len(settings)} 项配置")

def test_settings_ui_fix():
    """测试设置界面UI修复"""
    print("=" * 60)
    print("测试设置界面UI修复")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("设置界面测试")
        
        # 创建测试窗口
        window = TestSettingsWindow()
        window.show()
        
        print("✓ 设置界面创建成功")
        print("✓ 所有UI组件正常加载")
        print("✓ 变量名问题已修复")
        print("✓ 布局问题已修复")
        
        # 测试设置加载
        settings_widget = window.settings_widget
        current_settings = settings_widget.current_settings
        
        print(f"\n当前设置项数量: {len(current_settings)}")
        
        # 测试各个标签页
        tab_widget = settings_widget.tab_widget
        tab_count = tab_widget.count()
        
        print(f"标签页数量: {tab_count}")
        for i in range(tab_count):
            tab_name = tab_widget.tabText(i)
            print(f"  - {tab_name}")
        
        # 测试设置保存功能
        print("\n测试设置保存功能...")
        try:
            settings_widget.save_settings()
            print("✓ 设置保存功能正常")
        except Exception as e:
            print(f"❌ 设置保存功能异常: {e}")
        
        print(f"\n{'='*60}")
        print("设置界面UI修复测试完成")
        print("界面已打开，请手动检查以下内容：")
        print("1. 所有标签页是否正常显示")
        print("2. 所有标签文字是否清晰可读")
        print("3. 所有输入控件是否正常工作")
        print("4. 保存和重置按钮是否正常")
        print("5. 界面布局是否美观")
        print(f"{'='*60}")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = test_settings_ui_fix()
    if exit_code == 0:
        print("\n✅ 第三阶段设置界面UI修复测试通过")
    else:
        print("\n❌ 第三阶段设置界面UI修复测试失败")
    
    sys.exit(exit_code)

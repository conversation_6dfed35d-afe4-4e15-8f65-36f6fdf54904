#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统整体集成测试
"""

import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from trading.real_broker import create_broker
from ml.model_manager import ModelManager
from strategies.strategy_factory import StrategyFactory
from data.realtime_feed import create_realtime_feed
from backtesting.backtest_engine import BacktestEngine


def test_complete_trading_system():
    """测试完整的交易系统集成"""
    print("\n" + "="*60)
    print("🚀 完整交易系统集成测试")
    print("="*60)

    try:
        # 1. 测试策略工厂
        print("\n🎯 1. 测试策略工厂")
        strategy_factory = StrategyFactory()
        strategies_info = strategy_factory.get_available_strategies()
        print(f"✓ 可用策略: {len(strategies_info)} 种")

        # 创建测试策略
        ma_strategy = strategy_factory.create_strategy('MA', 'test_ma_strategy')
        if ma_strategy:
            print("✓ MA策略创建成功")

        # 2. 测试机器学习模块
        print("\n🤖 2. 测试机器学习模块")
        model_manager = ModelManager()

        # 创建测试模型
        model_name = "integration_test_model"
        if model_manager.create_model(model_name, 'random_forest_classifier'):
            print("✓ ML模型创建成功")

            # 创建测试数据
            np.random.seed(42)
            X = pd.DataFrame(
                np.random.randn(500, 5),
                columns=[f'feature_{i}' for i in range(5)]
            )
            y = pd.Series(np.random.choice([0, 1], 500))

            # 训练模型
            metrics = model_manager.train_model(model_name, X, y)
            if metrics:
                print(f"✓ 模型训练成功，准确率: {metrics.get('test_accuracy', 0):.3f}")

        # 3. 测试实时数据推送
        print("\n📡 3. 测试实时数据推送")
        feed_config = {'websocket_enabled': False}
        realtime_feed = create_realtime_feed('sina', feed_config)
        if realtime_feed:
            print("✓ 实时数据源创建成功")

            # 测试连接
            if realtime_feed.connect():
                print("✓ 实时数据源连接成功")
                realtime_feed.disconnect()
                print("✓ 实时数据源断开成功")

        # 4. 测试实盘交易接口
        print("\n💰 4. 测试实盘交易接口")
        broker_config = {
            'api_key': 'test_key',
            'secret_key': 'test_secret',
            'account_id': 'test_account',
            'sandbox': True
        }

        # 测试华泰券商
        huatai_broker = create_broker('huatai', broker_config)
        if huatai_broker and huatai_broker.connect():
            print("✓ 华泰券商连接成功")
            account_info = huatai_broker.get_account_info()
            if account_info:
                print(f"✓ 账户信息获取成功，总资产: {account_info.total_assets}")
            huatai_broker.disconnect()

        # 测试中信券商
        zhongxin_broker = create_broker('zhongxin', broker_config)
        if zhongxin_broker and zhongxin_broker.connect():
            print("✓ 中信券商连接成功")
            positions = zhongxin_broker.get_positions()
            print(f"✓ 持仓信息获取成功，持仓数量: {len(positions)}")
            zhongxin_broker.disconnect()

        # 5. 测试回测引擎
        print("\n📈 5. 测试回测引擎")
        backtest_engine = BacktestEngine()
        print("✓ 回测引擎创建成功")

        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        test_data = pd.DataFrame({
            'open': np.random.uniform(10, 20, len(dates)),
            'high': np.random.uniform(15, 25, len(dates)),
            'low': np.random.uniform(8, 15, len(dates)),
            'close': np.random.uniform(10, 20, len(dates)),
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)

        # 配置回测
        backtest_config = {
            'initial_capital': 100000,
            'commission': 0.001,
            'slippage': 0.001
        }

        if ma_strategy:
            # 运行回测
            try:
                results = backtest_engine.run_backtest(
                    strategy=ma_strategy,
                    data={'TEST.SH': test_data},
                    config=backtest_config
                )
                if results:
                    print("✓ 回测运行成功")
                    print(f"  - 总收益率: {results.get('total_return', 0):.2%}")
                    print(f"  - 最大回撤: {results.get('max_drawdown', 0):.2%}")
                    print(f"  - 夏普比率: {results.get('sharpe_ratio', 0):.3f}")
            except Exception as e:
                print(f"⚠️ 回测运行遇到问题: {e}")

        print("\n✅ 完整交易系统集成测试完成")
        return True

    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_optimization():
    """测试性能优化"""
    print("\n" + "="*60)
    print("⚡ 性能优化测试")
    print("="*60)

    try:
        # 1. 测试策略创建性能
        print("\n🎯 1. 策略创建性能测试")
        factory = StrategyFactory()

        start_time = time.time()
        strategies = []
        for i in range(10):
            strategy = factory.create_strategy('MA', f'perf_test_{i}')
            if strategy:
                strategies.append(strategy)

        creation_time = time.time() - start_time
        print(f"✓ 创建10个策略耗时: {creation_time:.3f}秒")
        print(f"✓ 平均每个策略: {creation_time/10:.3f}秒")

        # 2. 测试模型管理性能
        print("\n🤖 2. 模型管理性能测试")
        model_manager = ModelManager()

        start_time = time.time()
        models = []
        for i in range(5):
            model_name = f"perf_model_{i}"
            if model_manager.create_model(model_name, 'random_forest_classifier'):
                models.append(model_name)

        model_creation_time = time.time() - start_time
        print(f"✓ 创建5个模型耗时: {model_creation_time:.3f}秒")
        print(f"✓ 平均每个模型: {model_creation_time/5:.3f}秒")

        # 3. 测试数据处理性能
        print("\n📊 3. 数据处理性能测试")

        # 生成大量测试数据
        start_time = time.time()
        large_data = pd.DataFrame({
            'open': np.random.uniform(10, 20, 10000),
            'high': np.random.uniform(15, 25, 10000),
            'low': np.random.uniform(8, 15, 10000),
            'close': np.random.uniform(10, 20, 10000),
            'volume': np.random.randint(1000, 10000, 10000)
        })

        data_generation_time = time.time() - start_time
        print(f"✓ 生成10000条数据耗时: {data_generation_time:.3f}秒")

        # 测试技术指标计算性能
        start_time = time.time()

        # 计算移动平均线
        large_data['ma5'] = large_data['close'].rolling(5).mean()
        large_data['ma20'] = large_data['close'].rolling(20).mean()

        # 计算RSI
        delta = large_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        large_data['rsi'] = 100 - (100 / (1 + rs))

        indicator_time = time.time() - start_time
        print(f"✓ 计算技术指标耗时: {indicator_time:.3f}秒")

        print("\n✅ 性能优化测试完成")
        return True

    except Exception as e:
        print(f"❌ 性能优化测试失败: {e}")
        return False


def test_error_handling_robustness():
    """测试错误处理和系统健壮性"""
    print("\n" + "="*60)
    print("🛡️ 错误处理和健壮性测试")
    print("="*60)

    try:
        error_count = 0

        # 1. 测试无效参数处理
        print("\n🔧 1. 无效参数处理测试")

        # 测试策略工厂
        factory = StrategyFactory()
        invalid_strategy = factory.create_strategy('INVALID_STRATEGY', 'test')
        if invalid_strategy is None:
            print("✓ 无效策略类型正确处理")
        else:
            error_count += 1

        # 测试券商工厂
        invalid_broker = create_broker('invalid_broker', {})
        if invalid_broker is None:
            print("✓ 无效券商类型正确处理")
        else:
            error_count += 1

        # 测试数据源工厂
        invalid_feed = create_realtime_feed('invalid_feed')
        if invalid_feed is None:
            print("✓ 无效数据源类型正确处理")
        else:
            error_count += 1

        # 2. 测试异常数据处理
        print("\n📊 2. 异常数据处理测试")

        model_manager = ModelManager()
        model_name = "error_test_model"

        if model_manager.create_model(model_name, 'random_forest_classifier'):
            # 测试空数据
            empty_X = pd.DataFrame()
            empty_y = pd.Series(dtype=float)

            metrics = model_manager.train_model(model_name, empty_X, empty_y)
            if not metrics:  # 应该返回空字典或None
                print("✓ 空数据正确处理")
            else:
                error_count += 1

        # 3. 测试网络错误处理
        print("\n🌐 3. 网络错误处理测试")

        # 测试实时数据源的错误处理
        feed = create_realtime_feed('sina')
        if feed:
            error_received = False

            def on_error(error):
                nonlocal error_received
                error_received = True
                print(f"✓ 捕获网络错误: {error}")

            feed.add_callback('error', on_error)

            if feed.connect():
                # 订阅无效股票代码
                feed.subscribe(['INVALID.CODE'])
                time.sleep(2)  # 等待错误发生
                feed.disconnect()

                if error_received:
                    print("✓ 网络错误正确处理")
                else:
                    print("⚠️ 未检测到网络错误（可能是正常情况）")

        print(f"\n✅ 错误处理测试完成，发现 {error_count} 个问题")
        return error_count == 0

    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始系统整体优化和集成测试")

    test_results = []

    # 完整系统集成测试
    test_results.append(test_complete_trading_system())

    # 性能优化测试
    test_results.append(test_performance_optimization())

    # 错误处理和健壮性测试
    test_results.append(test_error_handling_robustness())

    # 汇总测试结果
    print("\n" + "="*70)
    print("📊 系统整体测试结果汇总")
    print("="*70)

    passed_tests = sum(test_results)
    total_tests = len(test_results)

    print(f"✅ 通过测试: {passed_tests}/{total_tests}")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统整体优化成功！")
        print("\n🏆 股票分析工具开发完成！")
        print("✨ 所有核心功能已100%实现并通过测试！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()

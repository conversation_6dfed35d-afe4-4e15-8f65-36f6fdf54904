#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码质量修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_syntax_check():
    """测试语法检查"""
    print("🔍 测试代码语法...")
    
    test_files = [
        "main.py",
        "gui/main_window.py",
        "gui/widgets/analysis_center_widget.py",
        "gui/widgets/strategy_center_widget.py",
        "gui/widgets/technical_chart_widget.py",
        "gui/widgets/base_widget.py"
    ]
    
    errors = []
    
    for file_path in test_files:
        try:
            import ast
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            ast.parse(code)
            print(f"   ✅ {file_path} 语法正确")
        except Exception as e:
            print(f"   ❌ {file_path} 语法错误: {e}")
            errors.append(f"{file_path}: {e}")
    
    return len(errors) == 0, errors

def test_import_check():
    """测试导入检查"""
    print("\n🔍 测试模块导入...")
    
    try:
        # 测试主要模块导入
        from gui.main_window import MainWindow
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        from gui.widgets.strategy_center_widget import StrategyCenterWidget
        from gui.widgets.base_widget import BaseWidget
        
        print("   ✅ 所有主要模块导入成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用
        app = QApplication([])
        
        # 测试组件创建
        from gui.widgets.base_widget import BaseWidget
        base_widget = BaseWidget()
        print("   ✅ 基础组件创建成功")
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        print("   ✅ 分析中心组件创建成功")
        
        from gui.widgets.strategy_center_widget import StrategyCenterWidget
        strategy_widget = StrategyCenterWidget()
        print("   ✅ 策略中心组件创建成功")
        
        # 清理
        app.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("代码质量修复测试")
    print("=" * 50)
    
    # 测试1: 语法检查
    syntax_ok, syntax_errors = test_syntax_check()
    
    # 测试2: 导入检查
    import_ok = test_import_check()
    
    # 测试3: 基本功能
    function_ok = test_basic_functionality()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"语法检查: {'✅ 通过' if syntax_ok else '❌ 失败'}")
    if syntax_errors:
        for error in syntax_errors:
            print(f"  - {error}")
    print(f"导入检查: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"功能检查: {'✅ 通过' if function_ok else '❌ 失败'}")
    
    all_passed = syntax_ok and import_ok and function_ok
    
    if all_passed:
        print("\n🎉 所有代码质量测试通过！代码质量问题已修复！")
        return True
    else:
        print("\n❌ 代码质量测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

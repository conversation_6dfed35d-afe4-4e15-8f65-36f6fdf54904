#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特定窗口背景颜色问题
专门修复"最近交易"、"数据源状态"、"数据源管理"窗口的白色文字配白色背景问题
"""

from pathlib import Path

def fix_dashboard_widget():
    """修复仪表盘组件中的最近交易面板"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "dashboard_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复最近交易表格的背景颜色
        old_code = '''        # 设置表格属性
        self.trades_table.setAlternatingRowColors(True)
        self.trades_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.trades_table.horizontalHeader().setStretchLastSection(True)'''
        
        new_code = '''        # 设置表格属性
        self.trades_table.setAlternatingRowColors(True)
        self.trades_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.trades_table.horizontalHeader().setStretchLastSection(True)
        
        # 设置表格背景为蓝色，确保白色文字可见
        self.trades_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)'''
        
        content = content.replace(old_code, new_code)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 dashboard_widget.py 中的最近交易面板")
        return True
        
    except Exception as e:
        print(f"❌ 修复 dashboard_widget.py 失败: {e}")
        return False

def fix_data_dashboard_widget():
    """修复数据仪表盘组件中的数据源状态"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "data_dashboard_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复数据源状态表格的背景颜色
        old_code = '''        self.status_table.setColumnCount(3)
        self.status_table.setHorizontalHeaderLabels(["数据源", "状态", "延迟(ms)"])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        self.status_table.setAlternatingRowColors(True)'''
        
        new_code = '''        self.status_table.setColumnCount(3)
        self.status_table.setHorizontalHeaderLabels(["数据源", "状态", "延迟(ms)"])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        self.status_table.setAlternatingRowColors(True)
        
        # 设置表格背景为蓝色，确保白色文字可见
        self.status_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)'''
        
        content = content.replace(old_code, new_code)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 data_dashboard_widget.py 中的数据源状态表格")
        return True
        
    except Exception as e:
        print(f"❌ 修复 data_dashboard_widget.py 失败: {e}")
        return False

def fix_data_source_manager_widget():
    """修复数据源管理组件"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "data_source_manager_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复数据源状态表格的背景颜色
        old_code = '''        # 设置列宽
        header = self.status_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

        layout.addWidget(self.status_table)'''
        
        new_code = '''        # 设置列宽
        header = self.status_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置表格背景为蓝色，确保白色文字可见
        self.status_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.status_table)'''
        
        content = content.replace(old_code, new_code)
        
        # 修复标题标签
        old_title = '''        # 标题
        title_label = QLabel("数据源管理中心")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)'''
        
        new_title = '''        # 标题
        title_label = QLabel("数据源管理中心")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(title_label)'''
        
        content = content.replace(old_title, new_title)
        
        # 修复备用数据源标签
        old_fallback = '''        fallback_info = QLabel("备用数据源列表（按优先级排序）:")
        fallback_layout.addWidget(fallback_info)'''
        
        new_fallback = '''        fallback_info = QLabel("备用数据源列表（按优先级排序）:")
        fallback_info.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        fallback_layout.addWidget(fallback_info)'''
        
        content = content.replace(old_fallback, new_fallback)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 data_source_manager_widget.py 中的数据源管理界面")
        return True
        
    except Exception as e:
        print(f"❌ 修复 data_source_manager_widget.py 失败: {e}")
        return False

def fix_trading_center_widget():
    """修复交易中心组件中的表格"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "trading_center_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复实时行情表格
        old_quote = '''        # 添加示例行情数据
        self.add_sample_quotes()

        quote_layout.addWidget(self.quote_table)'''
        
        new_quote = '''        # 添加示例行情数据
        self.add_sample_quotes()
        
        # 设置行情表格背景为蓝色
        self.quote_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        quote_layout.addWidget(self.quote_table)'''
        
        content = content.replace(old_quote, new_quote)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 trading_center_widget.py 中的交易表格")
        return True
        
    except Exception as e:
        print(f"❌ 修复 trading_center_widget.py 失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复特定窗口背景颜色问题")
    print("将白色背景改为蓝色，确保白色文字清晰可见")
    print("=" * 60)
    
    success_count = 0
    total_count = 4
    
    # 1. 修复仪表盘中的最近交易面板
    if fix_dashboard_widget():
        success_count += 1
    
    # 2. 修复数据仪表盘中的数据源状态
    if fix_data_dashboard_widget():
        success_count += 1
    
    # 3. 修复数据源管理组件
    if fix_data_source_manager_widget():
        success_count += 1
    
    # 4. 修复交易中心组件
    if fix_trading_center_widget():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"修复完成! 成功修复 {success_count}/{total_count} 个组件")
    print("=" * 60)
    
    if success_count == total_count:
        print("🎉 所有窗口背景颜色修复成功！")
        print("现在所有白色文字都应该在蓝色背景上清晰可见。")
    else:
        print("⚠️  部分修复失败，请检查错误信息。")

if __name__ == "__main__":
    main()

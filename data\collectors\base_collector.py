#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集器基类
"""

import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

from utils.logger import get_logger
from utils.constants import PERIODS


class BaseCollector(ABC):
    """数据采集器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"Collector.{name}")
        self.is_connected = False
        self.last_update = None
        
    @abstractmethod
    def connect(self) -> bool:
        """连接数据源"""
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def get_stock_list(self, market: str = None) -> pd.DataFrame:
        """获取股票列表"""
        pass
    
    @abstractmethod
    def get_stock_data(self, 
                      symbol: str, 
                      start_date: str = None, 
                      end_date: str = None,
                      period: str = "daily") -> pd.DataFrame:
        """获取股票历史数据"""
        pass
    
    @abstractmethod
    def get_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据质量"""
        if data is None or data.empty:
            self.logger.warning("数据为空")
            return False
        
        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            self.logger.warning(f"缺少必要的列: {missing_columns}")
            return False
        
        # 检查数据类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in data.columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    self.logger.warning(f"列 {col} 不是数值类型")
                    return False
        
        # 检查异常值
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                if (data[col] <= 0).any():
                    self.logger.warning(f"列 {col} 包含非正数值")
                    return False
        
        # 检查高开低收的逻辑关系
        if all(col in data.columns for col in price_columns):
            invalid_rows = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close'])
            )
            
            if invalid_rows.any():
                self.logger.warning(f"发现 {invalid_rows.sum()} 行价格逻辑错误")
                return False
        
        self.logger.info(f"数据验证通过，共 {len(data)} 行数据")
        return True
    
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理数据"""
        if data is None or data.empty:
            return data
        
        # 删除重复行
        original_len = len(data)
        data = data.drop_duplicates()
        if len(data) < original_len:
            self.logger.info(f"删除了 {original_len - len(data)} 行重复数据")
        
        # 处理缺失值
        if data.isnull().any().any():
            self.logger.info("发现缺失值，使用前向填充")
            data = data.fillna(method='ffill')
        
        # 排序
        if 'date' in data.columns:
            data = data.sort_values('date')
        elif data.index.name == 'date' or isinstance(data.index, pd.DatetimeIndex):
            data = data.sort_index()
        
        return data
    
    def format_symbol(self, symbol: str) -> str:
        """格式化股票代码"""
        # 移除空格
        symbol = symbol.strip()
        
        # 转换为大写
        symbol = symbol.upper()
        
        # 添加市场后缀（如果没有）
        if '.' not in symbol:
            if symbol.startswith('0') or symbol.startswith('3'):
                symbol += '.SZ'  # 深圳
            elif symbol.startswith('6'):
                symbol += '.SH'  # 上海
            elif symbol.startswith('8') or symbol.startswith('4'):
                symbol += '.BJ'  # 北京
        
        return symbol
    
    def retry_request(self, func, max_retries: int = 3, delay: float = 1.0):
        """重试机制"""
        for attempt in range(max_retries):
            try:
                return func()
            except Exception as e:
                self.logger.warning(f"第 {attempt + 1} 次尝试失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(delay * (2 ** attempt))  # 指数退避
                else:
                    raise e
    
    def get_trading_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日期列表"""
        # 这里可以实现更复杂的交易日历逻辑
        # 暂时返回所有工作日
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        dates = pd.bdate_range(start=start, end=end)
        return [date.strftime('%Y-%m-%d') for date in dates]
    
    def check_connection(self) -> bool:
        """检查连接状态"""
        return self.is_connected
    
    def get_status(self) -> Dict[str, Any]:
        """获取采集器状态"""
        return {
            "name": self.name,
            "connected": self.is_connected,
            "last_update": self.last_update,
            "status": "正常" if self.is_connected else "断开"
        }

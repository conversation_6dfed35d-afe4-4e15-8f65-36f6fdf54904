#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的分析中心功能
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.widgets.analysis_center_widget import AnalysisCenterWidget
from utils.logger import get_logger


class TestAnalysisWindow(QMainWindow):
    """测试分析中心的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("TestAnalysisWindow")
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("增强分析中心测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建分析中心组件
        self.analysis_center = AnalysisCenterWidget()
        layout.addWidget(self.analysis_center)
        
        self.logger.info("测试窗口初始化完成")


def test_analysis_center():
    """测试分析中心功能"""
    print("=" * 60)
    print("增强分析中心功能测试")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("分析中心测试")
        
        # 设置应用程序样式
        app.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #606060;
            }
            QComboBox, QLineEdit, QDateEdit {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
                border-radius: 3px;
            }
            QTableWidget {
                background-color: #353535;
                alternate-background-color: #404040;
                gridline-color: #555555;
            }
            QHeaderView::section {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
            }
            QTabBar::tab {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 5px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #505050;
            }
        """)
        
        # 创建测试窗口
        window = TestAnalysisWindow()
        window.show()
        
        print("✅ 分析中心界面创建成功！")
        print("\n测试说明:")
        print("1. 在技术分析标签页中输入股票代码（如：000001.SZ）")
        print("2. 点击'开始分析'按钮查看技术分析图表")
        print("3. 可以切换不同的图表类型和指标")
        print("4. 查看技术指标数值表格和交易信号")
        print("5. 可以导出图表为图片文件")
        print("\n功能特点:")
        print("- 集成了49个技术指标")
        print("- 支持K线图、分时图、技术指标组合图等")
        print("- 实时计算交易信号")
        print("- 支持图表导出")
        print("- 美观的深色主题界面")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """主函数"""
    try:
        # 检查必要的依赖
        required_packages = ['matplotlib', 'mplfinance']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print("缺少以下依赖包:")
            for package in missing_packages:
                print(f"  - {package}")
            print("\n请运行以下命令安装依赖:")
            print("pip install matplotlib mplfinance")
            return 1
        
        # 运行测试
        exit_code = test_analysis_center()
        
        if exit_code == 0:
            print("\n✅ 增强分析中心测试完成！")
        else:
            print("\n❌ 测试过程中出现问题")
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 0
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

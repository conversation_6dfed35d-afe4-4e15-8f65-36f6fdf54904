#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略工厂
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Type
import importlib
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from strategies.technical.ma_strategy import MovingAverageStrategy, DoubleMovingAverageStrategy
from strategies.technical.macd_strategy import MACDStrategy
from strategies.technical.rsi_strategy import RSIStrategy
from strategies.technical.bollinger_strategy import BollingerBandsStrategy
from strategies.technical.advanced_strategies import (
    KDJStrategy, BollingerBandsStrategy as AdvancedBollingerStrategy,
    MeanReversionStrategy, BreakoutStrategy, TrendFollowingStrategy
)
from strategies.ml.simple_ml_strategy import SimpleMLStrategy
from utils.logger import get_logger


class StrategyFactory:
    """策略工厂类"""

    def __init__(self):
        self.logger = get_logger("StrategyFactory")
        self._strategies = {}
        self._register_default_strategies()

    def _register_default_strategies(self):
        """注册默认策略"""
        # 基础策略
        self.register_strategy("MA", MovingAverageStrategy)
        self.register_strategy("DoubleMA", DoubleMovingAverageStrategy)
        self.register_strategy("MACD", MACDStrategy)
        self.register_strategy("RSI", RSIStrategy)
        self.register_strategy("Bollinger", BollingerBandsStrategy)

        # 高级技术策略
        self.register_strategy("KDJ", KDJStrategy)
        self.register_strategy("AdvancedBollinger", AdvancedBollingerStrategy)
        self.register_strategy("MeanReversion", MeanReversionStrategy)
        self.register_strategy("Breakout", BreakoutStrategy)
        self.register_strategy("TrendFollowing", TrendFollowingStrategy)

        # 机器学习策略
        self.register_strategy("SimpleML", SimpleMLStrategy)

    def register_strategy(self, name: str, strategy_class: Type[BaseStrategy]):
        """注册策略"""
        if not issubclass(strategy_class, BaseStrategy):
            raise ValueError(f"策略类 {strategy_class} 必须继承自 BaseStrategy")

        self._strategies[name] = strategy_class
        self.logger.info(f"注册策略: {name}")

    def create_strategy(self, strategy_type: str, name: str = None, config: Dict[str, Any] = None) -> Optional[BaseStrategy]:
        """创建策略实例"""
        try:
            if strategy_type not in self._strategies:
                self.logger.error(f"未知的策略类型: {strategy_type}")
                return None

            strategy_class = self._strategies[strategy_type]

            # 使用提供的名称或默认名称
            if name is None:
                name = f"{strategy_type}策略_{len(self._strategies)}"

            # 创建策略实例
            if strategy_type in ["MA", "DoubleMA"]:
                # MA策略使用parameters参数
                strategy = strategy_class(name=name, parameters=config)
            elif strategy_type in ["KDJ", "AdvancedBollinger", "MeanReversion", "Breakout", "TrendFollowing"]:
                # 高级策略使用关键字参数
                if config:
                    strategy = strategy_class(**config)
                else:
                    strategy = strategy_class()
                strategy.name = name
            else:
                # 其他策略使用config参数
                strategy = strategy_class(name=name, config=config)

            self.logger.info(f"创建策略成功: {name} ({strategy_type})")
            return strategy

        except Exception as e:
            self.logger.error(f"创建策略失败: {e}")
            return None

    def get_available_strategies(self) -> Dict[str, Dict[str, Any]]:
        """获取可用策略列表"""
        strategies_info = {}

        for strategy_type, strategy_class in self._strategies.items():
            try:
                # 创建临时实例获取信息，使用默认参数
                if strategy_type in ["MA", "DoubleMA"]:
                    # MA策略需要特殊处理
                    temp_strategy = strategy_class(name=f"temp_{strategy_type}", parameters={})
                elif strategy_type in ["KDJ", "AdvancedBollinger", "MeanReversion", "Breakout", "TrendFollowing"]:
                    # 高级策略使用默认构造函数
                    temp_strategy = strategy_class()
                    temp_strategy.name = f"temp_{strategy_type}"
                else:
                    # 其他策略使用默认构造
                    temp_strategy = strategy_class(name=f"temp_{strategy_type}", config={})

                # 安全获取策略信息
                if hasattr(temp_strategy, 'get_strategy_info'):
                    info = temp_strategy.get_strategy_info()
                else:
                    info = {}

                strategies_info[strategy_type] = {
                    'name': info.get('name', strategy_type),
                    'description': info.get('description', f'{strategy_type}策略'),
                    'strategy_type': info.get('strategy_type', strategy_type),
                    'indicators': info.get('indicators', []),
                    'parameters': getattr(temp_strategy, 'parameters', {}),
                    'signal_conditions': info.get('signal_conditions', {}),
                    'risk_management': info.get('risk_management', {})
                }

            except Exception as e:
                self.logger.error(f"获取策略信息失败 {strategy_type}: {e}")
                strategies_info[strategy_type] = {
                    'name': strategy_type,
                    'description': '策略信息获取失败',
                    'error': str(e),
                    'parameters': {},
                    'indicators': [],
                    'signal_conditions': {},
                    'risk_management': {}
                }

        return strategies_info

    def get_strategy_parameters(self, strategy_type: str) -> Dict[str, Any]:
        """获取策略参数"""
        if strategy_type not in self._strategies:
            return {}

        try:
            strategy_class = self._strategies[strategy_type]
            temp_strategy = strategy_class()
            return temp_strategy.parameters
        except Exception as e:
            self.logger.error(f"获取策略参数失败 {strategy_type}: {e}")
            return {}

    def validate_strategy_config(self, strategy_type: str, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证策略配置"""
        try:
            if strategy_type not in self._strategies:
                return False, f"未知的策略类型: {strategy_type}"

            # 获取默认参数
            default_params = self.get_strategy_parameters(strategy_type)

            # 检查配置参数
            for key, value in config.items():
                if key not in default_params:
                    return False, f"未知的参数: {key}"

                # 类型检查
                default_value = default_params[key]
                if type(value) != type(default_value):
                    return False, f"参数 {key} 类型错误，期望 {type(default_value).__name__}，得到 {type(value).__name__}"

            # 尝试创建策略实例验证
            strategy = self.create_strategy(strategy_type, "test", config)
            if strategy is None:
                return False, "策略创建失败"

            return True, "配置验证通过"

        except Exception as e:
            return False, f"配置验证失败: {str(e)}"

    def load_custom_strategy(self, module_path: str, class_name: str, strategy_name: str) -> bool:
        """加载自定义策略"""
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location("custom_strategy", module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 获取策略类
            strategy_class = getattr(module, class_name)

            # 注册策略
            self.register_strategy(strategy_name, strategy_class)

            self.logger.info(f"加载自定义策略成功: {strategy_name}")
            return True

        except Exception as e:
            self.logger.error(f"加载自定义策略失败: {e}")
            return False

    def create_strategy_combination(self, strategies_config: List[Dict[str, Any]],
                                  combination_name: str = "组合策略") -> Optional['StrategyCombination']:
        """创建策略组合"""
        try:
            strategies = []

            for config in strategies_config:
                strategy_type = config.get('type')
                strategy_name = config.get('name')
                strategy_params = config.get('parameters', {})
                weight = config.get('weight', 1.0)

                strategy = self.create_strategy(strategy_type, strategy_name, strategy_params)
                if strategy:
                    strategies.append({
                        'strategy': strategy,
                        'weight': weight
                    })

            if not strategies:
                self.logger.error("没有有效的策略可以组合")
                return None

            combination = StrategyCombination(combination_name, strategies)
            self.logger.info(f"创建策略组合成功: {combination_name}，包含{len(strategies)}个策略")
            return combination

        except Exception as e:
            self.logger.error(f"创建策略组合失败: {e}")
            return None


class StrategyCombination(BaseStrategy):
    """策略组合类"""

    def __init__(self, name: str, strategies: List[Dict[str, Any]]):
        super().__init__(name)
        self.strategies = strategies
        self.total_weight = sum(s['weight'] for s in strategies)

        # 归一化权重
        for strategy_info in self.strategies:
            strategy_info['normalized_weight'] = strategy_info['weight'] / self.total_weight

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成组合信号"""
        all_signals = []

        for strategy_info in self.strategies:
            strategy = strategy_info['strategy']
            weight = strategy_info['normalized_weight']

            try:
                signals = strategy.generate_signals(data)

                # 调整信号权重
                for signal in signals:
                    signal['weight'] = weight
                    signal['source_strategy'] = strategy.name
                    all_signals.append(signal)

            except Exception as e:
                self.logger.error(f"策略 {strategy.name} 生成信号失败: {e}")

        # 合并同向信号
        merged_signals = self._merge_signals(all_signals)

        return merged_signals

    def _merge_signals(self, signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并信号"""
        if not signals:
            return []

        # 按方向分组
        buy_signals = [s for s in signals if s['direction'] == 'buy']
        sell_signals = [s for s in signals if s['direction'] == 'sell']

        merged_signals = []

        # 合并买入信号
        if buy_signals:
            total_weight = sum(s['weight'] for s in buy_signals)
            avg_confidence = sum(s['confidence'] * s['weight'] for s in buy_signals) / total_weight

            merged_signal = buy_signals[0].copy()
            merged_signal.update({
                'signal_type': 'COMBINATION',
                'confidence': avg_confidence,
                'weight': total_weight,
                'source_strategies': [s['source_strategy'] for s in buy_signals],
                'reason': f"组合信号：{len(buy_signals)}个策略买入"
            })
            merged_signals.append(merged_signal)

        # 合并卖出信号
        if sell_signals:
            total_weight = sum(s['weight'] for s in sell_signals)
            avg_confidence = sum(s['confidence'] * s['weight'] for s in sell_signals) / total_weight

            merged_signal = sell_signals[0].copy()
            merged_signal.update({
                'signal_type': 'COMBINATION',
                'confidence': avg_confidence,
                'weight': total_weight,
                'source_strategies': [s['source_strategy'] for s in sell_signals],
                'reason': f"组合信号：{len(sell_signals)}个策略卖出"
            })
            merged_signals.append(merged_signal)

        return merged_signals

    def calculate_position_size(self, signal: Dict[str, Any], current_price: float,
                               available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于权重的仓位计算
            base_position = available_capital * 0.1  # 基础仓位10%
            weighted_position = base_position * signal.get('weight', 1.0)
            quantity = int(weighted_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略组合信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '策略组合',
            'sub_strategies': [
                {
                    'name': s['strategy'].name,
                    'weight': s['weight'],
                    'type': s['strategy'].__class__.__name__
                }
                for s in self.strategies
            ],
            'total_strategies': len(self.strategies)
        })
        return info


# 全局策略工厂实例
strategy_factory = StrategyFactory()

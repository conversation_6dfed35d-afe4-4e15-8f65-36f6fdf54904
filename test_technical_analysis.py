#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试技术分析功能
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_technical_indicators_display():
    """测试技术指标显示"""
    print("🔍 测试技术指标显示...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 模拟技术分析操作
        print("   ✅ 分析中心组件创建成功")
        
        # 检查技术分析标签页
        if hasattr(analysis_widget, 'tab_widget'):
            for i in range(analysis_widget.tab_widget.count()):
                tab_name = analysis_widget.tab_widget.tabText(i)
                print(f"   ✅ 发现标签页: {tab_name}")
                
                if "技术分析" in tab_name:
                    analysis_widget.tab_widget.setCurrentIndex(i)
                    print("   ✅ 切换到技术分析标签页成功")
                    
                    # 处理事件
                    app.processEvents()
                    
                    # 测试技术分析功能
                    if hasattr(analysis_widget, 'perform_technical_analysis'):
                        print("   ✅ 技术分析方法存在")
                    else:
                        print("   ⚠️ 技术分析方法不存在")
                    
                    break
        
        # 测试技术指标计算
        if analysis_widget.ti:
            print("   ✅ 技术指标模块可用")
            
            # 创建测试数据
            import pandas as pd
            import numpy as np
            
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            np.random.seed(42)
            prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
            
            test_data = pd.DataFrame({
                'open': prices + np.random.randn(100) * 0.1,
                'high': prices + np.abs(np.random.randn(100) * 0.2),
                'low': prices - np.abs(np.random.randn(100) * 0.2),
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, 100)
            }, index=dates)
            
            # 测试指标计算
            indicators = analysis_widget.ti.calculate_all_indicators(test_data)
            print(f"   ✅ 计算了 {len(indicators.columns) - len(test_data.columns)} 个技术指标")
            
            # 检查关键指标
            key_indicators = ['sma_5', 'sma_20', 'rsi_12', 'macd', 'kdj_k']
            for indicator in key_indicators:
                if indicator in indicators.columns:
                    print(f"   ✅ {indicator} 指标计算成功")
                else:
                    print(f"   ❌ {indicator} 指标缺失")
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术指标显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_technical_analysis_interface():
    """测试技术分析界面"""
    print("\n🔍 测试技术分析界面...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 检查技术分析界面元素
        print("   ✅ 分析界面创建成功")
        
        # 查找技术分析相关的输入控件
        stock_input_found = False
        analyze_button_found = False
        
        # 递归查找子控件
        def find_widgets(widget, widget_type, name_pattern=None):
            found = []
            for child in widget.findChildren(widget_type):
                if name_pattern is None or name_pattern.lower() in child.objectName().lower():
                    found.append(child)
            return found
        
        from PyQt5.QtWidgets import QLineEdit, QPushButton, QTableWidget
        
        # 查找输入框
        line_edits = find_widgets(analysis_widget, QLineEdit)
        if line_edits:
            print(f"   ✅ 发现 {len(line_edits)} 个输入框")
            stock_input_found = True
        
        # 查找按钮
        buttons = find_widgets(analysis_widget, QPushButton)
        if buttons:
            print(f"   ✅ 发现 {len(buttons)} 个按钮")
            for button in buttons:
                if "分析" in button.text():
                    analyze_button_found = True
                    print(f"   ✅ 发现分析按钮: {button.text()}")
        
        # 查找表格
        tables = find_widgets(analysis_widget, QTableWidget)
        if tables:
            print(f"   ✅ 发现 {len(tables)} 个数据表格")
        
        # 处理事件
        app.processEvents()
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术分析界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("技术分析功能测试")
    print("=" * 50)
    
    # 测试1: 技术指标显示
    indicators_ok = test_technical_indicators_display()
    
    # 测试2: 技术分析界面
    interface_ok = test_technical_analysis_interface()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"技术指标显示: {'✅ 通过' if indicators_ok else '❌ 失败'}")
    print(f"技术分析界面: {'✅ 通过' if interface_ok else '❌ 失败'}")
    
    all_passed = indicators_ok and interface_ok
    
    if all_passed:
        print("\n🎉 技术分析功能测试通过！")
        return True
    else:
        print("\n❌ 技术分析功能测试失败，需要进一步完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

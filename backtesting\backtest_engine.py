#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测引擎
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import copy

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from utils.logger import get_logger


class BacktestEngine:
    """回测引擎"""

    def __init__(self, initial_capital: float = 1000000, commission_rate: float = 0.0003):
        self.logger = get_logger("BacktestEngine")
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate

        # 回测状态
        self.current_capital = initial_capital
        self.positions = {}  # symbol -> {'quantity': int, 'avg_price': float, 'market_value': float}
        self.trades = []  # 交易记录
        self.portfolio_values = []  # 组合价值历史
        self.signals = []  # 信号记录

        # 回测配置
        self.start_date = None
        self.end_date = None
        self.data = {}  # symbol -> DataFrame
        self.strategies = []

        # 性能指标
        self.performance_metrics = {}

    def add_data(self, symbol: str, data: pd.DataFrame):
        """添加回测数据"""
        try:
            if data is None or data.empty:
                self.logger.error(f"数据为空: {symbol}")
                return False

            # 验证数据格式
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in data.columns for col in required_columns):
                self.logger.error(f"数据格式错误，缺少必要列: {symbol}")
                return False

            # 确保数据按日期排序
            data = data.sort_index()

            # 设置symbol属性
            data.attrs['symbol'] = symbol

            self.data[symbol] = data
            self.logger.info(f"添加回测数据成功: {symbol}, 数据量: {len(data)}")
            return True

        except Exception as e:
            self.logger.error(f"添加回测数据失败: {e}")
            return False

    def add_strategy(self, strategy: BaseStrategy):
        """添加策略"""
        try:
            if not isinstance(strategy, BaseStrategy):
                self.logger.error("策略必须继承自BaseStrategy")
                return False

            self.strategies.append(strategy)
            self.logger.info(f"添加策略成功: {strategy.name}")
            return True

        except Exception as e:
            self.logger.error(f"添加策略失败: {e}")
            return False

    def set_period(self, start_date: str, end_date: str):
        """设置回测周期"""
        try:
            self.start_date = pd.to_datetime(start_date)
            self.end_date = pd.to_datetime(end_date)
            self.logger.info(f"设置回测周期: {start_date} 至 {end_date}")

        except Exception as e:
            self.logger.error(f"设置回测周期失败: {e}")

    def run_backtest(self) -> Dict[str, Any]:
        """运行回测"""
        try:
            if not self.data:
                self.logger.error("没有回测数据")
                return {}

            if not self.strategies:
                self.logger.error("没有回测策略")
                return {}

            self.logger.info("开始回测...")

            # 重置回测状态
            self._reset_backtest_state()

            # 获取所有交易日期
            all_dates = self._get_trading_dates()

            if not all_dates:
                self.logger.error("没有有效的交易日期")
                return {}

            # 逐日回测
            for current_date in all_dates:
                self._process_trading_day(current_date)

            # 计算性能指标
            self.performance_metrics = self._calculate_performance_metrics()

            self.logger.info("回测完成")
            return self.performance_metrics

        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            return {}

    def _reset_backtest_state(self):
        """重置回测状态"""
        self.current_capital = self.initial_capital
        self.positions = {}
        self.trades = []
        self.portfolio_values = []
        self.signals = []

        # 重置策略状态
        for strategy in self.strategies:
            strategy.positions = {}
            strategy.trades = []
            strategy.signals = []

    def _get_trading_dates(self) -> List[pd.Timestamp]:
        """获取所有交易日期"""
        try:
            all_dates = set()

            for symbol, data in self.data.items():
                # 筛选日期范围
                if self.start_date and self.end_date:
                    mask = (data.index >= self.start_date) & (data.index <= self.end_date)
                    filtered_data = data[mask]
                else:
                    filtered_data = data

                all_dates.update(filtered_data.index)

            # 排序并返回
            return sorted(list(all_dates))

        except Exception as e:
            self.logger.error(f"获取交易日期失败: {e}")
            return []

    def _process_trading_day(self, current_date: pd.Timestamp):
        """处理单个交易日"""
        try:
            # 更新持仓市值
            self._update_positions_value(current_date)

            # 生成交易信号
            daily_signals = self._generate_signals(current_date)

            # 执行交易
            for signal in daily_signals:
                self._execute_signal(signal, current_date)

            # 记录组合价值
            total_value = self._calculate_total_portfolio_value(current_date)
            self.portfolio_values.append({
                'date': current_date,
                'total_value': total_value,
                'cash': self.current_capital,
                'positions_value': total_value - self.current_capital
            })

        except Exception as e:
            self.logger.error(f"处理交易日失败 {current_date}: {e}")

    def _update_positions_value(self, current_date: pd.Timestamp):
        """更新持仓市值"""
        try:
            for symbol in list(self.positions.keys()):
                if symbol in self.data and current_date in self.data[symbol].index:
                    current_price = self.data[symbol].loc[current_date, 'close']
                    self.positions[symbol]['market_value'] = (
                        self.positions[symbol]['quantity'] * current_price
                    )

        except Exception as e:
            self.logger.error(f"更新持仓市值失败: {e}")

    def _generate_signals(self, current_date: pd.Timestamp) -> List[Dict[str, Any]]:
        """生成交易信号"""
        all_signals = []

        try:
            for strategy in self.strategies:
                for symbol, data in self.data.items():
                    # 获取到当前日期的历史数据
                    historical_data = data[data.index <= current_date].copy()

                    if len(historical_data) < 20:  # 需要足够的历史数据
                        continue

                    # 设置symbol属性
                    historical_data.attrs['symbol'] = symbol

                    # 生成信号
                    signals = strategy.generate_signals(historical_data)

                    # 筛选当日信号
                    for signal in signals:
                        signal_date = pd.to_datetime(signal['timestamp'])
                        if signal_date.date() == current_date.date():
                            signal['strategy'] = strategy.name
                            all_signals.append(signal)
                            self.signals.append(signal)

            return all_signals

        except Exception as e:
            self.logger.error(f"生成信号失败: {e}")
            return []

    def _execute_signal(self, signal: Dict[str, Any], current_date: pd.Timestamp):
        """执行交易信号"""
        try:
            symbol = signal['symbol']
            direction = signal['direction']
            quantity = signal.get('quantity', 0)
            price = signal.get('price', 0)

            if quantity <= 0 or price <= 0:
                return

            # 获取当前价格（使用开盘价模拟次日执行）
            if symbol not in self.data:
                return

            # 寻找下一个交易日的开盘价
            next_date = self._get_next_trading_date(symbol, current_date)
            if next_date is None:
                return

            execution_price = self.data[symbol].loc[next_date, 'open']

            if direction == 'buy':
                self._execute_buy_order(symbol, quantity, execution_price, next_date, signal)
            elif direction == 'sell':
                self._execute_sell_order(symbol, quantity, execution_price, next_date, signal)

        except Exception as e:
            self.logger.error(f"执行信号失败: {e}")

    def _get_next_trading_date(self, symbol: str, current_date: pd.Timestamp) -> Optional[pd.Timestamp]:
        """获取下一个交易日"""
        try:
            data = self.data[symbol]
            future_dates = data[data.index > current_date].index

            if len(future_dates) > 0:
                return future_dates[0]

            return None

        except Exception:
            return None

    def _execute_buy_order(self, symbol: str, quantity: int, price: float,
                          execution_date: pd.Timestamp, signal: Dict[str, Any]):
        """执行买入订单"""
        try:
            # 计算交易成本
            trade_value = quantity * price
            commission = trade_value * self.commission_rate
            total_cost = trade_value + commission

            # 检查资金是否充足
            if total_cost > self.current_capital:
                # 调整数量以适应可用资金
                available_value = self.current_capital * 0.95  # 保留5%缓冲
                adjusted_quantity = int(available_value / (price * (1 + self.commission_rate)) / 100) * 100

                if adjusted_quantity < 100:
                    return  # 资金不足

                quantity = adjusted_quantity
                trade_value = quantity * price
                commission = trade_value * self.commission_rate
                total_cost = trade_value + commission

            # 更新持仓
            if symbol in self.positions:
                # 计算新的平均成本
                old_quantity = self.positions[symbol]['quantity']
                old_cost = old_quantity * self.positions[symbol]['avg_price']
                new_avg_price = (old_cost + trade_value) / (old_quantity + quantity)

                self.positions[symbol]['quantity'] += quantity
                self.positions[symbol]['avg_price'] = new_avg_price
            else:
                self.positions[symbol] = {
                    'quantity': quantity,
                    'avg_price': price,
                    'market_value': trade_value
                }

            # 更新现金
            self.current_capital -= total_cost

            # 记录交易
            trade = {
                'date': execution_date,
                'symbol': symbol,
                'direction': 'buy',
                'quantity': quantity,
                'price': price,
                'value': trade_value,
                'commission': commission,
                'strategy': signal.get('strategy', 'Unknown'),
                'signal_reason': signal.get('reason', ''),
                'confidence': signal.get('confidence', 0)
            }

            self.trades.append(trade)

        except Exception as e:
            self.logger.error(f"执行买入订单失败: {e}")

    def _execute_sell_order(self, symbol: str, quantity: int, price: float,
                           execution_date: pd.Timestamp, signal: Dict[str, Any]):
        """执行卖出订单"""
        try:
            # 检查持仓
            if symbol not in self.positions or self.positions[symbol]['quantity'] <= 0:
                return

            # 调整卖出数量
            available_quantity = self.positions[symbol]['quantity']
            quantity = min(quantity, available_quantity)

            if quantity <= 0:
                return

            # 计算交易成本
            trade_value = quantity * price
            commission = trade_value * self.commission_rate
            net_proceeds = trade_value - commission

            # 计算盈亏
            avg_cost = self.positions[symbol]['avg_price']
            pnl = (price - avg_cost) * quantity - commission

            # 更新持仓
            self.positions[symbol]['quantity'] -= quantity

            if self.positions[symbol]['quantity'] <= 0:
                del self.positions[symbol]

            # 更新现金
            self.current_capital += net_proceeds

            # 记录交易
            trade = {
                'date': execution_date,
                'symbol': symbol,
                'direction': 'sell',
                'quantity': quantity,
                'price': price,
                'value': trade_value,
                'commission': commission,
                'pnl': pnl,
                'strategy': signal.get('strategy', 'Unknown'),
                'signal_reason': signal.get('reason', ''),
                'confidence': signal.get('confidence', 0)
            }

            self.trades.append(trade)

        except Exception as e:
            self.logger.error(f"执行卖出订单失败: {e}")

    def _calculate_total_portfolio_value(self, current_date: pd.Timestamp) -> float:
        """计算组合总价值"""
        try:
            total_value = self.current_capital

            for symbol, position in self.positions.items():
                if symbol in self.data and current_date in self.data[symbol].index:
                    current_price = self.data[symbol].loc[current_date, 'close']
                    position_value = position['quantity'] * current_price
                    total_value += position_value

            return total_value

        except Exception as e:
            self.logger.error(f"计算组合价值失败: {e}")
            return self.current_capital

    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """计算性能指标"""
        try:
            if not self.portfolio_values:
                return {}

            # 转换为DataFrame便于计算
            portfolio_df = pd.DataFrame(self.portfolio_values)
            portfolio_df.set_index('date', inplace=True)

            # 基本指标
            final_value = portfolio_df['total_value'].iloc[-1]
            total_return = (final_value - self.initial_capital) / self.initial_capital

            # 日收益率
            daily_returns = portfolio_df['total_value'].pct_change().dropna()

            # 年化收益率
            trading_days = len(portfolio_df)
            if trading_days > 0:
                annualized_return = (final_value / self.initial_capital) ** (252 / trading_days) - 1
            else:
                annualized_return = 0

            # 波动率
            volatility = daily_returns.std() * np.sqrt(252) if len(daily_returns) > 1 else 0

            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03
            if volatility > 0:
                sharpe_ratio = (annualized_return - risk_free_rate) / volatility
            else:
                sharpe_ratio = 0

            # 最大回撤
            cumulative_returns = (portfolio_df['total_value'] / self.initial_capital)
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdown.min()

            # 交易统计
            trades_df = pd.DataFrame(self.trades) if self.trades else pd.DataFrame()

            if not trades_df.empty:
                # 盈利交易统计
                profitable_trades = trades_df[trades_df.get('pnl', 0) > 0]
                losing_trades = trades_df[trades_df.get('pnl', 0) < 0]

                total_trades = len(trades_df[trades_df['direction'] == 'sell'])  # 只计算卖出交易
                winning_trades = len(profitable_trades)
                losing_trades_count = len(losing_trades)

                win_rate = winning_trades / total_trades if total_trades > 0 else 0

                avg_win = profitable_trades['pnl'].mean() if len(profitable_trades) > 0 else 0
                avg_loss = losing_trades['pnl'].mean() if len(losing_trades) > 0 else 0

                profit_factor = abs(profitable_trades['pnl'].sum() / losing_trades['pnl'].sum()) if len(losing_trades) > 0 and losing_trades['pnl'].sum() != 0 else float('inf')

                total_commission = trades_df['commission'].sum()
            else:
                total_trades = 0
                winning_trades = 0
                losing_trades_count = 0
                win_rate = 0
                avg_win = 0
                avg_loss = 0
                profit_factor = 0
                total_commission = 0

            # 策略统计
            strategy_stats = {}
            if self.signals:
                signals_df = pd.DataFrame(self.signals)
                for strategy_name in signals_df['strategy'].unique():
                    strategy_signals = signals_df[signals_df['strategy'] == strategy_name]
                    strategy_stats[strategy_name] = {
                        'total_signals': len(strategy_signals),
                        'buy_signals': len(strategy_signals[strategy_signals['direction'] == 'buy']),
                        'sell_signals': len(strategy_signals[strategy_signals['direction'] == 'sell']),
                        'avg_confidence': strategy_signals['confidence'].mean()
                    }

            metrics = {
                # 收益指标
                'total_return': total_return,
                'annualized_return': annualized_return,
                'final_value': final_value,
                'initial_capital': self.initial_capital,

                # 风险指标
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,

                # 交易指标
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades_count,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'total_commission': total_commission,

                # 时间指标
                'trading_days': trading_days,
                'start_date': portfolio_df.index[0] if len(portfolio_df) > 0 else None,
                'end_date': portfolio_df.index[-1] if len(portfolio_df) > 0 else None,

                # 策略统计
                'strategy_stats': strategy_stats,

                # 原始数据
                'portfolio_values': self.portfolio_values,
                'trades': self.trades,
                'signals': self.signals
            }

            return metrics

        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {}

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取组合摘要"""
        try:
            if not self.performance_metrics:
                return {}

            metrics = self.performance_metrics

            summary = {
                '回测周期': f"{metrics.get('start_date', 'N/A')} 至 {metrics.get('end_date', 'N/A')}",
                '交易天数': metrics.get('trading_days', 0),
                '初始资金': f"¥{metrics.get('initial_capital', 0):,.2f}",
                '最终价值': f"¥{metrics.get('final_value', 0):,.2f}",
                '总收益率': f"{metrics.get('total_return', 0)*100:.2f}%",
                '年化收益率': f"{metrics.get('annualized_return', 0)*100:.2f}%",
                '年化波动率': f"{metrics.get('volatility', 0)*100:.2f}%",
                '夏普比率': f"{metrics.get('sharpe_ratio', 0):.3f}",
                '最大回撤': f"{metrics.get('max_drawdown', 0)*100:.2f}%",
                '总交易次数': metrics.get('total_trades', 0),
                '胜率': f"{metrics.get('win_rate', 0)*100:.2f}%",
                '盈亏比': f"{metrics.get('profit_factor', 0):.2f}",
                '总手续费': f"¥{metrics.get('total_commission', 0):,.2f}"
            }

            return summary

        except Exception as e:
            self.logger.error(f"获取组合摘要失败: {e}")
            return {}

    def export_results(self, output_dir: str = "backtest_results") -> bool:
        """导出回测结果"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 导出交易记录
            if self.trades:
                trades_df = pd.DataFrame(self.trades)
                trades_file = output_path / f"trades_{timestamp}.csv"
                trades_df.to_csv(trades_file, index=False, encoding='utf-8-sig')

            # 导出组合价值
            if self.portfolio_values:
                portfolio_df = pd.DataFrame(self.portfolio_values)
                portfolio_file = output_path / f"portfolio_{timestamp}.csv"
                portfolio_df.to_csv(portfolio_file, index=False, encoding='utf-8-sig')

            # 导出信号记录
            if self.signals:
                signals_df = pd.DataFrame(self.signals)
                signals_file = output_path / f"signals_{timestamp}.csv"
                signals_df.to_csv(signals_file, index=False, encoding='utf-8-sig')

            # 导出性能指标
            if self.performance_metrics:
                import json
                metrics_file = output_path / f"metrics_{timestamp}.json"

                # 处理不能序列化的对象
                exportable_metrics = {}
                for key, value in self.performance_metrics.items():
                    if key in ['portfolio_values', 'trades', 'signals']:
                        continue  # 这些已经单独导出
                    elif isinstance(value, (pd.Timestamp, datetime)):
                        exportable_metrics[key] = str(value)
                    elif isinstance(value, (np.integer, np.floating)):
                        exportable_metrics[key] = float(value)
                    else:
                        exportable_metrics[key] = value

                with open(metrics_file, 'w', encoding='utf-8') as f:
                    json.dump(exportable_metrics, f, indent=2, ensure_ascii=False)

            self.logger.info(f"回测结果导出成功: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出回测结果失败: {e}")
            return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析中心组件
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSplitter,
    QGroupBox, QTableWidget, QTableWidgetItem, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget,
    QSpinBox, QDoubleSpinBox, QCheckBox, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QPalette, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
# 导入分析模块（如果不存在则创建占位类）
# 暂时禁用技术图表组件导入，避免matplotlib依赖问题
TechnicalChartWidget = None

try:
    from analysis.technical_indicators import TechnicalIndicators
except ImportError:
    TechnicalIndicators = None

try:
    from analysis.fundamental_analysis import FundamentalAnalysis
except ImportError:
    FundamentalAnalysis = None

try:
    from analysis.quantitative_analysis import QuantitativeAnalysis
except ImportError:
    QuantitativeAnalysis = None


class AnalysisCenterWidget(QWidget):
    """分析中心组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("AnalysisCenterWidget")

        # 延迟初始化分析模块，避免导入时卡死
        self.ti = None
        self.fa = None
        self.qa = None
        self.current_data = None

        # 先初始化UI
        self.init_ui()

        # 然后异步初始化分析模块
        self.init_analysis_modules()

        self.logger.info("分析中心组件初始化完成")

    def init_analysis_modules(self):
        """初始化分析模块"""
        try:
            # 安全初始化技术指标模块
            if TechnicalIndicators:
                self.ti = TechnicalIndicators()
                self.logger.info("技术指标模块初始化成功")
            else:
                self.logger.warning("技术指标模块不可用")

            # 安全初始化基本面分析模块
            if FundamentalAnalysis:
                self.fa = FundamentalAnalysis()
                self.logger.info("基本面分析模块初始化成功")
            else:
                self.logger.warning("基本面分析模块不可用")

            # 安全初始化量化分析模块
            if QuantitativeAnalysis:
                self.qa = QuantitativeAnalysis()
                self.logger.info("量化分析模块初始化成功")
            else:
                self.logger.warning("量化分析模块不可用")

        except Exception as e:
            self.logger.error(f"分析模块初始化失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_label = QLabel("分析中心")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 技术分析标签页
        self.tab_widget.addTab(self.create_technical_analysis_tab(), "技术分析")

        # 基本面分析标签页
        self.tab_widget.addTab(self.create_fundamental_analysis_tab(), "基本面分析")

        # 量化分析标签页
        self.tab_widget.addTab(self.create_quantitative_analysis_tab(), "量化分析")

        # 报告生成标签页
        self.tab_widget.addTab(self.create_report_tab(), "报告生成")

        layout.addWidget(self.tab_widget)

    def create_technical_analysis_tab(self):
        """创建技术分析标签页"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # 左侧参数设置
        left_layout = QVBoxLayout()

        # 股票选择
        stock_group = QGroupBox("股票选择")
        stock_layout = QGridLayout(stock_group)

        symbol_label = QLabel("股票代码:")
        symbol_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        stock_layout.addWidget(symbol_label, 0, 0)
        self.tech_symbol_input = QLineEdit()
        self.tech_symbol_input.setPlaceholderText("例如: 000001.SZ")
        stock_layout.addWidget(self.tech_symbol_input, 0, 1)

        period_label = QLabel("分析周期:")
        period_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        stock_layout.addWidget(period_label, 1, 0)
        self.tech_period_combo = QComboBox()
        self.tech_period_combo.addItems(["日线", "周线", "月线", "60分钟", "30分钟", "15分钟", "5分钟"])
        stock_layout.addWidget(self.tech_period_combo, 1, 1)

        range_label = QLabel("数据范围:")
        range_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        stock_layout.addWidget(range_label, 2, 0)
        date_layout = QHBoxLayout()
        self.tech_start_date = QDateEdit()
        self.tech_start_date.setDate(QDate.currentDate().addDays(-365))
        self.tech_end_date = QDateEdit()
        self.tech_end_date.setDate(QDate.currentDate())
        date_layout.addWidget(self.tech_start_date)
        to_label = QLabel("至")
        to_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        date_layout.addWidget(to_label)
        date_layout.addWidget(self.tech_end_date)
        stock_layout.addLayout(date_layout, 2, 1)

        left_layout.addWidget(stock_group)

        # 指标选择
        indicators_group = QGroupBox("技术指标")
        indicators_layout = QVBoxLayout(indicators_group)

        # 趋势指标
        trend_label = QLabel("趋势指标:")
        trend_label.setFont(QFont("微软雅黑", 9, QFont.Bold))
        trend_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        indicators_layout.addWidget(trend_label)

        self.ma_check = QCheckBox("移动平均线(MA)")
        self.ma_check.setChecked(True)
        indicators_layout.addWidget(self.ma_check)

        self.ema_check = QCheckBox("指数移动平均(EMA)")
        indicators_layout.addWidget(self.ema_check)

        self.macd_check = QCheckBox("MACD")
        self.macd_check.setChecked(True)
        indicators_layout.addWidget(self.macd_check)

        self.bollinger_check = QCheckBox("布林带(BOLL)")
        indicators_layout.addWidget(self.bollinger_check)

        # 震荡指标
        oscillator_label = QLabel("震荡指标:")
        oscillator_label.setFont(QFont("微软雅黑", 9, QFont.Bold))
        oscillator_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        indicators_layout.addWidget(oscillator_label)

        self.rsi_check = QCheckBox("相对强弱指数(RSI)")
        self.rsi_check.setChecked(True)
        indicators_layout.addWidget(self.rsi_check)

        self.kdj_check = QCheckBox("随机指标(KDJ)")
        indicators_layout.addWidget(self.kdj_check)

        self.cci_check = QCheckBox("顺势指标(CCI)")
        indicators_layout.addWidget(self.cci_check)

        # 成交量指标
        volume_label = QLabel("成交量指标:")
        volume_label.setFont(QFont("微软雅黑", 9, QFont.Bold))
        volume_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        indicators_layout.addWidget(volume_label)

        self.obv_check = QCheckBox("能量潮(OBV)")
        indicators_layout.addWidget(self.obv_check)

        self.vwap_check = QCheckBox("成交量加权平均价(VWAP)")
        indicators_layout.addWidget(self.vwap_check)

        left_layout.addWidget(indicators_group)

        # 分析按钮
        analyze_btn = QPushButton("开始分析")
        analyze_btn.clicked.connect(self.start_technical_analysis)
        left_layout.addWidget(analyze_btn)

        left_layout.addStretch()

        # 右侧结果显示
        right_layout = QVBoxLayout()

        # 分析结果
        results_group = QGroupBox("分析结果")
        results_layout = QVBoxLayout(results_group)

        # 技术分析图表
        if TechnicalChartWidget:
            self.tech_chart_widget = TechnicalChartWidget()
        else:
            self.tech_chart_widget = QLabel("技术分析图表区域\n（图表组件开发中）")
            self.tech_chart_widget.setAlignment(Qt.AlignCenter)
            self.tech_chart_widget.setStyleSheet("border: 1px solid gray; min-height: 300px; color: white;")
        results_layout.addWidget(self.tech_chart_widget)

        # 指标数值表格
        self.tech_indicators_table = QTableWidget()
        self.tech_indicators_table.setColumnCount(3)
        self.tech_indicators_table.setHorizontalHeaderLabels(["指标", "当前值", "信号"])
        self.tech_indicators_table.horizontalHeader().setStretchLastSection(True)
        self.tech_indicators_table.setMaximumHeight(200)

        # 添加示例数据
        self.add_sample_tech_indicators()

        results_layout.addWidget(self.tech_indicators_table)

        right_layout.addWidget(results_group)

        # 添加到主布局
        layout.addLayout(left_layout, 1)
        layout.addLayout(right_layout, 2)

        return widget

    def create_fundamental_analysis_tab(self):
        """创建基本面分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 股票选择
        stock_group = QGroupBox("股票选择")
        stock_layout = QHBoxLayout(stock_group)

        stock_layout.addWidget(QLabel("股票代码:"))
        self.fund_symbol_input = QLineEdit()
        stock_layout.addWidget(self.fund_symbol_input)

        analyze_btn = QPushButton("分析")
        analyze_btn.clicked.connect(self.start_fundamental_analysis)
        stock_layout.addWidget(analyze_btn)

        stock_layout.addStretch()
        layout.addWidget(stock_group)

        # 基本面数据
        fundamental_group = QGroupBox("基本面数据")
        fundamental_layout = QVBoxLayout(fundamental_group)

        self.fundamental_table = QTableWidget()
        self.fundamental_table.setColumnCount(4)
        self.fundamental_table.setHorizontalHeaderLabels(["指标", "最新值", "行业平均", "评级"])
        self.fundamental_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例基本面数据
        self.add_sample_fundamental_data()

        fundamental_layout.addWidget(self.fundamental_table)
        layout.addWidget(fundamental_group)

        # 财务分析
        financial_group = QGroupBox("财务分析")
        financial_layout = QVBoxLayout(financial_group)

        self.financial_table = QTableWidget()
        self.financial_table.setColumnCount(5)
        self.financial_table.setHorizontalHeaderLabels(["项目", "2023年", "2022年", "2021年", "同比增长"])
        self.financial_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例财务数据
        self.add_sample_financial_data()

        financial_layout.addWidget(self.financial_table)
        layout.addWidget(financial_group)

        return widget

    def create_quantitative_analysis_tab(self):
        """创建量化分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 分析类型选择
        analysis_group = QGroupBox("分析类型")
        analysis_layout = QGridLayout(analysis_group)

        analysis_type_label = QLabel("分析类型:")
        analysis_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        analysis_layout.addWidget(analysis_type_label, 0, 0)
        self.quant_type_combo = QComboBox()
        self.quant_type_combo.addItems([
            "相关性分析", "因子分析", "风险分析", "收益分析", "回归分析"
        ])
        analysis_layout.addWidget(self.quant_type_combo, 0, 1)

        stock_pool_label = QLabel("股票池:")
        stock_pool_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        analysis_layout.addWidget(stock_pool_label, 1, 0)
        self.stock_pool_input = QLineEdit()
        self.stock_pool_input.setPlaceholderText("多个股票用逗号分隔")
        analysis_layout.addWidget(self.stock_pool_input, 1, 1)

        start_quant_btn = QPushButton("开始分析")
        start_quant_btn.clicked.connect(self.start_quantitative_analysis)
        analysis_layout.addWidget(start_quant_btn, 2, 0, 1, 2)

        layout.addWidget(analysis_group)

        # 分析结果
        results_group = QGroupBox("分析结果")
        results_layout = QVBoxLayout(results_group)

        # 结果图表区域（占位）
        chart_label = QLabel("量化分析图表区域")
        chart_label.setAlignment(Qt.AlignCenter)
        chart_label.setStyleSheet("border: 1px solid gray; min-height: 250px;")
        results_layout.addWidget(chart_label)

        # 结果数据表格
        self.quant_results_table = QTableWidget()
        self.quant_results_table.setColumnCount(4)
        self.quant_results_table.setHorizontalHeaderLabels(["股票", "因子值", "排名", "评分"])
        self.quant_results_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例量化分析结果
        self.add_sample_quant_results()

        results_layout.addWidget(self.quant_results_table)
        layout.addWidget(results_group)

        return widget

    def create_report_tab(self):
        """创建报告生成标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 报告配置
        config_group = QGroupBox("报告配置")
        config_layout = QGridLayout(config_group)

        report_type_label = QLabel("报告类型:")
        report_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(report_type_label, 0, 0)
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "技术分析报告", "基本面分析报告", "策略回测报告", "风险评估报告", "综合分析报告"
        ])
        config_layout.addWidget(self.report_type_combo, 0, 1)

        stock_symbol_label = QLabel("股票代码:")
        stock_symbol_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(stock_symbol_label, 1, 0)
        self.report_symbol_input = QLineEdit()
        config_layout.addWidget(self.report_symbol_input, 1, 1)

        report_period_label = QLabel("报告周期:")
        report_period_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(report_period_label, 2, 0)
        period_layout = QHBoxLayout()
        self.report_start_date = QDateEdit()
        self.report_start_date.setDate(QDate.currentDate().addDays(-30))
        self.report_end_date = QDateEdit()
        self.report_end_date.setDate(QDate.currentDate())
        period_layout.addWidget(self.report_start_date)
        period_layout.addWidget(QLabel("至"))
        period_layout.addWidget(self.report_end_date)
        config_layout.addLayout(period_layout, 2, 1)

        output_format_label = QLabel("输出格式:")
        output_format_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(output_format_label, 3, 0)
        self.report_format_combo = QComboBox()
        self.report_format_combo.addItems(["PDF", "HTML", "Word", "Excel"])
        config_layout.addWidget(self.report_format_combo, 3, 1)

        layout.addWidget(config_group)

        # 报告内容选择
        content_group = QGroupBox("报告内容")
        content_layout = QVBoxLayout(content_group)

        self.include_charts_check = QCheckBox("包含图表")
        self.include_charts_check.setChecked(True)
        content_layout.addWidget(self.include_charts_check)

        self.include_indicators_check = QCheckBox("包含技术指标")
        self.include_indicators_check.setChecked(True)
        content_layout.addWidget(self.include_indicators_check)

        self.include_fundamental_check = QCheckBox("包含基本面数据")
        content_layout.addWidget(self.include_fundamental_check)

        self.include_risk_check = QCheckBox("包含风险分析")
        content_layout.addWidget(self.include_risk_check)

        self.include_recommendation_check = QCheckBox("包含投资建议")
        content_layout.addWidget(self.include_recommendation_check)

        layout.addWidget(content_group)

        # 生成按钮
        button_layout = QHBoxLayout()

        generate_btn = QPushButton("生成报告")
        generate_btn.clicked.connect(self.generate_report)
        button_layout.addWidget(generate_btn)

        preview_btn = QPushButton("预览报告")
        preview_btn.clicked.connect(self.preview_report)
        button_layout.addWidget(preview_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 生成进度
        progress_group = QGroupBox("生成进度")
        progress_layout = QVBoxLayout(progress_group)

        self.report_progress = QProgressBar()
        progress_layout.addWidget(self.report_progress)

        self.report_status_label = QLabel("就绪")


        self.report_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        progress_layout.addWidget(self.report_status_label)

        layout.addWidget(progress_group)

        layout.addStretch()
        return widget

    def add_sample_tech_indicators(self):
        """添加示例技术指标数据"""
        indicators = [
            ("MA5", "12.45", "买入"),
            ("MA20", "12.20", "买入"),
            ("MACD", "0.15", "买入"),
            ("RSI", "65.2", "中性"),
            ("KDJ", "75.3", "卖出"),
            ("BOLL上轨", "12.80", "阻力"),
            ("BOLL下轨", "11.90", "支撑"),
        ]

        self.tech_indicators_table.setRowCount(len(indicators))
        for row, (indicator, value, signal) in enumerate(indicators):
            self.tech_indicators_table.setItem(row, 0, QTableWidgetItem(indicator))
            self.tech_indicators_table.setItem(row, 1, QTableWidgetItem(value))

            signal_item = QTableWidgetItem(signal)
            if signal == "买入":
                signal_item.setForeground(QColor("red"))
            elif signal == "卖出":
                signal_item.setForeground(QColor("green"))
            self.tech_indicators_table.setItem(row, 2, signal_item)

    def add_sample_fundamental_data(self):
        """添加示例基本面数据"""
        fundamental = [
            ("市盈率(PE)", "15.2", "18.5", "优秀"),
            ("市净率(PB)", "1.8", "2.1", "良好"),
            ("净资产收益率(ROE)", "12.5%", "10.2%", "优秀"),
            ("毛利率", "35.2%", "28.6%", "优秀"),
            ("资产负债率", "45.8%", "52.3%", "良好"),
            ("流动比率", "2.1", "1.8", "良好"),
        ]

        self.fundamental_table.setRowCount(len(fundamental))
        for row, (metric, value, industry_avg, rating) in enumerate(fundamental):
            self.fundamental_table.setItem(row, 0, QTableWidgetItem(metric))
            self.fundamental_table.setItem(row, 1, QTableWidgetItem(value))
            self.fundamental_table.setItem(row, 2, QTableWidgetItem(industry_avg))

            rating_item = QTableWidgetItem(rating)
            if rating == "优秀":
                rating_item.setForeground(QColor("red"))
            elif rating == "良好":
                rating_item.setForeground(QColor("blue"))
            self.fundamental_table.setItem(row, 3, rating_item)

    def add_sample_financial_data(self):
        """添加示例财务数据"""
        financial = [
            ("营业收入(亿元)", "125.6", "118.2", "110.5", "+6.3%"),
            ("净利润(亿元)", "18.9", "16.8", "15.2", "+12.5%"),
            ("每股收益(元)", "1.25", "1.12", "1.01", "+11.6%"),
            ("净资产(亿元)", "89.5", "82.1", "75.8", "+9.0%"),
            ("总资产(亿元)", "156.8", "148.2", "142.1", "+5.8%"),
        ]

        self.financial_table.setRowCount(len(financial))
        for row, (item, y2023, y2022, y2021, growth) in enumerate(financial):
            self.financial_table.setItem(row, 0, QTableWidgetItem(item))
            self.financial_table.setItem(row, 1, QTableWidgetItem(y2023))
            self.financial_table.setItem(row, 2, QTableWidgetItem(y2022))
            self.financial_table.setItem(row, 3, QTableWidgetItem(y2021))

            growth_item = QTableWidgetItem(growth)
            if growth.startswith('+'):
                growth_item.setForeground(QColor("red"))
            elif growth.startswith('-'):
                growth_item.setForeground(QColor("green"))
            self.financial_table.setItem(row, 4, growth_item)

    def add_sample_quant_results(self):
        """添加示例量化分析结果"""
        results = [
            ("000001.SZ", "0.85", "1", "A+"),
            ("600036.SH", "0.72", "2", "A"),
            ("300015.SZ", "0.68", "3", "A-"),
            ("002415.SZ", "0.55", "4", "B+"),
            ("000002.SZ", "0.42", "5", "B"),
        ]

        self.quant_results_table.setRowCount(len(results))
        for row, (stock, factor, rank, score) in enumerate(results):
            self.quant_results_table.setItem(row, 0, QTableWidgetItem(stock))
            self.quant_results_table.setItem(row, 1, QTableWidgetItem(factor))
            self.quant_results_table.setItem(row, 2, QTableWidgetItem(rank))
            self.quant_results_table.setItem(row, 3, QTableWidgetItem(score))

    def start_technical_analysis(self):
        """开始技术分析"""
        try:
            # 获取输入参数
            symbol = self.tech_symbol_input.text().strip()
            if not symbol:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "请输入股票代码")
                return

            # 生成示例数据进行演示
            sample_data = self.generate_sample_stock_data(symbol)

            # 设置数据到图表组件
            if hasattr(self.tech_chart_widget, 'set_data'):
                self.tech_chart_widget.set_data(sample_data, symbol)
            else:
                # 更新占位标签
                self.tech_chart_widget.setText(f"技术分析图表区域\n股票代码: {symbol}\n数据点数: {len(sample_data)}")

            self.current_data = sample_data

            # 计算技术指标并更新表格
            if self.ti:
                self.update_tech_indicators_table(sample_data)
            else:
                # 使用示例数据
                self.add_sample_tech_indicators()

            self.logger.info(f"技术分析完成: {symbol}")

        except Exception as e:
            self.logger.error(f"技术分析失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"技术分析失败:\n{str(e)}")

    def generate_sample_stock_data(self, symbol: str):
        """生成示例股票数据"""
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta

        # 生成日期序列
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日

        # 生成模拟股价数据
        np.random.seed(hash(symbol) % 2**32)  # 根据股票代码设置种子
        base_price = np.random.uniform(10, 100)

        prices = [base_price]
        for i in range(1, len(dates)):
            # 模拟股价波动
            change = np.random.normal(0.001, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格为正

        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开盘价
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))

            # 生成最高价和最低价
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))

            # 生成成交量
            volume = np.random.randint(1000000, 10000000)

            data.append({
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })

        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)

        return df

    def update_tech_indicators_table(self, data):
        """更新技术指标表格"""
        try:
            # 计算技术指标
            indicators_data = self.ti.calculate_all_indicators(data)
            latest_data = indicators_data.iloc[-1]

            # 准备显示的指标
            indicators_to_show = [
                ('MA5', 'sma_5', self.get_ma_signal),
                ('MA20', 'sma_20', self.get_ma_signal),
                ('RSI', 'rsi_12', self.get_rsi_signal),
                ('MACD', 'macd', self.get_macd_signal),
                ('KDJ_K', 'kdj_k', self.get_kdj_signal),
                ('KDJ_D', 'kdj_d', self.get_kdj_signal),
                ('布林带上轨', 'bb_upper', self.get_bb_signal),
                ('布林带下轨', 'bb_lower', self.get_bb_signal),
                ('威廉指标', 'wr', self.get_wr_signal),
                ('CCI', 'cci', self.get_cci_signal),
            ]

            self.tech_indicators_table.setRowCount(len(indicators_to_show))

            for row, (name, column, signal_func) in enumerate(indicators_to_show):
                if column in indicators_data.columns:
                    value = latest_data[column]
                    if pd.notna(value):
                        # 设置指标名称
                        self.tech_indicators_table.setItem(row, 0, QTableWidgetItem(name))

                        # 设置当前值
                        value_str = f"{value:.2f}"
                        self.tech_indicators_table.setItem(row, 1, QTableWidgetItem(value_str))

                        # 设置信号
                        signal = signal_func(value, indicators_data, column)
                        signal_item = QTableWidgetItem(signal)

                        # 设置信号颜色
                        if signal == "买入":
                            signal_item.setForeground(QColor("red"))
                        elif signal == "卖出":
                            signal_item.setForeground(QColor("green"))
                        else:
                            signal_item.setForeground(QColor("yellow"))

                        self.tech_indicators_table.setItem(row, 2, signal_item)

            self.tech_indicators_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新技术指标表格失败: {e}")

    def get_ma_signal(self, value, data, column):
        """获取移动平均线信号"""
        try:
            current_price = data['close'].iloc[-1]
            if current_price > value:
                return "买入"
            elif current_price < value:
                return "卖出"
            else:
                return "中性"
        except:
            return "中性"

    def get_rsi_signal(self, value, data, column):
        """获取RSI信号"""
        if value > 70:
            return "卖出"
        elif value < 30:
            return "买入"
        else:
            return "中性"

    def get_macd_signal(self, value, data, column):
        """获取MACD信号"""
        try:
            if 'macd_histogram' in data.columns:
                histogram = data['macd_histogram'].iloc[-1]
                if histogram > 0:
                    return "买入"
                elif histogram < 0:
                    return "卖出"
            return "中性"
        except:
            return "中性"

    def get_kdj_signal(self, value, data, column):
        """获取KDJ信号"""
        if value > 80:
            return "卖出"
        elif value < 20:
            return "买入"
        else:
            return "中性"

    def get_bb_signal(self, value, data, column):
        """获取布林带信号"""
        try:
            current_price = data['close'].iloc[-1]
            if column == 'bb_upper' and current_price > value:
                return "卖出"
            elif column == 'bb_lower' and current_price < value:
                return "买入"
            else:
                return "支撑/阻力"
        except:
            return "中性"

    def get_wr_signal(self, value, data, column):
        """获取威廉指标信号"""
        if value > 80:
            return "卖出"
        elif value < 20:
            return "买入"
        else:
            return "中性"

    def get_cci_signal(self, value, data, column):
        """获取CCI信号"""
        if value > 100:
            return "卖出"
        elif value < -100:
            return "买入"
        else:
            return "中性"

    def start_fundamental_analysis(self):
        """开始基本面分析"""
        try:
            # 获取输入参数
            symbol = self.fund_symbol_input.text().strip()
            if not symbol:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "请输入股票代码")
                return

            # 进行基本面分析
            if self.fa:
                analysis_result = self.fa.analyze_stock(symbol)

                if analysis_result:
                    # 更新基本面数据表格
                    self.update_fundamental_table(analysis_result)

                    # 更新财务分析表格
                    self.update_financial_table(analysis_result)

                    self.logger.info(f"基本面分析完成: {symbol}")

                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(
                        self, "分析完成",
                        f"股票 {symbol} 基本面分析完成！\n"
                        f"综合评级: {analysis_result['evaluation'].get('综合评级', 'N/A')}\n"
                        f"综合得分: {analysis_result['evaluation'].get('综合得分', 'N/A')}"
                    )
                else:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "警告", "基本面分析失败，请检查股票代码")
            else:
                # 使用示例数据
                self.logger.info(f"基本面分析（示例模式）: {symbol}")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self, "分析完成",
                    f"股票 {symbol} 基本面分析完成！\n"
                    f"（当前为示例数据模式）\n"
                    f"综合评级: A\n"
                    f"综合得分: 85分"
                )

        except Exception as e:
            self.logger.error(f"基本面分析失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"基本面分析失败:\n{str(e)}")

    def update_fundamental_table(self, analysis_result):
        """更新基本面数据表格"""
        try:
            ratios = analysis_result.get('ratios', {})
            evaluation = analysis_result.get('evaluation', {})
            industry_comparison = analysis_result.get('industry_comparison', {})

            # 准备显示的基本面指标
            fundamental_data = [
                ("市盈率(PE)", ratios.get('市盈率(PE)', 0), "15.2", self.get_rating_from_comparison(industry_comparison.get('市盈率(PE)', '接近行业'))),
                ("市净率(PB)", ratios.get('市净率(PB)', 0), "2.1", self.get_rating_from_comparison(industry_comparison.get('市净率(PB)', '接近行业'))),
                ("净资产收益率(ROE)", f"{ratios.get('净资产收益率(ROE)', 0):.2f}%", "10.2%", self.get_rating_from_comparison(industry_comparison.get('净资产收益率(ROE)', '接近行业'))),
                ("净利润率", f"{ratios.get('净利润率', 0):.2f}%", "8.5%", self.get_rating_from_comparison(industry_comparison.get('净利润率', '接近行业'))),
                ("资产负债率", f"{ratios.get('资产负债率', 0):.2f}%", "55.0%", self.get_rating_from_comparison(industry_comparison.get('资产负债率', '接近行业'))),
                ("流动比率", f"{ratios.get('流动比率', 0):.2f}", "1.8", self.get_rating_from_comparison(industry_comparison.get('流动比率', '接近行业'))),
                ("总资产收益率(ROA)", f"{ratios.get('总资产收益率(ROA)', 0):.2f}%", "5.2%", evaluation.get('盈利能力', '一般')),
                ("每股收益(EPS)", f"{ratios.get('每股收益(EPS)', 0):.2f}", "1.25", evaluation.get('盈利能力', '一般')),
            ]

            self.fundamental_table.setRowCount(len(fundamental_data))

            for row, (metric, value, industry_avg, rating) in enumerate(fundamental_data):
                self.fundamental_table.setItem(row, 0, QTableWidgetItem(metric))
                self.fundamental_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.fundamental_table.setItem(row, 2, QTableWidgetItem(industry_avg))

                rating_item = QTableWidgetItem(rating)
                if rating in ["优秀", "远超行业"]:
                    rating_item.setForeground(QColor("red"))
                elif rating in ["良好", "超过行业"]:
                    rating_item.setForeground(QColor("blue"))
                elif rating in ["一般", "接近行业"]:
                    rating_item.setForeground(QColor("yellow"))
                else:
                    rating_item.setForeground(QColor("gray"))

                self.fundamental_table.setItem(row, 3, rating_item)

            self.fundamental_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新基本面数据表格失败: {e}")

    def update_financial_table(self, analysis_result):
        """更新财务分析表格"""
        try:
            financial_data = analysis_result.get('financial_data', {})
            ratios = analysis_result.get('ratios', {})

            # 模拟多年财务数据
            import random

            # 当前年度数据
            current_revenue = financial_data.get('revenue', 0) / 10000  # 转换为亿元
            current_net_income = financial_data.get('net_income', 0) / 10000
            current_eps = ratios.get('每股收益(EPS)', 0)
            current_equity = financial_data.get('total_equity', 0) / 10000
            current_assets = financial_data.get('total_assets', 0) / 10000

            # 生成历史数据（简化处理）
            revenue_growth = ratios.get('营收增长率', 0) / 100
            profit_growth = ratios.get('净利润增长率', 0) / 100

            prev_year_revenue = current_revenue / (1 + revenue_growth) if revenue_growth != -1 else current_revenue * 0.9
            prev2_year_revenue = prev_year_revenue / (1 + revenue_growth * 0.8) if revenue_growth != -1 else prev_year_revenue * 0.9

            prev_year_profit = current_net_income / (1 + profit_growth) if profit_growth != -1 else current_net_income * 0.9
            prev2_year_profit = prev_year_profit / (1 + profit_growth * 0.8) if profit_growth != -1 else prev_year_profit * 0.9

            financial_table_data = [
                ("营业收入(亿元)", f"{current_revenue:.1f}", f"{prev_year_revenue:.1f}", f"{prev2_year_revenue:.1f}", f"+{revenue_growth*100:.1f}%"),
                ("净利润(亿元)", f"{current_net_income:.1f}", f"{prev_year_profit:.1f}", f"{prev2_year_profit:.1f}", f"+{profit_growth*100:.1f}%"),
                ("每股收益(元)", f"{current_eps:.2f}", f"{current_eps*0.9:.2f}", f"{current_eps*0.8:.2f}", f"+{((current_eps/(current_eps*0.9))-1)*100:.1f}%"),
                ("净资产(亿元)", f"{current_equity:.1f}", f"{current_equity*0.95:.1f}", f"{current_equity*0.9:.1f}", f"+{5.0:.1f}%"),
                ("总资产(亿元)", f"{current_assets:.1f}", f"{current_assets*0.93:.1f}", f"{current_assets*0.87:.1f}", f"+{7.0:.1f}%"),
            ]

            self.financial_table.setRowCount(len(financial_table_data))

            for row, (item, y2023, y2022, y2021, growth) in enumerate(financial_table_data):
                self.financial_table.setItem(row, 0, QTableWidgetItem(item))
                self.financial_table.setItem(row, 1, QTableWidgetItem(y2023))
                self.financial_table.setItem(row, 2, QTableWidgetItem(y2022))
                self.financial_table.setItem(row, 3, QTableWidgetItem(y2021))

                growth_item = QTableWidgetItem(growth)
                if growth.startswith('+'):
                    growth_item.setForeground(QColor("red"))
                elif growth.startswith('-'):
                    growth_item.setForeground(QColor("green"))
                else:
                    growth_item.setForeground(QColor("yellow"))

                self.financial_table.setItem(row, 4, growth_item)

            self.financial_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新财务分析表格失败: {e}")

    def get_rating_from_comparison(self, comparison):
        """根据行业比较结果获取评级"""
        rating_map = {
            '远超行业': '优秀',
            '超过行业': '良好',
            '接近行业': '一般',
            '低于行业': '较差',
            '远低行业': '很差'
        }
        return rating_map.get(comparison, '一般')

    def start_quantitative_analysis(self):
        """开始量化分析"""
        try:
            # 获取输入参数
            symbols_text = self.stock_pool_input.text().strip()
            if not symbols_text:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "请输入股票代码（用逗号分隔）")
                return

            # 解析股票代码
            symbols = [s.strip() for s in symbols_text.split(',') if s.strip()]
            if len(symbols) == 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "请输入有效的股票代码")
                return

            if self.qa:
                # 生成示例市场数据
                market_data = self.qa.generate_sample_market_data(symbols, days=252)

                # 进行风险分析
                risk_analysis = self.qa.perform_risk_analysis(market_data)

                # 计算相关性矩阵
                correlation_matrix = self.qa.calculate_correlation_matrix(market_data)

                # 进行主成分分析
                pca_results = self.qa.perform_pca_analysis(market_data, n_components=min(5, len(symbols)))

                # 更新量化分析表格
                self.update_quantitative_table(risk_analysis, correlation_matrix, pca_results)

                self.logger.info(f"量化分析完成: {len(symbols)} 个标的")

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self, "分析完成",
                    f"量化分析完成！\n"
                    f"分析标的: {len(symbols)} 个\n"
                    f"分析周期: 252 个交易日\n"
                    f"主成分数量: {pca_results.get('n_components', 0)}"
                )
            else:
                # 使用示例数据
                self.logger.info(f"量化分析（示例模式）: {len(symbols)} 个标的")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self, "分析完成",
                    f"量化分析完成！\n"
                    f"分析标的: {len(symbols)} 个\n"
                    f"（当前为示例数据模式）\n"
                    f"分析周期: 252 个交易日"
                )

        except Exception as e:
            self.logger.error(f"量化分析失败: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"量化分析失败:\n{str(e)}")

    def update_quantitative_table(self, risk_analysis, correlation_matrix, pca_results):
        """更新量化分析表格"""
        try:
            # 准备风险指标数据
            risk_data = []

            for symbol, metrics in risk_analysis.items():
                risk_data.append([
                    symbol,
                    f"{metrics.get('annual_return', 0)*100:.2f}%",
                    f"{metrics.get('annual_volatility', 0)*100:.2f}%",
                    f"{metrics.get('sharpe_ratio', 0):.3f}",
                    f"{metrics.get('max_drawdown', 0)*100:.2f}%",
                    f"{metrics.get('var_5', 0)*100:.2f}%",
                    self.get_risk_rating(metrics)
                ])

            # 更新风险分析表格
            self.quant_table.setRowCount(len(risk_data))

            for row, data in enumerate(risk_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))

                    # 设置颜色
                    if col == 6:  # 风险评级列
                        if value == "低风险":
                            item.setForeground(QColor("green"))
                        elif value == "中风险":
                            item.setForeground(QColor("yellow"))
                        elif value == "高风险":
                            item.setForeground(QColor("red"))
                    elif col == 1:  # 年化收益率
                        if float(value.replace('%', '')) > 10:
                            item.setForeground(QColor("red"))
                        elif float(value.replace('%', '')) > 5:
                            item.setForeground(QColor("yellow"))
                        else:
                            item.setForeground(QColor("green"))

                    self.quant_table.setItem(row, col, item)

            self.quant_table.resizeColumnsToContents()

            # 更新相关性分析文本
            if not correlation_matrix.empty:
                corr_text = "相关性分析结果:\n\n"

                # 找出高相关性的股票对
                high_corr_pairs = []
                for i in range(len(correlation_matrix.columns)):
                    for j in range(i+1, len(correlation_matrix.columns)):
                        corr_value = correlation_matrix.iloc[i, j]
                        if abs(corr_value) > 0.7:
                            high_corr_pairs.append((
                                correlation_matrix.columns[i],
                                correlation_matrix.columns[j],
                                corr_value
                            ))

                if high_corr_pairs:
                    corr_text += "高相关性股票对 (|相关系数| > 0.7):\n"
                    for stock1, stock2, corr in high_corr_pairs:
                        corr_text += f"  {stock1} - {stock2}: {corr:.3f}\n"
                else:
                    corr_text += "未发现高相关性股票对 (|相关系数| > 0.7)\n"

                # 显示平均相关性
                avg_corr = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean()
                corr_text += f"\n平均相关系数: {avg_corr:.3f}\n"

                # PCA分析结果
                if pca_results:
                    corr_text += f"\n主成分分析结果:\n"
                    explained_variance = pca_results.get('explained_variance_ratio', [])
                    cumulative_variance = pca_results.get('cumulative_variance_ratio', [])

                    for i, (exp_var, cum_var) in enumerate(zip(explained_variance, cumulative_variance)):
                        corr_text += f"  PC{i+1}: 解释方差 {exp_var:.3f} ({exp_var*100:.1f}%), 累计 {cum_var:.3f} ({cum_var*100:.1f}%)\n"

                self.correlation_text.setText(corr_text)

        except Exception as e:
            self.logger.error(f"更新量化分析表格失败: {e}")

    def get_risk_rating(self, metrics):
        """获取风险评级"""
        try:
            sharpe_ratio = metrics.get('sharpe_ratio', 0)
            max_drawdown = metrics.get('max_drawdown', 0)
            volatility = metrics.get('annual_volatility', 0)

            # 综合评分
            score = 0

            # 夏普比率评分
            if sharpe_ratio > 1.5:
                score += 3
            elif sharpe_ratio > 1.0:
                score += 2
            elif sharpe_ratio > 0.5:
                score += 1

            # 最大回撤评分
            if max_drawdown < 0.1:
                score += 3
            elif max_drawdown < 0.2:
                score += 2
            elif max_drawdown < 0.3:
                score += 1

            # 波动率评分
            if volatility < 0.15:
                score += 3
            elif volatility < 0.25:
                score += 2
            elif volatility < 0.35:
                score += 1

            # 评级
            if score >= 7:
                return "低风险"
            elif score >= 4:
                return "中风险"
            else:
                return "高风险"

        except Exception:
            return "未知"

    def generate_report(self):
        """生成报告"""
        self.logger.info("生成报告功能开发中...")
        self.report_progress.setValue(0)
        self.report_status_label.setText("正在生成报告...")

        # 模拟进度
        for i in range(101):
            self.report_progress.setValue(i)
            if i == 100:
                self.report_status_label.setText("报告生成完成")

    def preview_report(self):
        """预览报告"""
        self.logger.info("预览报告功能开发中...")

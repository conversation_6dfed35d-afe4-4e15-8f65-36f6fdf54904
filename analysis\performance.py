#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绩效分析模块
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

from .risk_metrics import RiskMetrics
from .technical_indicators import TechnicalIndicators
from utils.logger import get_logger

plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


class PerformanceAnalyzer:
    """绩效分析器"""
    
    def __init__(self):
        self.logger = get_logger("PerformanceAnalyzer")
        self.risk_metrics = RiskMetrics()
        self.tech_indicators = TechnicalIndicators()
    
    def analyze_portfolio(self, portfolio_data: pd.DataFrame, 
                         benchmark_data: pd.DataFrame = None,
                         start_date: str = None, 
                         end_date: str = None) -> Dict[str, Any]:
        """分析投资组合绩效"""
        try:
            # 数据预处理
            if start_date:
                portfolio_data = portfolio_data[portfolio_data.index >= start_date]
            if end_date:
                portfolio_data = portfolio_data[portfolio_data.index <= end_date]
            
            if benchmark_data is not None:
                if start_date:
                    benchmark_data = benchmark_data[benchmark_data.index >= start_date]
                if end_date:
                    benchmark_data = benchmark_data[benchmark_data.index <= end_date]
            
            # 计算基础统计
            portfolio_prices = portfolio_data['close'] if 'close' in portfolio_data.columns else portfolio_data.iloc[:, 0]
            benchmark_prices = benchmark_data['close'] if benchmark_data is not None and 'close' in benchmark_data.columns else None
            
            # 计算风险指标
            risk_metrics = self.risk_metrics.calculate_all_metrics(
                portfolio_prices, 
                benchmark_prices
            )
            
            # 计算月度收益
            monthly_returns = self._calculate_monthly_returns(portfolio_prices)
            
            # 计算年度收益
            yearly_returns = self._calculate_yearly_returns(portfolio_prices)
            
            # 计算滚动指标
            rolling_metrics = self._calculate_rolling_metrics(portfolio_prices)
            
            # 计算相关性分析
            correlation_analysis = self._calculate_correlations(portfolio_data, benchmark_data)
            
            analysis_result = {
                'summary': {
                    'start_date': portfolio_data.index[0].strftime('%Y-%m-%d'),
                    'end_date': portfolio_data.index[-1].strftime('%Y-%m-%d'),
                    'total_days': len(portfolio_data),
                    'trading_days': len(portfolio_data.dropna()),
                },
                'risk_metrics': risk_metrics,
                'monthly_returns': monthly_returns,
                'yearly_returns': yearly_returns,
                'rolling_metrics': rolling_metrics,
                'correlation_analysis': correlation_analysis,
            }
            
            self.logger.info("投资组合绩效分析完成")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"投资组合绩效分析失败: {e}")
            return {}
    
    def _calculate_monthly_returns(self, prices: pd.Series) -> pd.DataFrame:
        """计算月度收益"""
        monthly_prices = prices.resample('M').last()
        monthly_returns = monthly_prices.pct_change().dropna()
        
        monthly_stats = pd.DataFrame({
            'return': monthly_returns * 100,
            'cumulative_return': (1 + monthly_returns).cumprod() * 100 - 100
        })
        
        return monthly_stats
    
    def _calculate_yearly_returns(self, prices: pd.Series) -> pd.DataFrame:
        """计算年度收益"""
        yearly_prices = prices.resample('Y').last()
        yearly_returns = yearly_prices.pct_change().dropna()
        
        yearly_stats = pd.DataFrame({
            'return': yearly_returns * 100,
            'volatility': prices.resample('Y').apply(lambda x: x.pct_change().std() * np.sqrt(252) * 100),
            'sharpe_ratio': prices.resample('Y').apply(
                lambda x: self.risk_metrics.sharpe_ratio(x.pct_change().dropna())
            ),
            'max_drawdown': prices.resample('Y').apply(
                lambda x: self.risk_metrics.max_drawdown(x)['max_drawdown'] * 100
            )
        })
        
        return yearly_stats.dropna()
    
    def _calculate_rolling_metrics(self, prices: pd.Series, window: int = 252) -> pd.DataFrame:
        """计算滚动指标"""
        returns = prices.pct_change().dropna()
        
        rolling_metrics = pd.DataFrame(index=prices.index)
        
        # 滚动收益率
        rolling_metrics['rolling_return'] = returns.rolling(window=window).apply(
            lambda x: (1 + x).prod() - 1
        ) * 100
        
        # 滚动波动率
        rolling_metrics['rolling_volatility'] = returns.rolling(window=window).std() * np.sqrt(252) * 100
        
        # 滚动夏普比率
        rolling_metrics['rolling_sharpe'] = returns.rolling(window=window).apply(
            lambda x: self.risk_metrics.sharpe_ratio(x)
        )
        
        # 滚动最大回撤
        rolling_metrics['rolling_max_drawdown'] = prices.rolling(window=window).apply(
            lambda x: self.risk_metrics.max_drawdown(x)['max_drawdown'] if len(x) > 1 else 0
        ) * 100
        
        return rolling_metrics.dropna()
    
    def _calculate_correlations(self, portfolio_data: pd.DataFrame, 
                              benchmark_data: pd.DataFrame = None) -> Dict[str, Any]:
        """计算相关性分析"""
        correlations = {}
        
        if benchmark_data is not None:
            # 与基准的相关性
            portfolio_returns = portfolio_data['close'].pct_change().dropna()
            benchmark_returns = benchmark_data['close'].pct_change().dropna()
            
            # 对齐数据
            aligned_data = pd.concat([portfolio_returns, benchmark_returns], axis=1, join='inner')
            aligned_data.columns = ['portfolio', 'benchmark']
            
            correlations['benchmark_correlation'] = aligned_data.corr().iloc[0, 1]
            correlations['rolling_correlation'] = aligned_data['portfolio'].rolling(
                window=60
            ).corr(aligned_data['benchmark'])
        
        # 内部相关性（如果有多个资产）
        if len(portfolio_data.columns) > 1:
            price_columns = [col for col in portfolio_data.columns if 'close' in col.lower() or 'price' in col.lower()]
            if len(price_columns) > 1:
                returns_data = portfolio_data[price_columns].pct_change().dropna()
                correlations['internal_correlation'] = returns_data.corr()
        
        return correlations
    
    def generate_performance_report(self, analysis_result: Dict[str, Any], 
                                  save_path: str = None) -> str:
        """生成绩效报告"""
        try:
            report = []
            report.append("=" * 60)
            report.append("投资组合绩效分析报告")
            report.append("=" * 60)
            report.append("")
            
            # 基本信息
            summary = analysis_result.get('summary', {})
            report.append("基本信息:")
            report.append(f"  分析期间: {summary.get('start_date', 'N/A')} 至 {summary.get('end_date', 'N/A')}")
            report.append(f"  总天数: {summary.get('total_days', 'N/A')} 天")
            report.append(f"  交易天数: {summary.get('trading_days', 'N/A')} 天")
            report.append("")
            
            # 风险收益指标
            risk_metrics = analysis_result.get('risk_metrics', {})
            report.append("风险收益指标:")
            report.append(f"  总收益率: {risk_metrics.get('total_return', 0):.2f}%")
            report.append(f"  年化收益率: {risk_metrics.get('annual_return', 0):.2f}%")
            report.append(f"  年化波动率: {risk_metrics.get('volatility', 0):.2f}%")
            report.append(f"  夏普比率: {risk_metrics.get('sharpe_ratio', 0):.3f}")
            report.append(f"  索提诺比率: {risk_metrics.get('sortino_ratio', 0):.3f}")
            report.append(f"  卡尔马比率: {risk_metrics.get('calmar_ratio', 0):.3f}")
            report.append("")
            
            # 回撤分析
            max_dd = risk_metrics.get('max_drawdown', {})
            if isinstance(max_dd, dict):
                report.append("回撤分析:")
                report.append(f"  最大回撤: {max_dd.get('max_drawdown', 0) * 100:.2f}%")
                report.append(f"  回撤开始日期: {max_dd.get('peak_date', 'N/A')}")
                report.append(f"  回撤结束日期: {max_dd.get('trough_date', 'N/A')}")
                report.append(f"  回撤持续天数: {max_dd.get('duration_days', 'N/A')} 天")
                if max_dd.get('recovery_date'):
                    report.append(f"  恢复日期: {max_dd.get('recovery_date', 'N/A')}")
                    report.append(f"  恢复天数: {max_dd.get('recovery_days', 'N/A')} 天")
                else:
                    report.append("  尚未恢复到历史高点")
                report.append("")
            
            # 风险指标
            report.append("风险指标:")
            report.append(f"  95% VaR: {risk_metrics.get('var_95', 0):.2f}%")
            report.append(f"  95% CVaR: {risk_metrics.get('cvar_95', 0):.2f}%")
            report.append(f"  下行偏差: {risk_metrics.get('downside_deviation', 0):.2f}%")
            report.append(f"  偏度: {risk_metrics.get('skewness', 0):.3f}")
            report.append(f"  峰度: {risk_metrics.get('kurtosis', 0):.3f}")
            report.append("")
            
            # 交易统计
            report.append("交易统计:")
            report.append(f"  胜率: {risk_metrics.get('win_rate', 0):.2f}%")
            report.append(f"  盈亏比: {risk_metrics.get('profit_loss_ratio', 0):.3f}")
            report.append(f"  最大连续亏损: {risk_metrics.get('max_consecutive_losses', 0)} 次")
            report.append(f"  最大连续盈利: {risk_metrics.get('max_consecutive_wins', 0)} 次")
            report.append("")
            
            # 相对基准指标（如果有）
            if 'beta' in risk_metrics:
                report.append("相对基准指标:")
                report.append(f"  贝塔系数: {risk_metrics.get('beta', 0):.3f}")
                report.append(f"  阿尔法系数: {risk_metrics.get('alpha', 0):.2f}%")
                report.append(f"  信息比率: {risk_metrics.get('information_ratio', 0):.3f}")
                report.append("")
            
            report_text = "\n".join(report)
            
            # 保存报告
            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                self.logger.info(f"绩效报告已保存到: {save_path}")
            
            return report_text
            
        except Exception as e:
            self.logger.error(f"生成绩效报告失败: {e}")
            return "报告生成失败"
    
    def plot_performance_charts(self, portfolio_data: pd.DataFrame, 
                              benchmark_data: pd.DataFrame = None,
                              save_path: str = None) -> None:
        """绘制绩效图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('投资组合绩效分析图表', fontsize=16)
            
            portfolio_prices = portfolio_data['close'] if 'close' in portfolio_data.columns else portfolio_data.iloc[:, 0]
            portfolio_returns = portfolio_prices.pct_change().dropna()
            
            # 1. 累计收益曲线
            cumulative_returns = (1 + portfolio_returns).cumprod()
            axes[0, 0].plot(cumulative_returns.index, cumulative_returns.values, label='投资组合', linewidth=2)
            
            if benchmark_data is not None:
                benchmark_prices = benchmark_data['close'] if 'close' in benchmark_data.columns else benchmark_data.iloc[:, 0]
                benchmark_returns = benchmark_prices.pct_change().dropna()
                benchmark_cumulative = (1 + benchmark_returns).cumprod()
                axes[0, 0].plot(benchmark_cumulative.index, benchmark_cumulative.values, label='基准', linewidth=2)
            
            axes[0, 0].set_title('累计收益曲线')
            axes[0, 0].set_ylabel('累计收益')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 回撤曲线
            cumulative = (1 + portfolio_returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            
            axes[0, 1].fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
            axes[0, 1].plot(drawdown.index, drawdown.values, color='red', linewidth=1)
            axes[0, 1].set_title('回撤曲线')
            axes[0, 1].set_ylabel('回撤比例')
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 收益率分布
            axes[1, 0].hist(portfolio_returns * 100, bins=50, alpha=0.7, density=True)
            axes[1, 0].axvline(portfolio_returns.mean() * 100, color='red', linestyle='--', label=f'均值: {portfolio_returns.mean() * 100:.2f}%')
            axes[1, 0].set_title('收益率分布')
            axes[1, 0].set_xlabel('日收益率 (%)')
            axes[1, 0].set_ylabel('密度')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 滚动夏普比率
            rolling_sharpe = portfolio_returns.rolling(window=60).apply(
                lambda x: self.risk_metrics.sharpe_ratio(x) if len(x) == 60 else np.nan
            )
            axes[1, 1].plot(rolling_sharpe.index, rolling_sharpe.values, linewidth=2)
            axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            axes[1, 1].set_title('60日滚动夏普比率')
            axes[1, 1].set_ylabel('夏普比率')
            axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"绩效图表已保存到: {save_path}")
            
            plt.show()
            
        except Exception as e:
            self.logger.error(f"绘制绩效图表失败: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级技术分析策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from analysis.technical_indicators import TechnicalIndicators
from utils.logger import get_logger


class KDJStrategy(BaseStrategy):
    """KDJ随机指标策略"""

    def __init__(self, name="KDJ策略", k_period=9, d_period=3, overbought=80, oversold=20):
        super().__init__(name)
        self.k_period = k_period
        self.d_period = d_period
        self.overbought = overbought
        self.oversold = oversold
        self.ti = TechnicalIndicators()
        self.logger = get_logger("KDJStrategy")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成KDJ交易信号"""
        try:
            signals = data.copy()

            # 计算KDJ指标
            k, d, j = self.ti.kdj(data['high'], data['low'], data['close'],
                                  self.k_period, self.d_period)

            signals['kdj_k'] = k
            signals['kdj_d'] = d
            signals['kdj_j'] = j

            # 生成交易信号
            signals['signal'] = 0
            signals['position'] = 0

            # 买入信号：K线从下方穿越D线，且都在超卖区域
            buy_condition = (
                (signals['kdj_k'] > signals['kdj_d']) &
                (signals['kdj_k'].shift(1) <= signals['kdj_d'].shift(1)) &
                (signals['kdj_k'] < self.oversold) &
                (signals['kdj_d'] < self.oversold)
            )

            # 卖出信号：K线从上方穿越D线，且都在超买区域
            sell_condition = (
                (signals['kdj_k'] < signals['kdj_d']) &
                (signals['kdj_k'].shift(1) >= signals['kdj_d'].shift(1)) &
                (signals['kdj_k'] > self.overbought) &
                (signals['kdj_d'] > self.overbought)
            )

            signals.loc[buy_condition, 'signal'] = 1
            signals.loc[sell_condition, 'signal'] = -1

            # 计算持仓
            signals['position'] = signals['signal'].fillna(0).cumsum()
            signals['position'] = signals['position'].clip(-1, 1)

            self.logger.info(f"KDJ策略信号生成完成，买入信号: {buy_condition.sum()}, 卖出信号: {sell_condition.sum()}")
            return signals

        except Exception as e:
            self.logger.error(f"KDJ策略信号生成失败: {e}")
            return data

    def calculate_position_size(self, signal: Dict, current_price: float, available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基础仓位计算
            base_position = available_capital * 0.1  # 10%仓位
            quantity = int(base_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': 'KDJ技术策略',
            'indicators': ['KDJ', 'Volume', 'Price'],
            'signal_conditions': {
                'buy': 'K线上穿D线且J值超卖',
                'sell': 'K线下穿D线且J值超买'
            },
            'risk_management': {
                'position_size': '10%仓位',
                'stop_loss': '基于ATR动态止损'
            }
        })
        return info


class BollingerBandsStrategy(BaseStrategy):
    """布林带策略"""

    def __init__(self, name="高级布林带策略", period=20, std_dev=2, rsi_period=14):
        super().__init__(name)
        self.period = period
        self.std_dev = std_dev
        self.rsi_period = rsi_period
        self.ti = TechnicalIndicators()
        self.logger = get_logger("BollingerBandsStrategy")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成布林带交易信号"""
        try:
            signals = data.copy()

            # 计算布林带
            bb_upper, bb_middle, bb_lower = self.ti.bollinger_bands(
                data['close'], self.period, self.std_dev
            )

            signals['bb_upper'] = bb_upper
            signals['bb_middle'] = bb_middle
            signals['bb_lower'] = bb_lower

            # 计算RSI作为辅助指标
            signals['rsi'] = self.ti.rsi(data['close'], self.rsi_period)

            # 计算布林带宽度
            signals['bb_width'] = (bb_upper - bb_lower) / bb_middle

            # 生成交易信号
            signals['signal'] = 0
            signals['position'] = 0

            # 买入信号：价格触及下轨且RSI超卖
            buy_condition = (
                (signals['close'] <= signals['bb_lower']) &
                (signals['rsi'] < 30) &
                (signals['bb_width'] > signals['bb_width'].rolling(10).mean())
            )

            # 卖出信号：价格触及上轨且RSI超买
            sell_condition = (
                (signals['close'] >= signals['bb_upper']) &
                (signals['rsi'] > 70) &
                (signals['bb_width'] > signals['bb_width'].rolling(10).mean())
            )

            # 止损信号：价格跌破中轨
            stop_loss_condition = (
                (signals['position'].shift(1) > 0) &
                (signals['close'] < signals['bb_middle'])
            )

            signals.loc[buy_condition, 'signal'] = 1
            signals.loc[sell_condition | stop_loss_condition, 'signal'] = -1

            # 计算持仓
            signals['position'] = signals['signal'].fillna(0).cumsum()
            signals['position'] = signals['position'].clip(-1, 1)

            self.logger.info(f"布林带策略信号生成完成，买入信号: {buy_condition.sum()}, 卖出信号: {sell_condition.sum()}")
            return signals

        except Exception as e:
            self.logger.error(f"布林带策略信号生成失败: {e}")
            return data

    def calculate_position_size(self, signal: Dict, current_price: float, available_capital: float) -> int:
        """计算仓位大小"""
        try:
            base_position = available_capital * 0.1
            quantity = int(base_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '高级布林带策略',
            'indicators': ['Bollinger Bands', 'RSI', 'BB Width'],
            'signal_conditions': {
                'buy': '价格触及下轨且RSI超卖且带宽扩张',
                'sell': '价格触及上轨且RSI超买且带宽扩张'
            },
            'risk_management': {
                'position_size': '10%仓位',
                'stop_loss': '价格跌破中轨止损'
            }
        })
        return info


class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""

    def __init__(self, name="均值回归策略", lookback_period=20, entry_threshold=2.0, exit_threshold=0.5):
        super().__init__(name)
        self.lookback_period = lookback_period
        self.entry_threshold = entry_threshold
        self.exit_threshold = exit_threshold
        self.ti = TechnicalIndicators()
        self.logger = get_logger("MeanReversionStrategy")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成均值回归交易信号"""
        try:
            signals = data.copy()

            # 计算移动平均和标准差
            signals['sma'] = self.ti.sma(data['close'], self.lookback_period)
            signals['std'] = data['close'].rolling(self.lookback_period).std()

            # 计算Z-Score
            signals['z_score'] = (signals['close'] - signals['sma']) / signals['std']

            # 计算RSI
            signals['rsi'] = self.ti.rsi(data['close'])

            # 生成交易信号
            signals['signal'] = 0
            signals['position'] = 0

            # 买入信号：价格显著低于均值（超卖）
            buy_condition = (
                (signals['z_score'] < -self.entry_threshold) &
                (signals['rsi'] < 30)
            )

            # 卖出信号：价格显著高于均值（超买）
            sell_condition = (
                (signals['z_score'] > self.entry_threshold) &
                (signals['rsi'] > 70)
            )

            # 平仓信号：价格回归到均值附近
            close_long_condition = (
                (signals['position'].shift(1) > 0) &
                (signals['z_score'] > -self.exit_threshold)
            )

            close_short_condition = (
                (signals['position'].shift(1) < 0) &
                (signals['z_score'] < self.exit_threshold)
            )

            signals.loc[buy_condition, 'signal'] = 1
            signals.loc[sell_condition, 'signal'] = -1
            signals.loc[close_long_condition | close_short_condition, 'signal'] = 0

            # 计算持仓
            current_position = 0
            positions = []

            for i, signal in enumerate(signals['signal']):
                if signal == 1:
                    current_position = 1
                elif signal == -1:
                    current_position = -1
                elif signal == 0 and current_position != 0:
                    current_position = 0
                positions.append(current_position)

            signals['position'] = positions

            self.logger.info(f"均值回归策略信号生成完成，买入信号: {buy_condition.sum()}, 卖出信号: {sell_condition.sum()}")
            return signals

        except Exception as e:
            self.logger.error(f"均值回归策略信号生成失败: {e}")
            return data

    def calculate_position_size(self, signal: Dict, current_price: float, available_capital: float) -> int:
        """计算仓位大小"""
        try:
            base_position = available_capital * 0.1
            quantity = int(base_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '均值回归策略',
            'indicators': ['SMA', 'Z-Score', 'RSI', 'Standard Deviation'],
            'signal_conditions': {
                'buy': f'Z-Score < -{self.entry_threshold} 且 RSI < 30',
                'sell': f'Z-Score > {self.entry_threshold} 且 RSI > 70'
            },
            'risk_management': {
                'position_size': '10%仓位',
                'exit_threshold': f'Z-Score回归到±{self.exit_threshold}范围内'
            }
        })
        return info


class BreakoutStrategy(BaseStrategy):
    """突破策略"""

    def __init__(self, name="突破策略", lookback_period=20, volume_threshold=1.5, atr_period=14):
        super().__init__(name)
        self.lookback_period = lookback_period
        self.volume_threshold = volume_threshold
        self.atr_period = atr_period
        self.ti = TechnicalIndicators()
        self.logger = get_logger("BreakoutStrategy")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成突破交易信号"""
        try:
            signals = data.copy()

            # 计算支撑阻力位
            signals['resistance'] = data['high'].rolling(self.lookback_period).max()
            signals['support'] = data['low'].rolling(self.lookback_period).min()

            # 计算ATR用于止损
            signals['atr'] = self.ti.atr(data['high'], data['low'], data['close'], self.atr_period)

            # 计算成交量均值
            signals['volume_ma'] = data['volume'].rolling(self.lookback_period).mean()

            # 生成交易信号
            signals['signal'] = 0
            signals['position'] = 0

            # 向上突破信号
            upward_breakout = (
                (signals['close'] > signals['resistance'].shift(1)) &
                (signals['volume'] > signals['volume_ma'] * self.volume_threshold) &
                (signals['close'] > signals['open'])  # 阳线
            )

            # 向下突破信号
            downward_breakout = (
                (signals['close'] < signals['support'].shift(1)) &
                (signals['volume'] > signals['volume_ma'] * self.volume_threshold) &
                (signals['close'] < signals['open'])  # 阴线
            )

            # 止损信号
            stop_loss_long = (
                (signals['position'].shift(1) > 0) &
                (signals['close'] < signals['close'].shift(1) - 2 * signals['atr'])
            )

            stop_loss_short = (
                (signals['position'].shift(1) < 0) &
                (signals['close'] > signals['close'].shift(1) + 2 * signals['atr'])
            )

            signals.loc[upward_breakout, 'signal'] = 1
            signals.loc[downward_breakout, 'signal'] = -1
            signals.loc[stop_loss_long | stop_loss_short, 'signal'] = 0

            # 计算持仓
            current_position = 0
            positions = []

            for i, signal in enumerate(signals['signal']):
                if signal == 1:
                    current_position = 1
                elif signal == -1:
                    current_position = -1
                elif signal == 0:
                    current_position = 0
                positions.append(current_position)

            signals['position'] = positions

            self.logger.info(f"突破策略信号生成完成，向上突破: {upward_breakout.sum()}, 向下突破: {downward_breakout.sum()}")
            return signals

        except Exception as e:
            self.logger.error(f"突破策略信号生成失败: {e}")
            return data

    def calculate_position_size(self, signal: Dict, current_price: float, available_capital: float) -> int:
        """计算仓位大小"""
        try:
            base_position = available_capital * 0.1
            quantity = int(base_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '突破策略',
            'indicators': ['Support/Resistance', 'ATR', 'Volume', 'Price Action'],
            'signal_conditions': {
                'buy': f'向上突破阻力位且成交量放大{self.volume_threshold}倍',
                'sell': f'向下突破支撑位且成交量放大{self.volume_threshold}倍'
            },
            'risk_management': {
                'position_size': '10%仓位',
                'stop_loss': f'基于{self.atr_period}期ATR的2倍止损'
            }
        })
        return info


class TrendFollowingStrategy(BaseStrategy):
    """趋势跟踪策略"""

    def __init__(self, name="趋势跟踪策略", fast_period=12, slow_period=26, signal_period=9):
        super().__init__(name)
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.ti = TechnicalIndicators()
        self.logger = get_logger("TrendFollowingStrategy")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成趋势跟踪交易信号"""
        try:
            signals = data.copy()

            # 计算MACD
            macd, macd_signal, macd_histogram = self.ti.macd(
                data['close'], self.fast_period, self.slow_period, self.signal_period
            )

            signals['macd'] = macd
            signals['macd_signal'] = macd_signal
            signals['macd_histogram'] = macd_histogram

            # 计算ADX判断趋势强度
            signals['adx'] = self.ti.adx(data['high'], data['low'], data['close'])

            # 计算EMA
            signals['ema_fast'] = self.ti.ema(data['close'], self.fast_period)
            signals['ema_slow'] = self.ti.ema(data['close'], self.slow_period)

            # 生成交易信号
            signals['signal'] = 0
            signals['position'] = 0

            # 买入信号：MACD金叉且趋势强劲
            buy_condition = (
                (signals['macd'] > signals['macd_signal']) &
                (signals['macd'].shift(1) <= signals['macd_signal'].shift(1)) &
                (signals['adx'] > 25) &
                (signals['ema_fast'] > signals['ema_slow'])
            )

            # 卖出信号：MACD死叉且趋势强劲
            sell_condition = (
                (signals['macd'] < signals['macd_signal']) &
                (signals['macd'].shift(1) >= signals['macd_signal'].shift(1)) &
                (signals['adx'] > 25) &
                (signals['ema_fast'] < signals['ema_slow'])
            )

            # 平仓信号：趋势减弱
            close_condition = (signals['adx'] < 20)

            signals.loc[buy_condition, 'signal'] = 1
            signals.loc[sell_condition, 'signal'] = -1
            signals.loc[close_condition, 'signal'] = 0

            # 计算持仓
            current_position = 0
            positions = []

            for i, signal in enumerate(signals['signal']):
                if signal == 1:
                    current_position = 1
                elif signal == -1:
                    current_position = -1
                elif signal == 0:
                    current_position = 0
                positions.append(current_position)

            signals['position'] = positions

            self.logger.info(f"趋势跟踪策略信号生成完成，买入信号: {buy_condition.sum()}, 卖出信号: {sell_condition.sum()}")
            return signals

        except Exception as e:
            self.logger.error(f"趋势跟踪策略信号生成失败: {e}")
            return data

    def calculate_position_size(self, signal: Dict, current_price: float, available_capital: float) -> int:
        """计算仓位大小"""
        try:
            base_position = available_capital * 0.1
            quantity = int(base_position / current_price / 100) * 100
            return max(100, quantity)
        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '趋势跟踪策略',
            'indicators': ['MACD', 'ADX', 'EMA Fast', 'EMA Slow'],
            'signal_conditions': {
                'buy': f'MACD金叉且ADX>25且快线({self.fast_period})>慢线({self.slow_period})',
                'sell': f'MACD死叉且ADX>25且快线({self.fast_period})<慢线({self.slow_period})'
            },
            'risk_management': {
                'position_size': '10%仓位',
                'exit_condition': 'ADX<20时平仓（趋势减弱）'
            }
        })
        return info
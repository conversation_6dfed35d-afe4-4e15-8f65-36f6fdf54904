{"model_config": {"type": "classification", "class_name": "RandomForestClassifier", "params": {"n_estimators": 100, "max_depth": 10, "min_samples_split": 5, "random_state": 42}}, "feature_names": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7", "feature_8", "feature_9"], "save_time": "2025-05-25T12:51:55.088917", "model_version": "1.0", "metadata": {"is_fitted": true, "feature_count": 10, "supports_probability": true, "supports_feature_importance": true}}
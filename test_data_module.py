#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模块测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.collectors.akshare_collector import AKShareCollector
from data.database.manager import DatabaseManager
from utils.logger import setup_logger


def test_akshare_collector():
    """测试AKShare数据采集器"""
    print("=" * 50)
    print("测试AKShare数据采集器")
    print("=" * 50)
    
    # 创建采集器
    collector = AKShareCollector()
    
    # 测试连接
    print("1. 测试连接...")
    if collector.connect():
        print("✓ 连接成功")
    else:
        print("✗ 连接失败")
        return False
    
    # 测试获取股票列表
    print("\n2. 测试获取股票列表...")
    try:
        stock_list = collector.get_stock_list()
        if not stock_list.empty:
            print(f"✓ 获取股票列表成功，共 {len(stock_list)} 只股票")
            print("前5只股票:")
            print(stock_list.head())
        else:
            print("✗ 股票列表为空")
    except Exception as e:
        print(f"✗ 获取股票列表失败: {e}")
    
    # 测试获取历史数据
    print("\n3. 测试获取历史数据...")
    try:
        # 获取平安银行的历史数据
        data = collector.get_stock_data("000001", start_date="2024-01-01", end_date="2024-01-31")
        if not data.empty:
            print(f"✓ 获取历史数据成功，共 {len(data)} 条记录")
            print("前5条数据:")
            print(data.head())
        else:
            print("✗ 历史数据为空")
    except Exception as e:
        print(f"✗ 获取历史数据失败: {e}")
    
    # 测试获取实时数据
    print("\n4. 测试获取实时数据...")
    try:
        realtime_data = collector.get_realtime_data(["000001", "000002"])
        if not realtime_data.empty:
            print(f"✓ 获取实时数据成功，共 {len(realtime_data)} 只股票")
            print("实时数据:")
            print(realtime_data)
        else:
            print("✗ 实时数据为空")
    except Exception as e:
        print(f"✗ 获取实时数据失败: {e}")
    
    collector.disconnect()
    return True


def test_database_manager():
    """测试数据库管理器"""
    print("\n" + "=" * 50)
    print("测试数据库管理器")
    print("=" * 50)
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        print("✓ 数据库管理器创建成功")
        
        # 测试保存股票信息
        print("\n1. 测试保存股票信息...")
        import pandas as pd
        
        test_stocks = pd.DataFrame([
            {'symbol': '000001', 'name': '平安银行', 'market': 'SZ', 'industry': '银行'},
            {'symbol': '000002', 'name': '万科A', 'market': 'SZ', 'industry': '房地产'}
        ])
        
        if db_manager.save_stock_info(test_stocks):
            print("✓ 保存股票信息成功")
        else:
            print("✗ 保存股票信息失败")
        
        # 测试获取股票列表
        print("\n2. 测试获取股票列表...")
        stock_list = db_manager.get_stock_list()
        if not stock_list.empty:
            print(f"✓ 获取股票列表成功，共 {len(stock_list)} 只股票")
            print(stock_list)
        else:
            print("✗ 股票列表为空")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库管理器测试失败: {e}")
        return False


def main():
    """主函数"""
    print("量化交易系统 - 数据模块测试")
    print("开始时间:", pd.Timestamp.now())
    
    # 设置日志
    logger = setup_logger()
    
    # 测试AKShare采集器
    akshare_success = test_akshare_collector()
    
    # 测试数据库管理器
    db_success = test_database_manager()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"AKShare采集器: {'✓ 通过' if akshare_success else '✗ 失败'}")
    print(f"数据库管理器: {'✓ 通过' if db_success else '✗ 失败'}")
    
    if akshare_success and db_success:
        print("\n🎉 所有测试通过！数据模块工作正常。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    import pandas as pd
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试量化分析功能
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis.quantitative_analysis import QuantitativeAnalysis
from utils.logger import get_logger


def test_basic_calculations():
    """测试基础计算功能"""
    logger = get_logger("TestQuantitativeAnalysis")
    
    print("=" * 60)
    print("量化分析基础功能测试")
    print("=" * 60)
    
    try:
        # 创建量化分析实例
        qa = QuantitativeAnalysis()
        
        # 生成示例价格数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        base_price = 100
        returns = np.random.normal(0.001, 0.02, 100)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        price_series = pd.Series(prices, index=dates)
        
        print(f"生成价格数据: {len(price_series)} 个数据点")
        print(f"价格范围: {price_series.min():.2f} - {price_series.max():.2f}")
        
        # 测试收益率计算
        simple_returns = qa.calculate_returns(price_series, 'simple')
        log_returns = qa.calculate_returns(price_series, 'log')
        
        print(f"\n收益率计算:")
        print(f"  简单收益率均值: {simple_returns.mean():.6f}")
        print(f"  对数收益率均值: {log_returns.mean():.6f}")
        print(f"  简单收益率标准差: {simple_returns.std():.6f}")
        print(f"  对数收益率标准差: {log_returns.std():.6f}")
        
        # 测试波动率计算
        volatility = qa.calculate_volatility(simple_returns, window=20, annualized=True)
        print(f"\n波动率计算:")
        print(f"  年化波动率最新值: {volatility.iloc[-1]:.4f}")
        print(f"  年化波动率均值: {volatility.mean():.4f}")
        
        # 测试夏普比率
        sharpe_ratio = qa.calculate_sharpe_ratio(simple_returns)
        print(f"\n夏普比率: {sharpe_ratio:.4f}")
        
        # 测试最大回撤
        max_drawdown_info = qa.calculate_max_drawdown(price_series)
        print(f"\n最大回撤分析:")
        print(f"  最大回撤: {max_drawdown_info['max_drawdown']:.4f}")
        print(f"  回撤持续时间: {max_drawdown_info.get('drawdown_duration', 0)} 天")
        
        # 测试VaR和CVaR
        var_5 = qa.calculate_var(simple_returns, 0.05)
        cvar_5 = qa.calculate_cvar(simple_returns, 0.05)
        print(f"\n风险价值:")
        print(f"  VaR (5%): {var_5:.4f}")
        print(f"  CVaR (5%): {cvar_5:.4f}")
        
        # 测试其他比率
        sortino_ratio = qa.calculate_sortino_ratio(simple_returns)
        calmar_ratio = qa.calculate_calmar_ratio(simple_returns, price_series)
        
        print(f"\n其他风险指标:")
        print(f"  索提诺比率: {sortino_ratio:.4f}")
        print(f"  卡玛比率: {calmar_ratio:.4f}")
        
        print("\n✅ 基础计算功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"基础计算功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_portfolio_analysis():
    """测试投资组合分析功能"""
    logger = get_logger("TestPortfolioAnalysis")
    
    print("\n" + "=" * 60)
    print("投资组合分析功能测试")
    print("=" * 60)
    
    try:
        qa = QuantitativeAnalysis()
        
        # 生成多只股票的示例数据
        symbols = ['STOCK_A', 'STOCK_B', 'STOCK_C', 'STOCK_D', 'STOCK_E']
        market_data = qa.generate_sample_market_data(symbols, days=252)
        
        print(f"生成市场数据: {len(symbols)} 只股票, {len(market_data)} 个交易日")
        print(f"股票代码: {', '.join(symbols)}")
        
        # 计算相关性矩阵
        correlation_matrix = qa.calculate_correlation_matrix(market_data)
        print(f"\n相关性矩阵计算完成: {correlation_matrix.shape}")
        
        # 显示部分相关性数据
        print("相关性矩阵 (前3x3):")
        print(correlation_matrix.iloc[:3, :3].round(3))
        
        # 进行主成分分析
        pca_results = qa.perform_pca_analysis(market_data, n_components=3)
        
        if pca_results:
            print(f"\n主成分分析:")
            print(f"  主成分数量: {pca_results['n_components']}")
            
            explained_variance = pca_results['explained_variance_ratio']
            cumulative_variance = pca_results['cumulative_variance_ratio']
            
            for i, (exp_var, cum_var) in enumerate(zip(explained_variance, cumulative_variance)):
                print(f"  PC{i+1}: 解释方差 {exp_var:.3f} ({exp_var*100:.1f}%), 累计 {cum_var:.3f} ({cum_var*100:.1f}%)")
        
        # 进行风险分析
        risk_analysis = qa.perform_risk_analysis(market_data)
        
        print(f"\n风险分析结果:")
        print("-" * 50)
        
        for symbol, metrics in risk_analysis.items():
            print(f"{symbol}:")
            print(f"  年化收益率: {metrics.get('annual_return', 0)*100:.2f}%")
            print(f"  年化波动率: {metrics.get('annual_volatility', 0)*100:.2f}%")
            print(f"  夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
            print(f"  最大回撤: {metrics.get('max_drawdown', 0)*100:.2f}%")
            print(f"  VaR (5%): {metrics.get('var_5', 0)*100:.2f}%")
            print()
        
        print("✅ 投资组合分析功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"投资组合分析功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_advanced_metrics():
    """测试高级指标计算"""
    logger = get_logger("TestAdvancedMetrics")
    
    print("\n" + "=" * 60)
    print("高级指标计算测试")
    print("=" * 60)
    
    try:
        qa = QuantitativeAnalysis()
        
        # 生成股票和基准数据
        symbols = ['STOCK_A', 'BENCHMARK']
        market_data = qa.generate_sample_market_data(symbols, days=252)
        
        stock_prices = market_data['STOCK_A']
        benchmark_prices = market_data['BENCHMARK']
        
        stock_returns = qa.calculate_returns(stock_prices)
        benchmark_returns = qa.calculate_returns(benchmark_prices)
        
        print(f"生成数据: 股票和基准, {len(market_data)} 个交易日")
        
        # 计算贝塔系数
        beta = qa.calculate_beta(stock_returns, benchmark_returns)
        print(f"\n贝塔系数: {beta:.3f}")
        
        # 计算信息比率
        information_ratio = qa.calculate_information_ratio(stock_returns, benchmark_returns)
        print(f"信息比率: {information_ratio:.3f}")
        
        # 计算特雷诺比率
        treynor_ratio = qa.calculate_treynor_ratio(stock_returns, beta)
        print(f"特雷诺比率: {treynor_ratio:.3f}")
        
        # 计算索提诺比率
        sortino_ratio = qa.calculate_sortino_ratio(stock_returns)
        print(f"索提诺比率: {sortino_ratio:.3f}")
        
        # 计算卡玛比率
        calmar_ratio = qa.calculate_calmar_ratio(stock_returns, stock_prices)
        print(f"卡玛比率: {calmar_ratio:.3f}")
        
        # 综合风险分析（包含基准）
        portfolio_data = pd.DataFrame({
            'STOCK_A': stock_prices,
            'STOCK_B': market_data['BENCHMARK']  # 使用基准作为第二只股票
        })
        
        risk_analysis = qa.perform_risk_analysis(portfolio_data, benchmark_prices)
        
        print(f"\n综合风险分析 (含基准比较):")
        print("-" * 40)
        
        for symbol, metrics in risk_analysis.items():
            print(f"{symbol}:")
            print(f"  年化收益率: {metrics.get('annual_return', 0)*100:.2f}%")
            print(f"  夏普比率: {metrics.get('sharpe_ratio', 0):.3f}")
            
            if 'beta' in metrics:
                print(f"  贝塔系数: {metrics['beta']:.3f}")
            if 'treynor_ratio' in metrics:
                print(f"  特雷诺比率: {metrics['treynor_ratio']:.3f}")
            if 'information_ratio' in metrics:
                print(f"  信息比率: {metrics['information_ratio']:.3f}")
            print()
        
        print("✅ 高级指标计算测试通过")
        return True
        
    except Exception as e:
        logger.error(f"高级指标计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始量化分析功能测试...")
    
    success = True
    
    # 测试基础计算功能
    if not test_basic_calculations():
        success = False
    
    # 测试投资组合分析
    if not test_portfolio_analysis():
        success = False
    
    # 测试高级指标
    if not test_advanced_metrics():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 量化分析功能测试全部通过！")
        print("\n功能特点:")
        print("✅ 完整的收益率和风险指标计算")
        print("✅ 投资组合相关性分析")
        print("✅ 主成分分析 (PCA)")
        print("✅ 多种风险调整收益指标")
        print("✅ VaR和CVaR风险度量")
        print("✅ 基准比较分析")
        
        print("\n支持的指标:")
        print("• 夏普比率、索提诺比率、卡玛比率")
        print("• 贝塔系数、信息比率、特雷诺比率")
        print("• 最大回撤、VaR、CVaR")
        print("• 相关性矩阵、主成分分析")
        
        print("\n下一步可以:")
        print("1. 集成到分析中心GUI")
        print("2. 添加更多量化策略")
        print("3. 实现投资组合优化")
        print("4. 连接真实市场数据")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 60)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据下载组件
"""

import sys
import random
import time
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget,
    QTableWidget, QTableWidgetItem, QCheckBox, QSpinBox,
    QFileDialog, QMessageBox, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate, QThread
from PyQt5.QtGui import QFont, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from data.collectors.akshare_collector import AKShareCollector


class BatchDownloadThread(QThread):
    """批量下载线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    item_completed = pyqtSignal(str, bool, str)  # symbol, success, message
    finished_signal = pyqtSignal(bool, str)

    def __init__(self, download_tasks):
        super().__init__()
        self.download_tasks = download_tasks
        self.collector = AKShareCollector()
        self.is_running = True

    def run(self):
        """运行批量下载"""
        try:
            total_tasks = len(self.download_tasks)
            completed = 0
            success_count = 0

            self.status_updated.emit(f"开始批量下载，共 {total_tasks} 个任务...")

            for i, task in enumerate(self.download_tasks):
                if not self.is_running:
                    break

                symbol = task['symbol']
                start_date = task['start_date']
                end_date = task['end_date']
                data_type = task['data_type']

                try:
                    self.status_updated.emit(f"正在下载 {symbol} ({data_type})...")

                    # 模拟下载过程
                    time.sleep(random.uniform(0.5, 2.0))

                    # 实际下载数据
                    if data_type == "日线数据":
                        data = self.collector.get_stock_data(symbol, start_date, end_date)
                    elif data_type == "基本信息":
                        # 模拟基本信息下载
                        data = {"symbol": symbol, "name": f"股票{symbol}", "industry": "制造业"}
                    else:
                        # 其他数据类型
                        data = None

                    if data is not None:
                        success_count += 1
                        self.item_completed.emit(symbol, True, f"成功下载 {symbol}")
                    else:
                        self.item_completed.emit(symbol, False, f"下载失败 {symbol}")

                except Exception as e:
                    self.item_completed.emit(symbol, False, f"下载错误: {str(e)}")

                completed += 1
                progress = int((completed / total_tasks) * 100)
                self.progress_updated.emit(progress)

            if self.is_running:
                self.finished_signal.emit(True, f"批量下载完成，成功 {success_count}/{total_tasks}")
            else:
                self.finished_signal.emit(False, "用户取消下载")

        except Exception as e:
            self.finished_signal.emit(False, f"批量下载失败: {str(e)}")

    def stop(self):
        """停止下载"""
        self.is_running = False


class DownloadTaskManager(QWidget):
    """下载任务管理器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DownloadTaskManager")
        self.download_tasks = []
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 任务配置区域
        config_group = QGroupBox("任务配置")
        config_layout = QGridLayout(config_group)

        # 股票代码列表
        股票代码_label = QLabel("股票代码:")
        股票代码_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(股票代码_label, 0, 0)
        self.symbols_input = QTextEdit()
        self.symbols_input.setMaximumHeight(80)
        self.symbols_input.setPlaceholderText("输入股票代码，每行一个，例如:\n000001\n000002\n600000")
        config_layout.addWidget(self.symbols_input, 0, 1, 2, 1)

        # 从文件导入
        import_btn = QPushButton("从文件导入")
        import_btn.clicked.connect(self.import_symbols_from_file)
        config_layout.addWidget(import_btn, 0, 2)

        # 获取全市场股票
        all_stocks_btn = QPushButton("获取全市场")
        all_stocks_btn.clicked.connect(self.get_all_stocks)
        config_layout.addWidget(all_stocks_btn, 1, 2)

        # 日期范围
        开始日期_label = QLabel("开始日期:")
        开始日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(开始日期_label, 2, 0)
        self.batch_start_date = QDateEdit()
        self.batch_start_date.setDate(QDate.currentDate().addDays(-365))
        self.batch_start_date.setCalendarPopup(True)
        config_layout.addWidget(self.batch_start_date, 2, 1)

        结束日期_label = QLabel("结束日期:")
        结束日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(结束日期_label, 3, 0)
        self.batch_end_date = QDateEdit()
        self.batch_end_date.setDate(QDate.currentDate())
        self.batch_end_date.setCalendarPopup(True)
        config_layout.addWidget(self.batch_end_date, 3, 1)

        # 数据类型
        数据类型_label = QLabel("数据类型:")
        数据类型_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(数据类型_label, 4, 0)
        self.batch_data_type = QComboBox()
        self.batch_data_type.addItems([
            "日线数据", "周线数据", "月线数据",
            "基本信息", "财务数据", "实时行情"
        ])
        config_layout.addWidget(self.batch_data_type, 4, 1)

        # 高级选项
        并发数量_label = QLabel("并发数量:")
        并发数量_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(并发数量_label, 5, 0)
        self.concurrent_count = QSpinBox()
        self.concurrent_count.setRange(1, 10)
        self.concurrent_count.setValue(3)
        config_layout.addWidget(self.concurrent_count, 5, 1)

        # 重试次数
        重试次数_label = QLabel("重试次数:")
        重试次数_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        config_layout.addWidget(重试次数_label, 6, 0)
        self.retry_count = QSpinBox()
        self.retry_count.setRange(0, 5)
        self.retry_count.setValue(2)
        config_layout.addWidget(self.retry_count, 6, 1)

        layout.addWidget(config_group)

        # 控制按钮
        control_layout = QHBoxLayout()

        self.create_tasks_btn = QPushButton("创建任务")
        self.create_tasks_btn.clicked.connect(self.create_download_tasks)
        control_layout.addWidget(self.create_tasks_btn)

        self.start_batch_btn = QPushButton("开始批量下载")
        self.start_batch_btn.clicked.connect(self.start_batch_download)
        self.start_batch_btn.setEnabled(False)
        control_layout.addWidget(self.start_batch_btn)

        self.stop_batch_btn = QPushButton("停止下载")
        self.stop_batch_btn.clicked.connect(self.stop_batch_download)
        self.stop_batch_btn.setEnabled(False)
        control_layout.addWidget(self.stop_batch_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 任务列表
        tasks_group = QGroupBox("下载任务")
        tasks_group.setStyleSheet("QGroupBox { color: #ffffff !important; }")
        tasks_layout = QVBoxLayout(tasks_group)

        self.tasks_table = QTableWidget()
        self.tasks_table.setColumnCount(5)
        self.tasks_table.setHorizontalHeaderLabels([
            "股票代码", "数据类型", "日期范围", "状态", "结果"
        ])
        self.tasks_table.setAlternatingRowColors(True)

        # 设置任务表格背景为蓝色，确保白色文字可见
        self.tasks_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        tasks_layout.addWidget(self.tasks_table)

        layout.addWidget(tasks_group)

        # 进度信息
        progress_group = QGroupBox("下载进度")
        progress_group.setStyleSheet("QGroupBox { color: #ffffff !important; }")
        progress_layout = QVBoxLayout(progress_group)

        self.batch_progress = QProgressBar()
        progress_layout.addWidget(self.batch_progress)

        self.batch_status = QLabel("就绪")
        self.batch_status.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        progress_layout.addWidget(self.batch_status)

        layout.addWidget(progress_group)

    def import_symbols_from_file(self):
        """从文件导入股票代码"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入股票代码文件", "",
                "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*.*)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.symbols_input.setPlainText(content)
                    self.logger.info(f"从文件导入股票代码: {file_path}")

        except Exception as e:
            self.logger.error(f"导入股票代码文件失败: {e}")
            QMessageBox.critical(self, "错误", f"导入文件失败:\n{str(e)}")

    def get_all_stocks(self):
        """获取全市场股票代码"""
        try:
            # 模拟获取全市场股票
            sample_stocks = [
                "000001", "000002", "000858", "000876", "002415",
                "600000", "600036", "600519", "600887", "601318"
            ]

            symbols_text = "\n".join(sample_stocks)
            self.symbols_input.setPlainText(symbols_text)
            self.logger.info("获取全市场股票代码完成")

        except Exception as e:
            self.logger.error(f"获取全市场股票失败: {e}")
            QMessageBox.critical(self, "错误", f"获取全市场股票失败:\n{str(e)}")

    def create_download_tasks(self):
        """创建下载任务"""
        try:
            # 获取股票代码列表
            symbols_text = self.symbols_input.toPlainText().strip()
            if not symbols_text:
                QMessageBox.warning(self, "警告", "请输入股票代码")
                return

            symbols = [s.strip() for s in symbols_text.split('\n') if s.strip()]

            # 获取其他参数
            start_date = self.batch_start_date.date().toString("yyyy-MM-dd")
            end_date = self.batch_end_date.date().toString("yyyy-MM-dd")
            data_type = self.batch_data_type.currentText()

            # 创建任务列表
            self.download_tasks = []
            for symbol in symbols:
                task = {
                    'symbol': symbol,
                    'start_date': start_date,
                    'end_date': end_date,
                    'data_type': data_type,
                    'status': '等待中',
                    'result': ''
                }
                self.download_tasks.append(task)

            # 更新任务表格
            self.update_tasks_table()

            self.start_batch_btn.setEnabled(True)
            self.batch_status.setText(f"已创建 {len(self.download_tasks)} 个下载任务")
            self.logger.info(f"创建了 {len(self.download_tasks)} 个下载任务")

        except Exception as e:
            self.logger.error(f"创建下载任务失败: {e}")
            QMessageBox.critical(self, "错误", f"创建任务失败:\n{str(e)}")

    def update_tasks_table(self):
        """更新任务表格"""
        self.tasks_table.setRowCount(len(self.download_tasks))

        for row, task in enumerate(self.download_tasks):
            self.tasks_table.setItem(row, 0, QTableWidgetItem(task['symbol']))
            self.tasks_table.setItem(row, 1, QTableWidgetItem(task['data_type']))
            self.tasks_table.setItem(row, 2, QTableWidgetItem(f"{task['start_date']} 至 {task['end_date']}"))
            self.tasks_table.setItem(row, 3, QTableWidgetItem(task['status']))
            self.tasks_table.setItem(row, 4, QTableWidgetItem(task['result']))

        self.tasks_table.resizeColumnsToContents()

    def start_batch_download(self):
        """开始批量下载"""
        try:
            if not self.download_tasks:
                QMessageBox.warning(self, "警告", "没有下载任务")
                return

            self.start_batch_btn.setEnabled(False)
            self.stop_batch_btn.setEnabled(True)
            self.batch_progress.setValue(0)

            # 创建批量下载线程
            self.batch_thread = BatchDownloadThread(self.download_tasks)
            self.batch_thread.progress_updated.connect(self.batch_progress.setValue)
            self.batch_thread.status_updated.connect(self.batch_status.setText)
            self.batch_thread.item_completed.connect(self.on_item_completed)
            self.batch_thread.finished_signal.connect(self.on_batch_finished)
            self.batch_thread.start()

            self.logger.info("开始批量下载")

        except Exception as e:
            self.logger.error(f"开始批量下载失败: {e}")
            QMessageBox.critical(self, "错误", f"开始下载失败:\n{str(e)}")

    def stop_batch_download(self):
        """停止批量下载"""
        if hasattr(self, 'batch_thread') and self.batch_thread.isRunning():
            self.batch_thread.stop()
            self.batch_thread.wait()
            self.on_batch_finished(False, "用户取消下载")

    def on_item_completed(self, symbol, success, message):
        """单个任务完成"""
        # 更新任务状态
        for i, task in enumerate(self.download_tasks):
            if task['symbol'] == symbol:
                task['status'] = '完成' if success else '失败'
                task['result'] = message

                # 更新表格
                status_item = QTableWidgetItem(task['status'])
                result_item = QTableWidgetItem(task['result'])

                if success:
                    # 成功状态：绿色背景，白色文字
                    status_item.setBackground(QColor("#4CAF50"))
                    status_item.setForeground(QColor("#ffffff"))
                    result_item.setForeground(QColor("#ffffff"))
                else:
                    # 失败状态：红色背景，白色文字
                    status_item.setBackground(QColor("#F44336"))
                    status_item.setForeground(QColor("#ffffff"))
                    result_item.setForeground(QColor("#ffffff"))

                self.tasks_table.setItem(i, 3, status_item)
                self.tasks_table.setItem(i, 4, result_item)
                break

    def on_batch_finished(self, success, message):
        """批量下载完成"""
        self.start_batch_btn.setEnabled(True)
        self.stop_batch_btn.setEnabled(False)
        self.batch_status.setText(message)

        if success:
            self.batch_progress.setValue(100)

        self.logger.info(f"批量下载完成: {message}")


class EnhancedDownloadWidget(QWidget):
    """增强数据下载组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("EnhancedDownloadWidget")
        self.init_ui()
        self.logger.info("增强数据下载组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 单个下载标签页
        single_download_widget = self.create_single_download_tab()
        tab_widget.addTab(single_download_widget, "单个下载")

        # 批量下载标签页
        self.batch_manager = DownloadTaskManager()
        tab_widget.addTab(self.batch_manager, "批量下载")

        layout.addWidget(tab_widget)

    def create_single_download_tab(self):
        """创建单个下载标签页"""
        # 这里可以保留原有的单个下载功能
        widget = QWidget()
        layout = QVBoxLayout(widget)

        info_label = QLabel("单个下载功能")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #666666; padding: 20px;")
        layout.addWidget(info_label)

        return widget

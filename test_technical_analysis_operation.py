#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试技术分析操作功能
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_technical_analysis_operation():
    """测试技术分析操作"""
    print("🔍 测试技术分析操作...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 切换到技术分析标签页
        if hasattr(analysis_widget, 'tab_widget'):
            for i in range(analysis_widget.tab_widget.count()):
                tab_name = analysis_widget.tab_widget.tabText(i)
                if "技术分析" in tab_name:
                    analysis_widget.tab_widget.setCurrentIndex(i)
                    print("   ✅ 切换到技术分析标签页")
                    break
        
        # 处理事件
        app.processEvents()
        
        # 查找股票代码输入框
        from PyQt5.QtWidgets import QLineEdit
        line_edits = analysis_widget.findChildren(QLineEdit)
        
        stock_input = None
        for edit in line_edits:
            # 查找可能是股票代码输入的控件
            if hasattr(edit, 'placeholderText'):
                placeholder = edit.placeholderText().lower()
                if '股票' in placeholder or 'symbol' in placeholder or '代码' in placeholder:
                    stock_input = edit
                    break
        
        if not stock_input and line_edits:
            # 如果没找到特定的，就用第一个
            stock_input = line_edits[0]
        
        if stock_input:
            # 输入测试股票代码
            test_symbol = "000001.SZ"
            stock_input.setText(test_symbol)
            print(f"   ✅ 输入股票代码: {test_symbol}")
            
            # 查找分析按钮
            from PyQt5.QtWidgets import QPushButton
            buttons = analysis_widget.findChildren(QPushButton)
            
            analyze_button = None
            for button in buttons:
                if "分析" in button.text() and "技术" in button.text():
                    analyze_button = button
                    break
            
            if not analyze_button:
                # 查找包含"分析"的按钮
                for button in buttons:
                    if "分析" in button.text():
                        analyze_button = button
                        break
            
            if analyze_button:
                print(f"   ✅ 找到分析按钮: {analyze_button.text()}")
                
                # 模拟点击分析按钮
                analyze_button.click()
                print("   ✅ 点击分析按钮")
                
                # 处理事件，等待分析完成
                for _ in range(10):
                    app.processEvents()
                    QTimer.singleShot(100, lambda: None)
                
                print("   ✅ 技术分析操作完成")
                
                # 检查是否有结果显示
                from PyQt5.QtWidgets import QTableWidget
                tables = analysis_widget.findChildren(QTableWidget)
                
                if tables:
                    for table in tables:
                        if table.rowCount() > 0:
                            print(f"   ✅ 发现分析结果表格，行数: {table.rowCount()}")
                            break
                    else:
                        print("   ⚠️ 分析结果表格为空")
                else:
                    print("   ⚠️ 未找到结果表格")
            else:
                print("   ❌ 未找到分析按钮")
        else:
            print("   ❌ 未找到股票代码输入框")
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术分析操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indicator_calculation():
    """测试指标计算功能"""
    print("\n🔍 测试指标计算功能...")
    
    try:
        from analysis.technical_indicators import TechnicalIndicators
        import pandas as pd
        import numpy as np
        
        # 创建技术指标实例
        ti = TechnicalIndicators()
        
        # 创建更真实的测试数据
        dates = pd.date_range('2023-01-01', periods=252, freq='D')  # 一年的交易日
        np.random.seed(42)
        
        # 模拟股价走势
        returns = np.random.normal(0.001, 0.02, 252)  # 日收益率
        prices = [100]  # 起始价格
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 创建OHLCV数据
        test_data = pd.DataFrame({
            'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 252)
        }, index=dates)
        
        print("   ✅ 创建真实测试数据成功")
        
        # 计算所有技术指标
        indicators = ti.calculate_all_indicators(test_data)
        print(f"   ✅ 计算了 {len(indicators.columns) - len(test_data.columns)} 个技术指标")
        
        # 验证关键指标
        key_indicators = {
            'sma_5': '5日简单移动平均',
            'sma_20': '20日简单移动平均',
            'ema_12': '12日指数移动平均',
            'rsi_12': '12日相对强弱指数',
            'macd': 'MACD指标',
            'macd_signal': 'MACD信号线',
            'bb_upper': '布林带上轨',
            'bb_lower': '布林带下轨',
            'kdj_k': 'KDJ-K值',
            'kdj_d': 'KDJ-D值',
            'atr': '平均真实波幅',
            'obv': '能量潮指标'
        }
        
        for indicator, name in key_indicators.items():
            if indicator in indicators.columns:
                latest_value = indicators[indicator].iloc[-1]
                if pd.notna(latest_value):
                    print(f"   ✅ {name}: {latest_value:.4f}")
                else:
                    print(f"   ⚠️ {name}: 数据为空")
            else:
                print(f"   ❌ {name}: 指标缺失")
        
        # 验证指标的合理性
        print("\n   📊 指标合理性验证:")
        
        # RSI应该在0-100之间
        if 'rsi_12' in indicators.columns:
            rsi_values = indicators['rsi_12'].dropna()
            if len(rsi_values) > 0:
                rsi_min, rsi_max = rsi_values.min(), rsi_values.max()
                if 0 <= rsi_min <= rsi_max <= 100:
                    print(f"   ✅ RSI范围正常: {rsi_min:.2f} - {rsi_max:.2f}")
                else:
                    print(f"   ❌ RSI范围异常: {rsi_min:.2f} - {rsi_max:.2f}")
        
        # 移动平均线应该平滑
        if 'sma_20' in indicators.columns:
            sma_values = indicators['sma_20'].dropna()
            if len(sma_values) > 10:
                volatility = sma_values.std() / sma_values.mean()
                print(f"   ✅ SMA20波动率: {volatility:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 指标计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("技术分析操作功能测试")
    print("=" * 50)
    
    # 测试1: 技术分析操作
    operation_ok = test_technical_analysis_operation()
    
    # 测试2: 指标计算
    calculation_ok = test_indicator_calculation()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"技术分析操作: {'✅ 通过' if operation_ok else '❌ 失败'}")
    print(f"指标计算功能: {'✅ 通过' if calculation_ok else '❌ 失败'}")
    
    all_passed = operation_ok and calculation_ok
    
    if all_passed:
        print("\n🎉 技术分析操作功能测试通过！")
        return True
    else:
        print("\n❌ 技术分析操作功能测试失败，需要进一步完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

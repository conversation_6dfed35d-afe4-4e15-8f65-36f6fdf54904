#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险指标计算模块
"""

import pandas as pd
import numpy as np
from typing import Union, Dict, Any, Optional
from scipy import stats

from utils.logger import get_logger


class RiskMetrics:
    """风险指标计算类"""
    
    def __init__(self):
        self.logger = get_logger("RiskMetrics")
    
    @staticmethod
    def returns(prices: pd.Series) -> pd.Series:
        """计算收益率"""
        return prices.pct_change().dropna()
    
    @staticmethod
    def log_returns(prices: pd.Series) -> pd.Series:
        """计算对数收益率"""
        return np.log(prices / prices.shift(1)).dropna()
    
    @staticmethod
    def volatility(returns: pd.Series, periods: int = 252) -> float:
        """计算年化波动率"""
        return returns.std() * np.sqrt(periods)
    
    @staticmethod
    def sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.03, periods: int = 252) -> float:
        """计算夏普比率"""
        excess_returns = returns.mean() * periods - risk_free_rate
        volatility = RiskMetrics.volatility(returns, periods)
        return excess_returns / volatility if volatility != 0 else 0
    
    @staticmethod
    def sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.03, periods: int = 252) -> float:
        """计算索提诺比率"""
        excess_returns = returns.mean() * periods - risk_free_rate
        downside_returns = returns[returns < 0]
        downside_volatility = downside_returns.std() * np.sqrt(periods)
        return excess_returns / downside_volatility if downside_volatility != 0 else 0
    
    @staticmethod
    def max_drawdown(prices: pd.Series) -> Dict[str, Any]:
        """计算最大回撤"""
        # 计算累计收益
        cumulative = (1 + RiskMetrics.returns(prices)).cumprod()
        
        # 计算历史最高点
        running_max = cumulative.expanding().max()
        
        # 计算回撤
        drawdown = (cumulative - running_max) / running_max
        
        # 找到最大回撤
        max_dd = drawdown.min()
        max_dd_date = drawdown.idxmin()
        
        # 找到最大回撤开始和结束日期
        peak_date = running_max.loc[:max_dd_date].idxmax()
        
        # 找到回撤结束日期（回到峰值的日期）
        recovery_date = None
        peak_value = running_max.loc[peak_date]
        
        for date in cumulative.loc[max_dd_date:].index:
            if cumulative.loc[date] >= peak_value:
                recovery_date = date
                break
        
        return {
            'max_drawdown': max_dd,
            'peak_date': peak_date,
            'trough_date': max_dd_date,
            'recovery_date': recovery_date,
            'duration_days': (max_dd_date - peak_date).days if peak_date and max_dd_date else 0,
            'recovery_days': (recovery_date - max_dd_date).days if recovery_date and max_dd_date else None
        }
    
    @staticmethod
    def var(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算风险价值 (Value at Risk)"""
        return np.percentile(returns, confidence_level * 100)
    
    @staticmethod
    def cvar(returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算条件风险价值 (Conditional Value at Risk)"""
        var_threshold = RiskMetrics.var(returns, confidence_level)
        return returns[returns <= var_threshold].mean()
    
    @staticmethod
    def beta(returns: pd.Series, market_returns: pd.Series) -> float:
        """计算贝塔系数"""
        covariance = np.cov(returns, market_returns)[0][1]
        market_variance = np.var(market_returns)
        return covariance / market_variance if market_variance != 0 else 0
    
    @staticmethod
    def alpha(returns: pd.Series, market_returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算阿尔法系数"""
        beta_value = RiskMetrics.beta(returns, market_returns)
        portfolio_return = returns.mean() * 252
        market_return = market_returns.mean() * 252
        return portfolio_return - (risk_free_rate + beta_value * (market_return - risk_free_rate))
    
    @staticmethod
    def information_ratio(returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """计算信息比率"""
        excess_returns = returns - benchmark_returns
        tracking_error = excess_returns.std()
        return excess_returns.mean() / tracking_error if tracking_error != 0 else 0
    
    @staticmethod
    def calmar_ratio(returns: pd.Series, periods: int = 252) -> float:
        """计算卡尔马比率"""
        annual_return = returns.mean() * periods
        prices = (1 + returns).cumprod()
        max_dd = RiskMetrics.max_drawdown(prices)['max_drawdown']
        return annual_return / abs(max_dd) if max_dd != 0 else 0
    
    @staticmethod
    def downside_deviation(returns: pd.Series, target_return: float = 0, periods: int = 252) -> float:
        """计算下行偏差"""
        downside_returns = returns[returns < target_return]
        return downside_returns.std() * np.sqrt(periods)
    
    @staticmethod
    def upside_deviation(returns: pd.Series, target_return: float = 0, periods: int = 252) -> float:
        """计算上行偏差"""
        upside_returns = returns[returns > target_return]
        return upside_returns.std() * np.sqrt(periods)
    
    @staticmethod
    def skewness(returns: pd.Series) -> float:
        """计算偏度"""
        return stats.skew(returns.dropna())
    
    @staticmethod
    def kurtosis(returns: pd.Series) -> float:
        """计算峰度"""
        return stats.kurtosis(returns.dropna())
    
    @staticmethod
    def tail_ratio(returns: pd.Series) -> float:
        """计算尾部比率"""
        return np.percentile(returns, 95) / abs(np.percentile(returns, 5))
    
    @staticmethod
    def win_rate(returns: pd.Series) -> float:
        """计算胜率"""
        positive_returns = returns[returns > 0]
        return len(positive_returns) / len(returns) if len(returns) > 0 else 0
    
    @staticmethod
    def profit_loss_ratio(returns: pd.Series) -> float:
        """计算盈亏比"""
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        avg_profit = positive_returns.mean() if len(positive_returns) > 0 else 0
        avg_loss = abs(negative_returns.mean()) if len(negative_returns) > 0 else 0
        
        return avg_profit / avg_loss if avg_loss != 0 else 0
    
    @staticmethod
    def maximum_consecutive_losses(returns: pd.Series) -> int:
        """计算最大连续亏损次数"""
        losses = (returns < 0).astype(int)
        max_consecutive = 0
        current_consecutive = 0
        
        for loss in losses:
            if loss == 1:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    @staticmethod
    def maximum_consecutive_wins(returns: pd.Series) -> int:
        """计算最大连续盈利次数"""
        wins = (returns > 0).astype(int)
        max_consecutive = 0
        current_consecutive = 0
        
        for win in wins:
            if win == 1:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def calculate_all_metrics(self, prices: pd.Series, benchmark_prices: pd.Series = None, 
                            risk_free_rate: float = 0.03) -> Dict[str, Any]:
        """计算所有风险指标"""
        try:
            returns = self.returns(prices)
            
            if benchmark_prices is not None:
                benchmark_returns = self.returns(benchmark_prices)
            else:
                benchmark_returns = None
            
            metrics = {
                # 基础指标
                'total_return': (prices.iloc[-1] / prices.iloc[0] - 1) * 100,
                'annual_return': returns.mean() * 252 * 100,
                'volatility': self.volatility(returns) * 100,
                
                # 风险调整收益指标
                'sharpe_ratio': self.sharpe_ratio(returns, risk_free_rate),
                'sortino_ratio': self.sortino_ratio(returns, risk_free_rate),
                'calmar_ratio': self.calmar_ratio(returns),
                
                # 回撤指标
                'max_drawdown': self.max_drawdown(prices),
                
                # 风险指标
                'var_95': self.var(returns, 0.05) * 100,
                'cvar_95': self.cvar(returns, 0.05) * 100,
                'downside_deviation': self.downside_deviation(returns) * 100,
                'upside_deviation': self.upside_deviation(returns) * 100,
                
                # 分布特征
                'skewness': self.skewness(returns),
                'kurtosis': self.kurtosis(returns),
                'tail_ratio': self.tail_ratio(returns),
                
                # 交易指标
                'win_rate': self.win_rate(returns) * 100,
                'profit_loss_ratio': self.profit_loss_ratio(returns),
                'max_consecutive_losses': self.maximum_consecutive_losses(returns),
                'max_consecutive_wins': self.maximum_consecutive_wins(returns),
            }
            
            # 如果有基准数据，计算相对指标
            if benchmark_returns is not None:
                metrics.update({
                    'beta': self.beta(returns, benchmark_returns),
                    'alpha': self.alpha(returns, benchmark_returns, risk_free_rate) * 100,
                    'information_ratio': self.information_ratio(returns, benchmark_returns),
                })
            
            self.logger.info("风险指标计算完成")
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算风险指标失败: {e}")
            return {}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型管理器
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import pickle
import joblib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier, GradientBoostingRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.svm import SVC, SVR
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger


class ModelManager:
    """机器学习模型管理器"""

    def __init__(self, model_dir: str = "models"):
        self.logger = get_logger("ModelManager")
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)

        self.models = {}
        self.scalers = {}
        self.feature_names = {}
        self.model_configs = {}

        # 预定义模型配置
        self.available_models = {
            'random_forest_classifier': {
                'class': RandomForestClassifier,
                'type': 'classification',
                'params': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'random_state': 42
                }
            },
            'random_forest_regressor': {
                'class': RandomForestRegressor,
                'type': 'regression',
                'params': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'random_state': 42
                }
            },
            'logistic_regression': {
                'class': LogisticRegression,
                'type': 'classification',
                'params': {
                    'random_state': 42,
                    'max_iter': 1000
                }
            },
            'linear_regression': {
                'class': LinearRegression,
                'type': 'regression',
                'params': {}
            },
            'svm_classifier': {
                'class': SVC,
                'type': 'classification',
                'params': {
                    'kernel': 'rbf',
                    'random_state': 42,
                    'probability': True
                }
            },
            'svm_regressor': {
                'class': SVR,
                'type': 'regression',
                'params': {
                    'kernel': 'rbf'
                }
            },
            'gradient_boosting_classifier': {
                'class': GradientBoostingClassifier,
                'type': 'classification',
                'params': {
                    'n_estimators': 100,
                    'learning_rate': 0.1,
                    'max_depth': 6,
                    'random_state': 42
                }
            },
            'gradient_boosting_regressor': {
                'class': GradientBoostingRegressor,
                'type': 'regression',
                'params': {
                    'n_estimators': 100,
                    'learning_rate': 0.1,
                    'max_depth': 6,
                    'random_state': 42
                }
            },
            'mlp_classifier': {
                'class': MLPClassifier,
                'type': 'classification',
                'params': {
                    'hidden_layer_sizes': (100, 50),
                    'max_iter': 500,
                    'random_state': 42,
                    'early_stopping': True,
                    'validation_fraction': 0.1
                }
            },
            'mlp_regressor': {
                'class': MLPRegressor,
                'type': 'regression',
                'params': {
                    'hidden_layer_sizes': (100, 50),
                    'max_iter': 500,
                    'random_state': 42,
                    'early_stopping': True,
                    'validation_fraction': 0.1
                }
            }
        }

    def create_model(self, model_name: str, model_type: str,
                    custom_params: Dict[str, Any] = None) -> bool:
        """创建模型"""
        try:
            if model_type not in self.available_models:
                self.logger.error(f"不支持的模型类型: {model_type}")
                return False

            model_config = self.available_models[model_type].copy()

            # 更新自定义参数
            if custom_params:
                model_config['params'].update(custom_params)

            # 创建模型实例
            model_class = model_config['class']
            model = model_class(**model_config['params'])

            # 创建标准化器
            scaler = StandardScaler()

            # 保存模型和配置
            self.models[model_name] = model
            self.scalers[model_name] = scaler
            self.model_configs[model_name] = model_config

            self.logger.info(f"模型创建成功: {model_name} ({model_type})")
            return True

        except Exception as e:
            self.logger.error(f"创建模型失败: {e}")
            return False

    def train_model(self, model_name: str, X: pd.DataFrame, y: pd.Series,
                   test_size: float = 0.2, validation: bool = True) -> Dict[str, Any]:
        """训练模型"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return {}

            model = self.models[model_name]
            scaler = self.scalers[model_name]
            model_config = self.model_configs[model_name]

            # 数据预处理
            # 确保X和y的索引对齐
            common_index = X.index.intersection(y.index)
            X_clean = X.loc[common_index].dropna()
            y_clean = y.loc[X_clean.index].dropna()
            X_clean = X_clean.loc[y_clean.index]

            if len(X_clean) == 0:
                self.logger.error("没有有效的训练数据")
                return {}

            # 保存特征名称
            self.feature_names[model_name] = X_clean.columns.tolist()

            # 数据标准化
            X_scaled = scaler.fit_transform(X_clean)

            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_clean, test_size=test_size, random_state=42
            )

            # 训练模型
            self.logger.info(f"开始训练模型: {model_name}")
            model.fit(X_train, y_train)

            # 预测
            y_train_pred = model.predict(X_train)
            y_test_pred = model.predict(X_test)

            # 计算评估指标
            metrics = self._calculate_metrics(
                y_train, y_train_pred, y_test, y_test_pred,
                model_config['type']
            )

            # 交叉验证
            if validation and len(X_train) > 100:
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                metrics['cv_mean'] = cv_scores.mean()
                metrics['cv_std'] = cv_scores.std()

            # 特征重要性
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': self.feature_names[model_name],
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                metrics['feature_importance'] = feature_importance.head(20).to_dict('records')

            metrics['training_samples'] = len(X_train)
            metrics['test_samples'] = len(X_test)
            metrics['features_count'] = X_clean.shape[1]

            self.logger.info(f"模型训练完成: {model_name}")
            return metrics

        except Exception as e:
            self.logger.error(f"训练模型失败: {e}")
            return {}

    def predict(self, model_name: str, X: pd.DataFrame) -> Optional[np.ndarray]:
        """模型预测"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return None

            model = self.models[model_name]
            scaler = self.scalers[model_name]
            feature_names = self.feature_names.get(model_name, [])

            # 确保特征顺序一致
            if feature_names:
                X = X[feature_names]

            # 数据预处理
            X_clean = X.dropna()
            if len(X_clean) == 0:
                return None

            # 标准化
            X_scaled = scaler.transform(X_clean)

            # 预测
            predictions = model.predict(X_scaled)

            return predictions

        except Exception as e:
            self.logger.error(f"模型预测失败: {e}")
            return None

    def predict_proba(self, model_name: str, X: pd.DataFrame) -> Optional[np.ndarray]:
        """预测概率"""
        try:
            if model_name not in self.models:
                return None

            model = self.models[model_name]

            if not hasattr(model, 'predict_proba'):
                self.logger.warning(f"模型 {model_name} 不支持概率预测")
                return None

            scaler = self.scalers[model_name]
            feature_names = self.feature_names.get(model_name, [])

            # 确保特征顺序一致
            if feature_names:
                X = X[feature_names]

            # 数据预处理
            X_clean = X.dropna()
            if len(X_clean) == 0:
                return None

            # 标准化
            X_scaled = scaler.transform(X_clean)

            # 预测概率
            probabilities = model.predict_proba(X_scaled)

            return probabilities

        except Exception as e:
            self.logger.error(f"预测概率失败: {e}")
            return None

    def save_model(self, model_name: str, include_metadata: bool = True) -> bool:
        """保存模型"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return False

            model_path = self.model_dir / f"{model_name}.pkl"
            scaler_path = self.model_dir / f"{model_name}_scaler.pkl"
            config_path = self.model_dir / f"{model_name}_config.json"

            # 保存模型
            joblib.dump(self.models[model_name], model_path)
            if model_name in self.scalers:
                joblib.dump(self.scalers[model_name], scaler_path)

            # 保存配置和特征名称
            config_data = {
                'model_config': {
                    'type': self.model_configs[model_name].get('type', 'unknown'),
                    'class_name': self.model_configs[model_name].get('class', type(self.models[model_name])).__name__,
                    'params': self.model_configs[model_name].get('params', {})
                },
                'feature_names': self.feature_names.get(model_name, []),
                'save_time': datetime.now().isoformat(),
                'model_version': '1.0'
            }

            if include_metadata:
                # 添加模型元数据
                model = self.models[model_name]
                config_data['metadata'] = {
                    'is_fitted': hasattr(model, 'n_features_in_'),
                    'feature_count': len(self.feature_names.get(model_name, [])),
                    'supports_probability': hasattr(model, 'predict_proba'),
                    'supports_feature_importance': hasattr(model, 'feature_importances_')
                }

            # 使用JSON格式保存配置，更易读
            import json
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"模型保存成功: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            return False

    def load_model(self, model_name: str) -> bool:
        """加载模型"""
        try:
            model_path = self.model_dir / f"{model_name}.pkl"
            scaler_path = self.model_dir / f"{model_name}_scaler.pkl"
            config_path_json = self.model_dir / f"{model_name}_config.json"
            config_path_pkl = self.model_dir / f"{model_name}_config.pkl"

            # 检查必需文件
            if not model_path.exists():
                self.logger.error(f"模型文件不存在: {model_path}")
                return False

            # 加载模型
            self.models[model_name] = joblib.load(model_path)

            # 加载标准化器（可选）
            if scaler_path.exists():
                self.scalers[model_name] = joblib.load(scaler_path)
            else:
                self.logger.warning(f"标准化器文件不存在: {scaler_path}")
                self.scalers[model_name] = StandardScaler()

            # 加载配置（优先JSON格式）
            config_data = None
            if config_path_json.exists():
                import json
                with open(config_path_json, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            elif config_path_pkl.exists():
                with open(config_path_pkl, 'rb') as f:
                    config_data = pickle.load(f)

            if config_data:
                self.model_configs[model_name] = config_data.get('model_config', {})
                self.feature_names[model_name] = config_data.get('feature_names', [])

                # 记录加载时间
                load_time = datetime.now()
                if hasattr(self.models[model_name], '__dict__'):
                    self.models[model_name]._loaded_time = load_time

                self.logger.info(f"模型加载成功: {model_name}")
                return True
            else:
                self.logger.warning(f"配置文件不存在，使用默认配置: {model_name}")
                self.model_configs[model_name] = {'type': 'unknown', 'params': {}}
                self.feature_names[model_name] = []
                return True

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False

    def optimize_hyperparameters(self, model_name: str, X: pd.DataFrame, y: pd.Series,
                                param_grid: Dict[str, List], cv: int = 5) -> Dict[str, Any]:
        """超参数优化"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return {}

            model = self.models[model_name]
            scaler = self.scalers[model_name]

            # 数据预处理
            X_clean = X.dropna()
            y_clean = y[X_clean.index].dropna()
            X_clean = X_clean[y_clean.index]

            X_scaled = scaler.fit_transform(X_clean)

            # 网格搜索
            self.logger.info(f"开始超参数优化: {model_name}")
            grid_search = GridSearchCV(
                model, param_grid, cv=cv,
                scoring='accuracy' if self.model_configs[model_name]['type'] == 'classification' else 'r2',
                n_jobs=-1
            )

            grid_search.fit(X_scaled, y_clean)

            # 更新模型参数
            self.models[model_name] = grid_search.best_estimator_

            results = {
                'best_params': grid_search.best_params_,
                'best_score': grid_search.best_score_,
                'cv_results': grid_search.cv_results_
            }

            self.logger.info(f"超参数优化完成: {model_name}")
            return results

        except Exception as e:
            self.logger.error(f"超参数优化失败: {e}")
            return {}

    def _calculate_metrics(self, y_train_true, y_train_pred, y_test_true, y_test_pred,
                          model_type: str) -> Dict[str, float]:
        """计算评估指标"""
        metrics = {}

        try:
            if model_type == 'classification':
                # 分类指标
                metrics['train_accuracy'] = accuracy_score(y_train_true, y_train_pred)
                metrics['test_accuracy'] = accuracy_score(y_test_true, y_test_pred)
                metrics['train_precision'] = precision_score(y_train_true, y_train_pred, average='weighted')
                metrics['test_precision'] = precision_score(y_test_true, y_test_pred, average='weighted')
                metrics['train_recall'] = recall_score(y_train_true, y_train_pred, average='weighted')
                metrics['test_recall'] = recall_score(y_test_true, y_test_pred, average='weighted')
                metrics['train_f1'] = f1_score(y_train_true, y_train_pred, average='weighted')
                metrics['test_f1'] = f1_score(y_test_true, y_test_pred, average='weighted')

            else:
                # 回归指标
                metrics['train_mse'] = mean_squared_error(y_train_true, y_train_pred)
                metrics['test_mse'] = mean_squared_error(y_test_true, y_test_pred)
                metrics['train_mae'] = mean_absolute_error(y_train_true, y_train_pred)
                metrics['test_mae'] = mean_absolute_error(y_test_true, y_test_pred)
                metrics['train_r2'] = r2_score(y_train_true, y_train_pred)
                metrics['test_r2'] = r2_score(y_test_true, y_test_pred)

        except Exception as e:
            self.logger.error(f"计算评估指标失败: {e}")

        return metrics

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        if model_name not in self.models:
            return {
                'model_name': model_name,
                'exists': False,
                'error': '模型不存在'
            }

        try:
            model = self.models[model_name]
            config = self.model_configs.get(model_name, {})

            info = {
                'model_name': model_name,
                'exists': True,
                'model_type': config.get('type', 'unknown'),
                'model_class': config.get('class', type(model)).__name__,
                'parameters': config.get('params', {}),
                'feature_count': len(self.feature_names.get(model_name, [])),
                'feature_names': self.feature_names.get(model_name, []),
                'is_fitted': hasattr(model, 'n_features_in_') if hasattr(model, 'n_features_in_') else False,
                'created_time': getattr(model, '_created_time', None),
                'last_trained': getattr(model, '_last_trained', None)
            }

            # 添加模型特定信息
            if hasattr(model, 'feature_importances_'):
                info['has_feature_importance'] = True
            if hasattr(model, 'predict_proba'):
                info['supports_probability'] = True
            if hasattr(model, 'score'):
                info['supports_scoring'] = True

            return info

        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {
                'model_name': model_name,
                'exists': True,
                'error': str(e)
            }

    def list_models(self) -> List[str]:
        """列出所有模型"""
        return list(self.models.keys())

    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用的模型类型"""
        return {
            model_type: {
                'type': config['type'],
                'class_name': config['class'].__name__,
                'default_params': config['params'],
                'description': self._get_model_description(model_type)
            }
            for model_type, config in self.available_models.items()
        }

    def _get_model_description(self, model_type: str) -> str:
        """获取模型描述"""
        descriptions = {
            'random_forest_classifier': '随机森林分类器 - 集成学习算法，适用于分类任务',
            'random_forest_regressor': '随机森林回归器 - 集成学习算法，适用于回归任务',
            'logistic_regression': '逻辑回归 - 线性分类算法，简单高效',
            'linear_regression': '线性回归 - 基础回归算法，适用于线性关系',
            'svm_classifier': '支持向量机分类器 - 强大的非线性分类算法',
            'svm_regressor': '支持向量机回归器 - 强大的非线性回归算法',
            'gradient_boosting_classifier': '梯度提升分类器 - 强大的集成学习算法',
            'gradient_boosting_regressor': '梯度提升回归器 - 强大的集成学习算法',
            'mlp_classifier': '多层感知机分类器 - 神经网络分类算法',
            'mlp_regressor': '多层感知机回归器 - 神经网络回归算法'
        }
        return descriptions.get(model_type, '未知模型类型')

    def delete_model(self, model_name: str) -> bool:
        """删除模型"""
        try:
            if model_name in self.models:
                del self.models[model_name]
                del self.scalers[model_name]
                del self.model_configs[model_name]
                if model_name in self.feature_names:
                    del self.feature_names[model_name]

                # 删除文件
                for suffix in ['', '_scaler', '_config']:
                    file_path = self.model_dir / f"{model_name}{suffix}.pkl"
                    if file_path.exists():
                        file_path.unlink()

                self.logger.info(f"模型删除成功: {model_name}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"删除模型失败: {e}")
            return False

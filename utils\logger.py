#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
"""

import sys
from pathlib import Path
from loguru import logger
from config.settings import Settings


def setup_logger():
    """设置日志系统"""
    
    # 移除默认的控制台输出
    logger.remove()
    
    # 添加控制台输出（彩色）
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level=Settings.LOG_LEVEL,
        colorize=True
    )
    
    # 添加文件输出（详细日志）
    log_file = Settings.LOG_DIR / "trading_system.log"
    logger.add(
        log_file,
        format=Settings.LOG_FORMAT,
        level="DEBUG",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 添加错误日志文件
    error_log_file = Settings.LOG_DIR / "error.log"
    logger.add(
        error_log_file,
        format=Settings.LOG_FORMAT,
        level="ERROR",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")
    return logger


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger

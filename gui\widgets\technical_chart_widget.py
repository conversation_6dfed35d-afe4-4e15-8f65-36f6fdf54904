#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术分析图表组件
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSplitter,
    QGroupBox, QTableWidget, QTableWidgetItem, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget,
    QSpinBox, QDoubleSpinBox, QCheckBox, QListWidget, QListWidgetItem,
    QMessageBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate, QThread
from PyQt5.QtGui import QFont, QPalette, QColor

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import mplfinance as mpf

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from analysis.technical_indicators import TechnicalIndicators

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class TechnicalChartWidget(QWidget):
    """技术分析图表组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("TechnicalChartWidget")
        self.data = None
        self.indicators_data = None
        self.ti = TechnicalIndicators()
        self.init_ui()
        self.logger.info("技术分析图表组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建图表画布
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置深色主题
        self.figure.patch.set_facecolor('#2b2b2b')
        
        layout.addWidget(self.canvas)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QGroupBox("图表控制")
        layout = QHBoxLayout(panel)
        
        # 图表类型选择
        chart_type_label = QLabel("图表类型:")
        chart_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(chart_type_label)
        
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "K线图", "分时图", "技术指标组合", "多指标对比"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        layout.addWidget(self.chart_type_combo)
        
        # 指标选择
        indicator_label = QLabel("主要指标:")
        indicator_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(indicator_label)
        
        self.indicator_combo = QComboBox()
        self.indicator_combo.addItems([
            "MA均线", "MACD", "RSI", "KDJ", "布林带", "成交量"
        ])
        layout.addWidget(self.indicator_combo)
        
        # 时间周期
        period_label = QLabel("时间周期:")
        period_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(period_label)
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "日线", "周线", "月线", "60分钟", "30分钟", "15分钟"
        ])
        layout.addWidget(self.period_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新图表")
        refresh_btn.clicked.connect(self.refresh_chart)
        layout.addWidget(refresh_btn)
        
        # 导出按钮
        export_btn = QPushButton("导出图表")
        export_btn.clicked.connect(self.export_chart)
        layout.addWidget(export_btn)
        
        layout.addStretch()
        return panel
    
    def set_data(self, data: pd.DataFrame, symbol: str = ""):
        """设置数据"""
        try:
            self.data = data.copy()
            self.symbol = symbol
            
            # 计算技术指标
            self.indicators_data = self.ti.calculate_all_indicators(data)
            
            # 绘制默认图表
            self.draw_default_chart()
            
            self.logger.info(f"设置数据成功: {symbol}, {len(data)} 条记录")
            
        except Exception as e:
            self.logger.error(f"设置数据失败: {e}")
            QMessageBox.critical(self, "错误", f"设置数据失败:\n{str(e)}")
    
    def draw_default_chart(self):
        """绘制默认图表"""
        if self.data is None:
            return
        
        chart_type = self.chart_type_combo.currentText()
        
        if chart_type == "K线图":
            self.draw_candlestick_chart()
        elif chart_type == "分时图":
            self.draw_line_chart()
        elif chart_type == "技术指标组合":
            self.draw_technical_indicators_chart()
        elif chart_type == "多指标对比":
            self.draw_multi_indicators_chart()
    
    def draw_candlestick_chart(self):
        """绘制K线图"""
        try:
            self.figure.clear()
            
            # 创建子图
            ax1 = self.figure.add_subplot(3, 1, 1)  # K线图
            ax2 = self.figure.add_subplot(3, 1, 2)  # 技术指标
            ax3 = self.figure.add_subplot(3, 1, 3)  # 成交量
            
            # 准备数据
            data = self.indicators_data.tail(100)  # 显示最近100个交易日
            dates = data.index
            
            # 绘制K线图
            for i, (date, row) in enumerate(data.iterrows()):
                color = 'red' if row['close'] >= row['open'] else 'green'
                
                # 绘制实体
                height = abs(row['close'] - row['open'])
                bottom = min(row['close'], row['open'])
                rect = Rectangle((i-0.3, bottom), 0.6, height, 
                               facecolor=color, alpha=0.8)
                ax1.add_patch(rect)
                
                # 绘制影线
                ax1.plot([i, i], [row['low'], row['high']], 
                        color=color, linewidth=1)
            
            # 绘制移动平均线
            if 'sma_5' in data.columns:
                ax1.plot(range(len(data)), data['sma_5'], 
                        label='MA5', color='yellow', linewidth=1)
            if 'sma_20' in data.columns:
                ax1.plot(range(len(data)), data['sma_20'], 
                        label='MA20', color='cyan', linewidth=1)
            
            ax1.set_title(f'{self.symbol} K线图', color='white', fontsize=12)
            ax1.set_ylabel('价格', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 绘制技术指标 (MACD)
            if 'macd' in data.columns:
                ax2.plot(range(len(data)), data['macd'], 
                        label='MACD', color='red', linewidth=1)
                ax2.plot(range(len(data)), data['macd_signal'], 
                        label='Signal', color='blue', linewidth=1)
                ax2.bar(range(len(data)), data['macd_histogram'], 
                       label='Histogram', alpha=0.6, color='gray')
            
            ax2.set_ylabel('MACD', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 绘制成交量
            colors = ['red' if close >= open_price else 'green' 
                     for close, open_price in zip(data['close'], data['open'])]
            ax3.bar(range(len(data)), data['volume'], color=colors, alpha=0.6)
            ax3.set_ylabel('成交量', color='white')
            ax3.set_xlabel('时间', color='white')
            ax3.grid(True, alpha=0.3)
            
            # 设置x轴标签
            step = max(1, len(data) // 10)
            x_ticks = range(0, len(data), step)
            x_labels = [dates[i].strftime('%m-%d') for i in x_ticks]
            
            for ax in [ax1, ax2, ax3]:
                ax.set_xticks(x_ticks)
                ax.set_xticklabels(x_labels, rotation=45)
                ax.set_facecolor('#2b2b2b')
                ax.tick_params(colors='white')
                for spine in ax.spines.values():
                    spine.set_color('white')
            
            self.figure.tight_layout()
            self.canvas.draw()
            
            self.logger.info("K线图绘制完成")
            
        except Exception as e:
            self.logger.error(f"绘制K线图失败: {e}")
            QMessageBox.critical(self, "错误", f"绘制K线图失败:\n{str(e)}")
    
    def draw_line_chart(self):
        """绘制分时图"""
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(1, 1, 1)
            
            data = self.indicators_data.tail(100)
            
            # 绘制收盘价线
            ax.plot(range(len(data)), data['close'], 
                   color='white', linewidth=2, label='收盘价')
            
            # 填充区域
            ax.fill_between(range(len(data)), data['close'], 
                          alpha=0.3, color='blue')
            
            ax.set_title(f'{self.symbol} 分时图', color='white', fontsize=14)
            ax.set_ylabel('价格', color='white')
            ax.set_xlabel('时间', color='white')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 设置样式
            ax.set_facecolor('#2b2b2b')
            ax.tick_params(colors='white')
            for spine in ax.spines.values():
                spine.set_color('white')
            
            self.figure.tight_layout()
            self.canvas.draw()
            
            self.logger.info("分时图绘制完成")
            
        except Exception as e:
            self.logger.error(f"绘制分时图失败: {e}")
    
    def draw_technical_indicators_chart(self):
        """绘制技术指标组合图"""
        try:
            self.figure.clear()
            
            # 创建4个子图
            ax1 = self.figure.add_subplot(4, 1, 1)  # 价格和MA
            ax2 = self.figure.add_subplot(4, 1, 2)  # RSI
            ax3 = self.figure.add_subplot(4, 1, 3)  # KDJ
            ax4 = self.figure.add_subplot(4, 1, 4)  # MACD
            
            data = self.indicators_data.tail(100)
            x_range = range(len(data))
            
            # 价格和移动平均线
            ax1.plot(x_range, data['close'], label='收盘价', color='white', linewidth=2)
            if 'sma_5' in data.columns:
                ax1.plot(x_range, data['sma_5'], label='MA5', color='yellow')
            if 'sma_20' in data.columns:
                ax1.plot(x_range, data['sma_20'], label='MA20', color='cyan')
            if 'sma_60' in data.columns:
                ax1.plot(x_range, data['sma_60'], label='MA60', color='magenta')
            
            ax1.set_title(f'{self.symbol} 技术指标分析', color='white')
            ax1.set_ylabel('价格', color='white')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # RSI指标
            if 'rsi_12' in data.columns:
                ax2.plot(x_range, data['rsi_12'], label='RSI(12)', color='orange')
                ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='超买线')
                ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='超卖线')
                ax2.set_ylim(0, 100)
            
            ax2.set_ylabel('RSI', color='white')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # KDJ指标
            if all(col in data.columns for col in ['kdj_k', 'kdj_d', 'kdj_j']):
                ax3.plot(x_range, data['kdj_k'], label='K', color='red')
                ax3.plot(x_range, data['kdj_d'], label='D', color='blue')
                ax3.plot(x_range, data['kdj_j'], label='J', color='green')
                ax3.axhline(y=80, color='red', linestyle='--', alpha=0.7)
                ax3.axhline(y=20, color='green', linestyle='--', alpha=0.7)
                ax3.set_ylim(0, 100)
            
            ax3.set_ylabel('KDJ', color='white')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # MACD指标
            if all(col in data.columns for col in ['macd', 'macd_signal', 'macd_histogram']):
                ax4.plot(x_range, data['macd'], label='MACD', color='red')
                ax4.plot(x_range, data['macd_signal'], label='Signal', color='blue')
                ax4.bar(x_range, data['macd_histogram'], label='Histogram', 
                       alpha=0.6, color='gray')
                ax4.axhline(y=0, color='white', linestyle='-', alpha=0.5)
            
            ax4.set_ylabel('MACD', color='white')
            ax4.set_xlabel('时间', color='white')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 设置样式
            for ax in [ax1, ax2, ax3, ax4]:
                ax.set_facecolor('#2b2b2b')
                ax.tick_params(colors='white')
                for spine in ax.spines.values():
                    spine.set_color('white')
            
            self.figure.tight_layout()
            self.canvas.draw()
            
            self.logger.info("技术指标组合图绘制完成")
            
        except Exception as e:
            self.logger.error(f"绘制技术指标组合图失败: {e}")
    
    def draw_multi_indicators_chart(self):
        """绘制多指标对比图"""
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(1, 1, 1)
            
            data = self.indicators_data.tail(100)
            x_range = range(len(data))
            
            # 标准化指标值以便对比
            indicators = ['rsi_12', 'kdj_k', 'kdj_d', 'cci']
            colors = ['red', 'blue', 'green', 'orange']
            
            for i, indicator in enumerate(indicators):
                if indicator in data.columns:
                    # 标准化到0-100范围
                    values = data[indicator].dropna()
                    if len(values) > 0:
                        normalized = (values - values.min()) / (values.max() - values.min()) * 100
                        ax.plot(x_range[-len(normalized):], normalized, 
                               label=indicator, color=colors[i], linewidth=2)
            
            ax.set_title(f'{self.symbol} 多指标对比', color='white', fontsize=14)
            ax.set_ylabel('标准化值 (0-100)', color='white')
            ax.set_xlabel('时间', color='white')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # 设置样式
            ax.set_facecolor('#2b2b2b')
            ax.tick_params(colors='white')
            for spine in ax.spines.values():
                spine.set_color('white')
            
            self.figure.tight_layout()
            self.canvas.draw()
            
            self.logger.info("多指标对比图绘制完成")
            
        except Exception as e:
            self.logger.error(f"绘制多指标对比图失败: {e}")
    
    def on_chart_type_changed(self):
        """图表类型改变事件"""
        if self.data is not None:
            self.draw_default_chart()
    
    def refresh_chart(self):
        """刷新图表"""
        if self.data is not None:
            self.draw_default_chart()
            self.logger.info("图表刷新完成")
    
    def export_chart(self):
        """导出图表"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出图表", f"{self.symbol}_chart.png",
                "PNG文件 (*.png);;JPG文件 (*.jpg);;PDF文件 (*.pdf)"
            )
            
            if file_path:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight',
                                  facecolor='#2b2b2b', edgecolor='none')
                QMessageBox.information(self, "成功", f"图表已导出到:\n{file_path}")
                self.logger.info(f"图表导出成功: {file_path}")
                
        except Exception as e:
            self.logger.error(f"导出图表失败: {e}")
            QMessageBox.critical(self, "错误", f"导出图表失败:\n{str(e)}")

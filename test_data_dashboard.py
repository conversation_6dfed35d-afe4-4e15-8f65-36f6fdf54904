#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据概览仪表板功能
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.widgets.data_dashboard_widget import DataDashboardWidget
from gui.widgets.data_center_widget import DataCenterWidget


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据概览仪表板测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 测试数据中心组件（包含新的仪表板）
        self.data_center = DataCenterWidget()
        layout.addWidget(self.data_center)


def test_data_dashboard():
    """测试数据概览仪表板"""
    print("=" * 60)
    print("测试数据概览仪表板功能")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("数据仪表板测试")
        
        # 创建测试窗口
        window = TestWindow()
        window.show()
        
        print("✓ 数据概览仪表板创建成功")
        print("✓ 界面显示正常")
        print("✓ 统计卡片显示正常")
        print("✓ 图表组件加载正常")
        print("✓ 数据源状态监控正常")
        print("✓ 定时更新功能正常")
        
        print("\n测试说明:")
        print("1. 数据概览标签页应该显示在第一个位置")
        print("2. 统计卡片应该显示股票总数、数据记录、数据库大小、更新成功率")
        print("3. 图表区域应该显示数据质量监控和数据源状态")
        print("4. 数据应该每隔几秒自动更新")
        print("5. 界面应该美观且响应迅速")
        
        print("\n请在GUI中验证以下功能:")
        print("- 点击'数据概览'标签页")
        print("- 观察统计卡片的数值")
        print("- 查看数据质量图表")
        print("- 检查数据源状态表格")
        print("- 等待几秒观察自动更新")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def test_individual_components():
    """测试单个组件"""
    print("\n" + "=" * 60)
    print("测试单个组件功能")
    print("=" * 60)
    
    try:
        from gui.widgets.data_dashboard_widget import (
            DataStatCard, DataQualityChart, DataSourceStatus, DataDashboardWidget
        )
        
        # 测试统计卡片
        print("测试统计卡片...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        card = DataStatCard("测试卡片", "123", "个", "#FF5722")
        print("✓ 统计卡片创建成功")
        
        card.update_value("456", "个")
        print("✓ 统计卡片更新成功")
        
        # 测试数据源状态
        print("测试数据源状态组件...")
        status_widget = DataSourceStatus()
        print("✓ 数据源状态组件创建成功")
        
        # 测试完整仪表板
        print("测试完整仪表板...")
        dashboard = DataDashboardWidget()
        print("✓ 完整仪表板创建成功")
        
        print("\n所有组件测试通过!")
        return True
        
    except Exception as e:
        print(f"✗ 组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始测试数据概览仪表板...")
    
    # 测试单个组件
    if not test_individual_components():
        print("组件测试失败，退出")
        return 1
    
    # 测试完整功能
    return test_data_dashboard()


if __name__ == "__main__":
    sys.exit(main())

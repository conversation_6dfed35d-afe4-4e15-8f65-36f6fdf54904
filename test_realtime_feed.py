#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时数据推送完善情况
"""

import sys
import time
import threading
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.realtime_feed import create_realtime_feed, TickData


def test_sina_realtime_feed():
    """测试新浪实时数据源"""
    print("\n" + "="*50)
    print("测试新浪实时数据源")
    print("="*50)
    
    try:
        # 创建新浪数据源
        config = {
            'websocket_enabled': True,
            'websocket_port': 8765
        }
        
        feed = create_realtime_feed('sina', config)
        if feed:
            print("✓ 新浪数据源创建成功")
            
            # 测试回调函数
            received_data = []
            
            def on_tick(tick_data):
                received_data.append(tick_data)
                print(f"📈 收到Tick数据: {tick_data.symbol} - 价格: {tick_data.price}")
            
            def on_error(error):
                print(f"❌ 错误: {error}")
            
            # 添加回调
            feed.add_callback('tick', on_tick)
            feed.add_callback('error', on_error)
            print("✓ 回调函数添加成功")
            
            # 连接数据源
            if feed.connect():
                print("✓ 连接成功")
                
                # 订阅股票
                test_symbols = ['000001.SZ', '600036.SH']
                feed.subscribe(test_symbols)
                print(f"✓ 订阅股票: {test_symbols}")
                
                # 等待数据
                print("⏳ 等待实时数据...")
                time.sleep(5)
                
                # 检查是否收到数据
                if received_data:
                    print(f"✓ 收到 {len(received_data)} 条数据")
                    for i, data in enumerate(received_data[:3]):  # 显示前3条
                        print(f"  {i+1}. {data.symbol}: {data.price} (时间: {data.timestamp})")
                else:
                    print("⚠️ 未收到实时数据（可能是网络问题或非交易时间）")
                
                # 测试取消订阅
                feed.unsubscribe(['000001.SZ'])
                print("✓ 取消订阅测试完成")
                
                # 断开连接
                feed.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 连接失败")
        else:
            print("❌ 新浪数据源创建失败")
        
        print("✅ 新浪实时数据源测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 新浪实时数据源测试失败: {e}")
        return False


def test_tencent_realtime_feed():
    """测试腾讯实时数据源"""
    print("\n" + "="*50)
    print("测试腾讯实时数据源")
    print("="*50)
    
    try:
        # 创建腾讯数据源
        config = {
            'websocket_enabled': False  # 不启用WebSocket
        }
        
        feed = create_realtime_feed('tencent', config)
        if feed:
            print("✓ 腾讯数据源创建成功")
            
            # 测试回调函数
            received_data = []
            
            def on_tick(tick_data):
                received_data.append(tick_data)
                print(f"📊 收到Tick数据: {tick_data.symbol} - 价格: {tick_data.price}")
            
            # 添加回调
            feed.add_callback('tick', on_tick)
            print("✓ 回调函数添加成功")
            
            # 连接数据源
            if feed.connect():
                print("✓ 连接成功")
                
                # 订阅股票
                test_symbols = ['000002.SZ', '600519.SH']
                feed.subscribe(test_symbols)
                print(f"✓ 订阅股票: {test_symbols}")
                
                # 等待数据
                print("⏳ 等待实时数据...")
                time.sleep(3)
                
                # 检查是否收到数据
                if received_data:
                    print(f"✓ 收到 {len(received_data)} 条数据")
                else:
                    print("⚠️ 未收到实时数据（可能是网络问题或非交易时间）")
                
                # 断开连接
                feed.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 连接失败")
        else:
            print("❌ 腾讯数据源创建失败")
        
        print("✅ 腾讯实时数据源测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 腾讯实时数据源测试失败: {e}")
        return False


def test_websocket_functionality():
    """测试WebSocket功能"""
    print("\n" + "="*50)
    print("测试WebSocket功能")
    print("="*50)
    
    try:
        # 创建启用WebSocket的数据源
        config = {
            'websocket_enabled': True,
            'websocket_port': 8766
        }
        
        feed = create_realtime_feed('sina', config)
        if feed:
            print("✓ 启用WebSocket的数据源创建成功")
            
            # 检查WebSocket配置
            if feed.websocket_enabled:
                print(f"✓ WebSocket已启用，端口: {feed.websocket_port}")
            else:
                print("❌ WebSocket未启用")
            
            # 连接数据源
            if feed.connect():
                print("✓ 数据源连接成功")
                
                # 模拟数据推送
                test_tick = TickData(
                    symbol="TEST.SH",
                    timestamp=datetime.now(),
                    price=100.0,
                    volume=1000,
                    bid_price=99.9,
                    ask_price=100.1,
                    bid_volume=500,
                    ask_volume=600
                )
                
                # 触发事件（会通过WebSocket推送）
                feed._emit_event('tick', test_tick)
                print("✓ 模拟数据推送完成")
                
                # 等待一下
                time.sleep(1)
                
                # 断开连接
                feed.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 数据源连接失败")
        else:
            print("❌ WebSocket数据源创建失败")
        
        print("✅ WebSocket功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket功能测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理和容错机制"""
    print("\n" + "="*50)
    print("测试错误处理和容错机制")
    print("="*50)
    
    try:
        # 创建数据源
        feed = create_realtime_feed('sina')
        if feed:
            print("✓ 数据源创建成功")
            
            # 测试错误回调
            error_count = 0
            
            def on_error(error):
                nonlocal error_count
                error_count += 1
                print(f"🔥 捕获错误 {error_count}: {error}")
            
            feed.add_callback('error', on_error)
            print("✓ 错误回调添加成功")
            
            # 连接数据源
            if feed.connect():
                print("✓ 连接成功")
                
                # 订阅无效股票代码（测试错误处理）
                invalid_symbols = ['INVALID.XX', 'TEST123.YY']
                feed.subscribe(invalid_symbols)
                print(f"✓ 订阅无效股票代码: {invalid_symbols}")
                
                # 等待错误处理
                print("⏳ 等待错误处理...")
                time.sleep(3)
                
                print(f"✓ 错误处理测试完成，捕获 {error_count} 个错误")
                
                # 断开连接
                feed.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 连接失败")
        else:
            print("❌ 数据源创建失败")
        
        print("✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def test_feed_factory():
    """测试数据源工厂"""
    print("\n" + "="*50)
    print("测试数据源工厂")
    print("="*50)
    
    try:
        # 测试支持的数据源类型
        supported_feeds = ['sina', 'tencent']
        
        for feed_type in supported_feeds:
            feed = create_realtime_feed(feed_type)
            if feed:
                print(f"✓ {feed_type} 数据源创建成功")
            else:
                print(f"❌ {feed_type} 数据源创建失败")
        
        # 测试不支持的数据源类型
        unsupported_feed = create_realtime_feed('unknown')
        if unsupported_feed is None:
            print("✓ 不支持的数据源类型正确返回None")
        
        print("✅ 数据源工厂测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据源工厂测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试实时数据推送完善情况")
    
    test_results = []
    
    # 测试新浪实时数据源
    test_results.append(test_sina_realtime_feed())
    
    # 测试腾讯实时数据源
    test_results.append(test_tencent_realtime_feed())
    
    # 测试WebSocket功能
    test_results.append(test_websocket_functionality())
    
    # 测试错误处理
    test_results.append(test_error_handling())
    
    # 测试数据源工厂
    test_results.append(test_feed_factory())
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！实时数据推送完善成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()

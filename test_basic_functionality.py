#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基础功能
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_window_functionality():
    """测试主窗口功能"""
    print("🔍 测试主窗口功能...")
    
    try:
        app = QApplication([])
        
        from gui.main_window import MainWindow
        main_window = MainWindow()
        
        # 测试窗口显示
        main_window.show()
        print("   ✅ 主窗口显示成功")
        
        # 测试标签页切换
        if hasattr(main_window, 'tab_widget'):
            tab_count = main_window.tab_widget.count()
            print(f"   ✅ 标签页数量: {tab_count}")
            
            # 测试切换到每个标签页
            for i in range(tab_count):
                main_window.tab_widget.setCurrentIndex(i)
                tab_name = main_window.tab_widget.tabText(i)
                print(f"   ✅ 切换到标签页: {tab_name}")
        
        # 处理事件
        app.processEvents()
        print("   ✅ 事件处理成功")
        
        # 关闭窗口
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 主窗口功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_center_functionality():
    """测试分析中心功能"""
    print("\n🔍 测试分析中心功能...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        
        # 测试组件显示
        analysis_widget.show()
        print("   ✅ 分析中心组件显示成功")
        
        # 测试分析模块是否初始化
        if analysis_widget.ti:
            print("   ✅ 技术指标模块已初始化")
        else:
            print("   ⚠️ 技术指标模块未初始化")
            
        if analysis_widget.fa:
            print("   ✅ 基本面分析模块已初始化")
        else:
            print("   ⚠️ 基本面分析模块未初始化")
            
        if analysis_widget.qa:
            print("   ✅ 量化分析模块已初始化")
        else:
            print("   ⚠️ 量化分析模块未初始化")
        
        # 测试标签页
        if hasattr(analysis_widget, 'tab_widget'):
            tab_count = analysis_widget.tab_widget.count()
            print(f"   ✅ 分析标签页数量: {tab_count}")
        
        # 处理事件
        app.processEvents()
        
        # 关闭组件
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析中心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_center_functionality():
    """测试策略中心功能"""
    print("\n🔍 测试策略中心功能...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.strategy_center_widget import StrategyCenterWidget
        strategy_widget = StrategyCenterWidget()
        
        # 测试组件显示
        strategy_widget.show()
        print("   ✅ 策略中心组件显示成功")
        
        # 测试标签页
        if hasattr(strategy_widget, 'tab_widget'):
            tab_count = strategy_widget.tab_widget.count()
            print(f"   ✅ 策略标签页数量: {tab_count}")
        
        # 处理事件
        app.processEvents()
        
        # 关闭组件
        strategy_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 策略中心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    print("\n🔍 测试技术指标计算...")
    
    try:
        from analysis.technical_indicators import TechnicalIndicators
        import pandas as pd
        import numpy as np
        
        # 创建技术指标实例
        ti = TechnicalIndicators()
        print("   ✅ 技术指标实例创建成功")
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        test_data = pd.DataFrame({
            'open': prices + np.random.randn(100) * 0.1,
            'high': prices + np.abs(np.random.randn(100) * 0.2),
            'low': prices - np.abs(np.random.randn(100) * 0.2),
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 100)
        }, index=dates)
        
        print("   ✅ 测试数据创建成功")
        
        # 测试基本指标
        sma_5 = ti.sma(test_data['close'], 5)
        print(f"   ✅ SMA5计算成功，最新值: {sma_5.iloc[-1]:.2f}")
        
        rsi = ti.rsi(test_data['close'])
        print(f"   ✅ RSI计算成功，最新值: {rsi.iloc[-1]:.2f}")
        
        macd, signal, histogram = ti.macd(test_data['close'])
        print(f"   ✅ MACD计算成功，最新值: {macd.iloc[-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("基础功能验证测试")
    print("=" * 50)
    
    # 测试1: 主窗口功能
    main_window_ok = test_main_window_functionality()
    
    # 测试2: 分析中心功能
    analysis_ok = test_analysis_center_functionality()
    
    # 测试3: 策略中心功能
    strategy_ok = test_strategy_center_functionality()
    
    # 测试4: 技术指标计算
    indicators_ok = test_technical_indicators()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"主窗口功能: {'✅ 通过' if main_window_ok else '❌ 失败'}")
    print(f"分析中心功能: {'✅ 通过' if analysis_ok else '❌ 失败'}")
    print(f"策略中心功能: {'✅ 通过' if strategy_ok else '❌ 失败'}")
    print(f"技术指标计算: {'✅ 通过' if indicators_ok else '❌ 失败'}")
    
    all_passed = main_window_ok and analysis_ok and strategy_ok and indicators_ok
    
    if all_passed:
        print("\n🎉 所有基础功能测试通过！程序基础功能正常！")
        return True
    else:
        print("\n❌ 基础功能测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的分析中心组件
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_analysis_center_import():
    """测试分析中心组件导入"""
    print("🔍 测试分析中心组件导入...")
    
    try:
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        print("✅ 分析中心组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 分析中心组件导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_center_creation():
    """测试分析中心组件创建"""
    print("\n🔍 测试分析中心组件创建...")
    
    try:
        app = QApplication([])
        
        # 创建分析中心组件
        analysis_widget = AnalysisCenterWidget()
        print("✅ 分析中心组件创建成功")
        
        # 测试显示
        main_window = QMainWindow()
        main_window.setCentralWidget(analysis_widget)
        main_window.setWindowTitle("分析中心测试")
        main_window.resize(800, 600)
        
        print("✅ 分析中心组件界面创建成功")
        
        # 不显示窗口，只测试创建
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 分析中心组件创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("分析中心组件修复测试")
    print("=" * 50)
    
    # 测试1: 导入测试
    test1_result = test_analysis_center_import()
    
    # 测试2: 创建测试
    test2_result = test_analysis_center_creation()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"导入测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"创建测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！分析中心组件修复成功！")
        return True
    else:
        print("\n❌ 测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

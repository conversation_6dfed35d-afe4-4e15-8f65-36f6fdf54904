#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复脚本 - 修复所有语法错误
"""

import re
from pathlib import Path

def fix_data_center_widget():
    """修复data_center_widget.py中的语法错误"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "data_center_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复addWidget语法错误
        fixes = [
            ('layout.addWidget_股票代码_label, 0, 0)', 'params_layout.addWidget(股票代码_label, 0, 0)'),
            ('layout.addWidget_开始日期_label, 1, 0)', 'params_layout.addWidget(开始日期_label, 1, 0)'),
            ('layout.addWidget_结束日期_label, 2, 0)', 'params_layout.addWidget(结束日期_label, 2, 0)'),
            ('layout.addWidget_数据类型_label, 3, 0)', 'params_layout.addWidget(数据类型_label, 3, 0)'),
            ('layout.addWidget_股票代码_label, 0, 0)', 'query_layout.addWidget(股票代码_label, 0, 0)'),
            ('layout.addWidget_日期范围_label, 1, 0)', 'query_layout.addWidget(日期范围_label, 1, 0)'),
        ]
        
        for old, new in fixes:
            content = content.replace(old, new)
        
        # 修复重复的setStyleSheet
        content = re.sub(r'(\w+\.setStyleSheet\("color: #ffffff !important; background-color: transparent !important;"\))\s*\n\s*\1\.setStyleSheet\("color: #ffffff; background-color: transparent;"\)', r'\1', content)
        
        # 修复导入问题
        content = content.replace('from datetime import datetime, timedelta', '# from datetime import datetime, timedelta')
        content = content.replace('QFrame, QScrollArea, QSplitter,', '# QFrame, QScrollArea, QSplitter,')
        content = content.replace('QTimer, pyqtSignal, QDate, QThread', 'pyqtSignal, QDate, QThread')
        content = content.replace('QPalette, QColor', '# QPalette, QColor')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 data_center_widget.py")
        return True
        
    except Exception as e:
        print(f"❌ 修复 data_center_widget.py 失败: {e}")
        return False

def fix_all_widgets():
    """修复所有widget文件中的语法错误"""
    widgets_dir = Path(__file__).parent / "gui" / "widgets"
    
    widget_files = [
        "analysis_center_widget.py",
        "strategy_center_widget.py", 
        "trading_center_widget.py",
        "dashboard_widget.py",
    ]
    
    for widget_file in widget_files:
        file_path = widgets_dir / widget_file
        
        if not file_path.exists():
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复addWidget语法错误
            content = re.sub(r'layout\.addWidget_(\w+)', r'layout.addWidget(\1', content)
            content = re.sub(r'layout\.addWidget\((\w+_label)\)', r'layout.addWidget(\1)', content)
            
            # 修复缺少的括号
            content = re.sub(r'layout\.addWidget\((\w+_label),\s*(\d+),\s*(\d+)\)', r'layout.addWidget(\1, \2, \3)', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 修复了 {widget_file}")
            else:
                print(f"ℹ️  {widget_file} 无需修复")
                
        except Exception as e:
            print(f"❌ 修复 {widget_file} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("紧急修复脚本")
    print("=" * 60)
    
    # 修复data_center_widget
    fix_data_center_widget()
    
    # 修复其他widget
    fix_all_widgets()
    
    print("\n修复完成!")

if __name__ == "__main__":
    main()

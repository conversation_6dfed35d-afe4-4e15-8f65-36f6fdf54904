#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强数据下载功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    try:
        print("测试导入模块...")
        
        from gui.widgets.enhanced_download_widget import (
            EnhancedDownloadWidget, DownloadTaskManager, BatchDownloadThread
        )
        print("✓ 增强下载组件导入成功")
        
        from gui.widgets.data_center_widget import DataCenterWidget
        print("✓ 数据中心组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_creation():
    """测试组件创建"""
    try:
        print("\n测试组件创建...")
        
        # 需要QApplication才能创建Qt组件
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_download_widget import (
            EnhancedDownloadWidget, DownloadTaskManager
        )
        
        # 测试任务管理器
        task_manager = DownloadTaskManager()
        print("✓ 下载任务管理器创建成功")
        
        # 测试增强下载组件
        enhanced_download = EnhancedDownloadWidget()
        print("✓ 增强下载组件创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager_functionality():
    """测试任务管理器功能"""
    try:
        print("\n测试任务管理器功能...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_download_widget import DownloadTaskManager
        
        task_manager = DownloadTaskManager()
        
        # 测试设置股票代码
        test_symbols = "000001\n000002\n600000"
        task_manager.symbols_input.setPlainText(test_symbols)
        print("✓ 股票代码设置成功")
        
        # 测试创建任务
        task_manager.create_download_tasks()
        print("✓ 下载任务创建成功")
        
        # 检查任务数量
        task_count = len(task_manager.download_tasks)
        print(f"✓ 创建了 {task_count} 个下载任务")
        
        return True
        
    except Exception as e:
        print(f"✗ 任务管理器功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_center_integration():
    """测试数据中心集成"""
    try:
        print("\n测试数据中心集成...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.data_center_widget import DataCenterWidget
        
        data_center = DataCenterWidget()
        print("✓ 数据中心组件创建成功")
        
        # 检查标签页数量
        tab_count = data_center.tab_widget.count()
        print(f"✓ 标签页数量: {tab_count}")
        
        # 检查数据下载标签页
        download_tab_index = -1
        for i in range(tab_count):
            if data_center.tab_widget.tabText(i) == "数据下载":
                download_tab_index = i
                break
        
        if download_tab_index >= 0:
            print("✓ 数据下载标签页集成成功")
            
            # 检查是否是增强版组件
            download_widget = data_center.tab_widget.widget(download_tab_index)
            if hasattr(download_widget, 'batch_manager'):
                print("✓ 增强下载功能集成成功")
            else:
                print("✗ 增强下载功能集成失败")
                return False
        else:
            print("✗ 数据下载标签页集成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据中心集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_download_thread():
    """测试批量下载线程"""
    try:
        print("\n测试批量下载线程...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_download_widget import BatchDownloadThread
        
        # 创建测试任务
        test_tasks = [
            {
                'symbol': '000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'data_type': '日线数据'
            },
            {
                'symbol': '000002',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'data_type': '日线数据'
            }
        ]
        
        # 创建下载线程
        download_thread = BatchDownloadThread(test_tasks)
        print("✓ 批量下载线程创建成功")
        
        # 测试停止功能
        download_thread.stop()
        print("✓ 下载线程停止功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 批量下载线程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("增强数据下载功能测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("组件创建", test_component_creation),
        ("任务管理器功能", test_task_manager_functionality),
        ("数据中心集成", test_data_center_integration),
        ("批量下载线程", test_batch_download_thread)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强数据下载功能正常")
        print("\n新增功能特性:")
        print("✓ 批量下载 - 支持多股票同时下载")
        print("✓ 任务管理 - 可视化任务队列和状态")
        print("✓ 多数据类型 - 支持日线、周线、月线等")
        print("✓ 文件导入 - 从文件导入股票代码列表")
        print("✓ 全市场获取 - 一键获取全市场股票")
        print("✓ 并发控制 - 可配置并发下载数量")
        print("✓ 重试机制 - 智能重试失败的下载")
        print("✓ 进度监控 - 实时显示下载进度和状态")
        print("✓ 完美集成 - 无缝集成到数据中心")
        return 0
    else:
        print("❌ 部分测试失败，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())

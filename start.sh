#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "         量化交易系统 v1.0.0"
echo "========================================"
echo -e "${NC}"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ 错误：未检测到Python环境${NC}"
    echo "请先安装Python 3.8或更高版本"
    echo "Ubuntu/Debian: sudo apt-get install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo -e "${GREEN}✓ Python环境检测正常${NC}"

# 检查虚拟环境
if [ -d "venv" ]; then
    echo -e "${GREEN}✓ 检测到虚拟环境，正在激活...${NC}"
    source venv/bin/activate
else
    echo -e "${YELLOW}⚠ 未检测到虚拟环境${NC}"
    echo "建议创建虚拟环境以避免依赖冲突"
    echo
    read -p "是否创建虚拟环境？(y/n): " choice
    if [[ $choice == [Yy]* ]]; then
        echo "正在创建虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        echo -e "${GREEN}✓ 虚拟环境创建成功${NC}"
    fi
fi

# 检查依赖包
echo
echo "正在检查依赖包..."
python3 -c "import PyQt5, pandas, numpy, akshare, sqlalchemy, loguru" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 缺少必要的依赖包${NC}"
    echo
    read -p "是否自动安装依赖包？(y/n): " install
    if [[ $install == [Yy]* ]]; then
        echo "正在安装依赖包，请稍候..."
        pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 依赖包安装失败${NC}"
            echo "请手动运行：pip3 install -r requirements.txt"
            exit 1
        fi
        echo -e "${GREEN}✓ 依赖包安装成功${NC}"
    else
        echo "请手动安装依赖包：pip3 install -r requirements.txt"
        exit 1
    fi
else
    echo -e "${GREEN}✓ 依赖包检查通过${NC}"
fi

# 检查数据库
echo
echo "正在初始化数据库..."
python3 -c "from data.database.manager import DatabaseManager; DatabaseManager()" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 数据库初始化失败${NC}"
    exit 1
else
    echo -e "${GREEN}✓ 数据库初始化成功${NC}"
fi

# 启动程序
echo
echo -e "${BLUE}🚀 正在启动量化交易系统...${NC}"
echo
python3 main.py

# 程序结束处理
echo
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 程序运行出现错误${NC}"
    echo "请查看日志文件：logs/error.log"
else
    echo -e "${GREEN}✓ 程序正常退出${NC}"
fi

echo
echo -e "${BLUE}感谢使用量化交易系统！${NC}"

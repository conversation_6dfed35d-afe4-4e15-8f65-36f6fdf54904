#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面标签修复脚本
一次性修复所有GUI组件中的标签颜色问题
"""

import os
import re
from pathlib import Path

def fix_label_styles():
    """修复所有标签样式"""
    
    # 项目根目录
    project_root = Path(__file__).parent
    widgets_dir = project_root / "gui" / "widgets"
    
    # 需要修复的文件列表
    widget_files = [
        "analysis_center_widget.py",
        "strategy_center_widget.py", 
        "trading_center_widget.py",
        "settings_widget.py",
        "data_center_widget.py",
        "dashboard_widget.py",
        "data_dashboard_widget.py",
        "chart_widget.py",
        "data_source_manager_widget.py",
        "enhanced_data_view_widget.py",
        "enhanced_download_widget.py",
        "realtime_monitor_widget.py"
    ]
    
    # 标签样式字符串
    label_style = 'setStyleSheet("color: #ffffff !important; background-color: transparent !important;")'
    
    print("开始全面修复标签样式...")
    
    for widget_file in widget_files:
        file_path = widgets_dir / widget_file
        
        if not file_path.exists():
            print(f"⚠️  文件不存在: {widget_file}")
            continue
            
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复模式1: QLabel("文本") 直接创建的标签
            pattern1 = r'(\s+)(\w+_layout\.addWidget\(QLabel\("([^"]+)"\),)'
            def replace1(match):
                indent = match.group(1)
                label_text = match.group(3)
                var_name = label_text.lower().replace(" ", "_").replace(":", "").replace("(", "").replace(")", "") + "_label"
                return f'{indent}{var_name} = QLabel("{label_text}")\n{indent}{var_name}.{label_style}\n{indent}layout.addWidget({var_name},'
            
            content = re.sub(pattern1, replace1, content)
            
            # 修复模式2: layout.addWidget(QLabel("文本"), row, col) 网格布局
            pattern2 = r'(\s+)(\w+_layout\.addWidget\(QLabel\("([^"]+)"\),\s*\d+,\s*\d+\))'
            def replace2(match):
                indent = match.group(1)
                original_line = match.group(2)
                label_text = match.group(3)
                var_name = label_text.lower().replace(" ", "_").replace(":", "").replace("(", "").replace(")", "") + "_label"
                # 提取行列信息
                row_col = re.search(r',\s*(\d+),\s*(\d+)\)', original_line)
                if row_col:
                    row, col = row_col.groups()
                    return f'{indent}{var_name} = QLabel("{label_text}")\n{indent}{var_name}.{label_style}\n{indent}layout.addWidget({var_name}, {row}, {col})'
                return match.group(0)
            
            content = re.sub(pattern2, replace2, content)
            
            # 修复模式3: 已存在的标签变量但没有样式
            # 查找所有 self.xxx_label = QLabel(...) 的行
            pattern3 = r'(\s+)(self\.\w+_label\s*=\s*QLabel\([^)]+\))'
            def replace3(match):
                indent = match.group(1)
                label_line = match.group(2)
                var_name = re.search(r'self\.(\w+_label)', label_line).group(1)
                return f'{indent}{label_line}\n{indent}self.{var_name}.{label_style}'
            
            content = re.sub(pattern3, replace3, content)
            
            # 检查是否有修改
            if content != original_content:
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 已修复: {widget_file}")
            else:
                print(f"ℹ️  无需修复: {widget_file}")
                
        except Exception as e:
            print(f"❌ 修复失败 {widget_file}: {e}")
    
    print("\n标签样式修复完成!")

def fix_main_window_labels():
    """修复主窗口标签"""
    main_window_path = Path(__file__).parent / "gui" / "main_window.py"
    
    if not main_window_path.exists():
        print("⚠️  主窗口文件不存在")
        return
    
    try:
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复状态栏标签
        if 'self.status_label = QLabel("就绪")' in content and 'setStyleSheet' not in content:
            content = content.replace(
                'self.status_label = QLabel("就绪")',
                'self.status_label = QLabel("就绪")\n        self.status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")'
            )
        
        if 'self.time_label = QLabel()' in content:
            content = content.replace(
                'self.time_label = QLabel()',
                'self.time_label = QLabel()\n        self.time_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")'
            )
        
        if content != original_content:
            with open(main_window_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 已修复主窗口标签")
        else:
            print("ℹ️  主窗口无需修复")
            
    except Exception as e:
        print(f"❌ 修复主窗口失败: {e}")

def enhance_dark_theme():
    """增强深色主题"""
    theme_path = Path(__file__).parent / "gui" / "styles" / "dark_theme.py"
    
    if not theme_path.exists():
        print("⚠️  主题文件不存在")
        return
    
    try:
        with open(theme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保包含强制样式
        if '!important' not in content:
            # 在QLabel样式后添加强制样式
            enhanced_styles = '''
        
        /* ========== 强制样式覆盖 ========== */
        QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QGroupBox QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QTabWidget QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QWidget QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QFrame QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QScrollArea QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QSplitter QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        /* 确保复选框文字可见 */
        QCheckBox {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        /* 确保列表项文字可见 */
        QListWidget {
            color: #ffffff !important;
            background-color: #3c3c3c !important;
        }
        
        QListWidget::item {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QListWidget::item:selected {
            background-color: #0078d4 !important;
            color: #ffffff !important;
        }
        
        /* 确保表格文字可见 */
        QTableWidget {
            color: #ffffff !important;
            background-color: #3c3c3c !important;
        }
        
        QTableWidget::item {
            color: #ffffff !important;
            background-color: transparent !important;
        }
        
        QTableWidget::item:selected {
            background-color: #0078d4 !important;
            color: #ffffff !important;
        }
        
        QHeaderView::section {
            color: #ffffff !important;
            background-color: #4a4a4a !important;
        }
        '''
            
            # 在样式表结尾前插入增强样式
            content = content.replace('        """', enhanced_styles + '\n        """')
            
            with open(theme_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 已增强深色主题")
        else:
            print("ℹ️  深色主题已包含强制样式")
            
    except Exception as e:
        print(f"❌ 增强主题失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("全面标签修复工具")
    print("=" * 60)
    
    # 1. 修复所有widget文件的标签
    fix_label_styles()
    
    # 2. 修复主窗口标签
    fix_main_window_labels()
    
    # 3. 增强深色主题
    enhance_dark_theme()
    
    print("\n" + "=" * 60)
    print("修复完成! 请重新启动程序查看效果")
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
支持PDF、Excel、HTML等格式的报告导出
"""

import os
import json
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from utils.logger import get_logger

# 尝试导入可选依赖
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_agg import FigureCanvasAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


@dataclass
class ReportData:
    """报告数据结构"""
    title: str
    period_start: datetime
    period_end: datetime
    summary: Dict[str, Any]
    performance_metrics: Dict[str, float]
    positions: List[Dict[str, Any]]
    trades: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]


class ReportGenerator:
    """报告生成器基类"""
    
    def __init__(self):
        self.logger = get_logger("ReportGenerator")
        self.output_dir = Path("reports")
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_report(self, data: ReportData, format_type: str = "pdf") -> Optional[str]:
        """生成报告"""
        try:
            if format_type.lower() == "pdf":
                return self.generate_pdf_report(data)
            elif format_type.lower() == "excel":
                return self.generate_excel_report(data)
            elif format_type.lower() == "html":
                return self.generate_html_report(data)
            else:
                self.logger.error(f"不支持的报告格式: {format_type}")
                return None
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return None


class PDFReportGenerator(ReportGenerator):
    """PDF报告生成器"""
    
    def generate_pdf_report(self, data: ReportData) -> Optional[str]:
        """生成PDF报告"""
        if not REPORTLAB_AVAILABLE:
            self.logger.error("ReportLab未安装，无法生成PDF报告")
            return None
        
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trading_report_{timestamp}.pdf"
            filepath = self.output_dir / filename
            
            # 创建PDF文档
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # 添加标题
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # 居中
            )
            story.append(Paragraph(data.title, title_style))
            story.append(Spacer(1, 12))
            
            # 添加报告期间
            period_text = f"报告期间: {data.period_start.strftime('%Y-%m-%d')} 至 {data.period_end.strftime('%Y-%m-%d')}"
            story.append(Paragraph(period_text, styles['Normal']))
            story.append(Spacer(1, 12))
            
            # 添加摘要
            story.append(Paragraph("执行摘要", styles['Heading2']))
            for key, value in data.summary.items():
                text = f"<b>{key}:</b> {value}"
                story.append(Paragraph(text, styles['Normal']))
            story.append(Spacer(1, 12))
            
            # 添加绩效指标
            story.append(Paragraph("绩效指标", styles['Heading2']))
            
            # 创建绩效指标表格
            metrics_data = [['指标', '数值']]
            for key, value in data.performance_metrics.items():
                if isinstance(value, float):
                    formatted_value = f"{value:.4f}"
                else:
                    formatted_value = str(value)
                metrics_data.append([key, formatted_value])
            
            metrics_table = Table(metrics_data)
            metrics_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(metrics_table)
            story.append(Spacer(1, 12))
            
            # 添加持仓信息
            if data.positions:
                story.append(Paragraph("当前持仓", styles['Heading2']))
                
                positions_data = [['股票代码', '数量', '成本价', '现价', '盈亏']]
                for pos in data.positions:
                    positions_data.append([
                        pos.get('symbol', ''),
                        str(pos.get('quantity', 0)),
                        f"{pos.get('avg_price', 0):.2f}",
                        f"{pos.get('current_price', 0):.2f}",
                        f"{pos.get('profit', 0):+.2f}"
                    ])
                
                positions_table = Table(positions_data)
                positions_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(positions_table)
                story.append(Spacer(1, 12))
            
            # 添加交易记录
            if data.trades:
                story.append(Paragraph("交易记录", styles['Heading2']))
                
                trades_data = [['时间', '股票代码', '方向', '数量', '价格', '金额']]
                for trade in data.trades[-10:]:  # 只显示最近10笔交易
                    trades_data.append([
                        trade.get('time', ''),
                        trade.get('symbol', ''),
                        trade.get('side', ''),
                        str(trade.get('quantity', 0)),
                        f"{trade.get('price', 0):.2f}",
                        f"{trade.get('amount', 0):.2f}"
                    ])
                
                trades_table = Table(trades_data)
                trades_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(trades_table)
            
            # 生成PDF
            doc.build(story)
            
            self.logger.info(f"PDF报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"生成PDF报告失败: {e}")
            return None


class ExcelReportGenerator(ReportGenerator):
    """Excel报告生成器"""
    
    def generate_excel_report(self, data: ReportData) -> Optional[str]:
        """生成Excel报告"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trading_report_{timestamp}.xlsx"
            filepath = self.output_dir / filename
            
            # 创建Excel写入器
            with pd.ExcelWriter(str(filepath), engine='openpyxl') as writer:
                
                # 摘要页
                summary_df = pd.DataFrame([
                    ['报告标题', data.title],
                    ['开始日期', data.period_start.strftime('%Y-%m-%d')],
                    ['结束日期', data.period_end.strftime('%Y-%m-%d')],
                    ['', ''],
                ] + [[k, v] for k, v in data.summary.items()])
                summary_df.to_excel(writer, sheet_name='摘要', index=False, header=False)
                
                # 绩效指标页
                metrics_df = pd.DataFrame([
                    [k, v] for k, v in data.performance_metrics.items()
                ], columns=['指标', '数值'])
                metrics_df.to_excel(writer, sheet_name='绩效指标', index=False)
                
                # 持仓页
                if data.positions:
                    positions_df = pd.DataFrame(data.positions)
                    positions_df.to_excel(writer, sheet_name='当前持仓', index=False)
                
                # 交易记录页
                if data.trades:
                    trades_df = pd.DataFrame(data.trades)
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            
            self.logger.info(f"Excel报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {e}")
            return None


class HTMLReportGenerator(ReportGenerator):
    """HTML报告生成器"""
    
    def generate_html_report(self, data: ReportData) -> Optional[str]:
        """生成HTML报告"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trading_report_{timestamp}.html"
            filepath = self.output_dir / filename
            
            # 生成HTML内容
            html_content = self._generate_html_content(data)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            return None
    
    def _generate_html_content(self, data: ReportData) -> str:
        """生成HTML内容"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{data.title}</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                h1 {{
                    color: #333;
                    text-align: center;
                    border-bottom: 2px solid #007bff;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #007bff;
                    margin-top: 30px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: center;
                }}
                th {{
                    background-color: #007bff;
                    color: white;
                }}
                tr:nth-child(even) {{
                    background-color: #f2f2f2;
                }}
                .summary {{
                    background-color: #e7f3ff;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                }}
                .positive {{
                    color: #28a745;
                }}
                .negative {{
                    color: #dc3545;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>{data.title}</h1>
                <p><strong>报告期间:</strong> {data.period_start.strftime('%Y-%m-%d')} 至 {data.period_end.strftime('%Y-%m-%d')}</p>
                
                <h2>执行摘要</h2>
                <div class="summary">
        """
        
        # 添加摘要
        for key, value in data.summary.items():
            html += f"<p><strong>{key}:</strong> {value}</p>\n"
        
        html += """
                </div>
                
                <h2>绩效指标</h2>
                <table>
                    <thead>
                        <tr>
                            <th>指标</th>
                            <th>数值</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # 添加绩效指标
        for key, value in data.performance_metrics.items():
            if isinstance(value, float):
                formatted_value = f"{value:.4f}"
                css_class = "positive" if value > 0 else "negative" if value < 0 else ""
            else:
                formatted_value = str(value)
                css_class = ""
            
            html += f'<tr><td>{key}</td><td class="{css_class}">{formatted_value}</td></tr>\n'
        
        html += """
                    </tbody>
                </table>
        """
        
        # 添加持仓信息
        if data.positions:
            html += """
                <h2>当前持仓</h2>
                <table>
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>数量</th>
                            <th>成本价</th>
                            <th>现价</th>
                            <th>盈亏</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            
            for pos in data.positions:
                profit = pos.get('profit', 0)
                profit_class = "positive" if profit > 0 else "negative" if profit < 0 else ""
                
                html += f"""
                    <tr>
                        <td>{pos.get('symbol', '')}</td>
                        <td>{pos.get('quantity', 0)}</td>
                        <td>{pos.get('avg_price', 0):.2f}</td>
                        <td>{pos.get('current_price', 0):.2f}</td>
                        <td class="{profit_class}">{profit:+.2f}</td>
                    </tr>
                """
            
            html += """
                    </tbody>
                </table>
            """
        
        # 添加交易记录
        if data.trades:
            html += """
                <h2>最近交易记录</h2>
                <table>
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>股票代码</th>
                            <th>方向</th>
                            <th>数量</th>
                            <th>价格</th>
                            <th>金额</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            
            for trade in data.trades[-20:]:  # 显示最近20笔交易
                side_class = "positive" if trade.get('side') == '买入' else "negative"
                
                html += f"""
                    <tr>
                        <td>{trade.get('time', '')}</td>
                        <td>{trade.get('symbol', '')}</td>
                        <td class="{side_class}">{trade.get('side', '')}</td>
                        <td>{trade.get('quantity', 0)}</td>
                        <td>{trade.get('price', 0):.2f}</td>
                        <td>{trade.get('amount', 0):.2f}</td>
                    </tr>
                """
            
            html += """
                    </tbody>
                </table>
            """
        
        html += """
                <div style="margin-top: 30px; text-align: center; color: #666;">
                    <p>报告生成时间: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
                    <p>量化交易系统 v1.0.0</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html


def create_sample_report_data() -> ReportData:
    """创建示例报告数据"""
    return ReportData(
        title="量化交易策略回测报告",
        period_start=datetime.now() - timedelta(days=30),
        period_end=datetime.now(),
        summary={
            "策略名称": "双移动平均线策略",
            "总交易次数": 25,
            "盈利交易": 15,
            "亏损交易": 10,
            "胜率": "60%"
        },
        performance_metrics={
            "总收益率": 0.1250,
            "年化收益率": 0.1800,
            "最大回撤": -0.0850,
            "夏普比率": 1.2500,
            "索提诺比率": 1.4200,
            "卡尔马比率": 2.1200
        },
        positions=[
            {
                "symbol": "000001.SZ",
                "quantity": 1000,
                "avg_price": 12.50,
                "current_price": 13.20,
                "profit": 700.0
            },
            {
                "symbol": "600036.SH",
                "quantity": 500,
                "avg_price": 36.00,
                "current_price": 35.20,
                "profit": -400.0
            }
        ],
        trades=[
            {
                "time": "2024-01-15 09:30:00",
                "symbol": "000001.SZ",
                "side": "买入",
                "quantity": 1000,
                "price": 12.50,
                "amount": 12500.0
            },
            {
                "time": "2024-01-16 14:30:00",
                "symbol": "600036.SH",
                "side": "买入",
                "quantity": 500,
                "price": 36.00,
                "amount": 18000.0
            }
        ],
        charts=[]
    )


# 报告生成器工厂
def create_report_generator(format_type: str) -> Optional[ReportGenerator]:
    """创建报告生成器"""
    if format_type.lower() == "pdf":
        return PDFReportGenerator()
    elif format_type.lower() == "excel":
        return ExcelReportGenerator()
    elif format_type.lower() == "html":
        return HTMLReportGenerator()
    else:
        logger = get_logger("ReportGeneratorFactory")
        logger.error(f"不支持的报告格式: {format_type}")
        return None

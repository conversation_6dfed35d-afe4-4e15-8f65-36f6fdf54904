#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AKShare数据采集器
"""

import pandas as pd
import akshare as ak
from typing import List, Optional
from datetime import datetime, timedelta

from .base_collector import BaseCollector


class AKShareCollector(BaseCollector):
    """AKShare数据采集器"""
    
    def __init__(self):
        super().__init__("AKShare")
        
    def connect(self) -> bool:
        """连接数据源"""
        try:
            # 测试连接
            test_data = ak.stock_zh_a_spot_em()
            if test_data is not None and not test_data.empty:
                self.is_connected = True
                self.logger.info("AKShare连接成功")
                return True
            else:
                self.logger.error("AKShare连接失败：无法获取测试数据")
                return False
        except Exception as e:
            self.logger.error(f"AKShare连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        self.is_connected = False
        self.logger.info("AKShare连接已断开")
    
    def get_stock_list(self, market: str = None) -> pd.DataFrame:
        """获取股票列表"""
        try:
            if not self.is_connected:
                self.connect()
            
            # 获取A股列表
            stock_list = ak.stock_zh_a_spot_em()
            
            if stock_list is None or stock_list.empty:
                self.logger.warning("获取股票列表失败")
                return pd.DataFrame()
            
            # 重命名列
            column_mapping = {
                '代码': 'symbol',
                '名称': 'name',
                '最新价': 'price',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '最高': 'high',
                '最低': 'low',
                '今开': 'open',
                '昨收': 'pre_close'
            }
            
            # 选择需要的列并重命名
            available_columns = [col for col in column_mapping.keys() if col in stock_list.columns]
            stock_list = stock_list[available_columns].rename(columns=column_mapping)
            
            # 添加市场信息
            stock_list['market'] = stock_list['symbol'].apply(self._get_market)
            
            # 过滤市场
            if market:
                stock_list = stock_list[stock_list['market'] == market]
            
            self.logger.info(f"获取股票列表成功，共 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_stock_data(self, 
                      symbol: str, 
                      start_date: str = None, 
                      end_date: str = None,
                      period: str = "daily") -> pd.DataFrame:
        """获取股票历史数据"""
        try:
            if not self.is_connected:
                self.connect()
            
            # 格式化股票代码
            symbol = self._format_akshare_symbol(symbol)
            
            # 设置默认日期
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            else:
                end_date = end_date.replace('-', '')
            
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            else:
                start_date = start_date.replace('-', '')
            
            # 根据周期获取数据
            if period == "daily":
                data = ak.stock_zh_a_hist(symbol=symbol, 
                                        start_date=start_date, 
                                        end_date=end_date, 
                                        adjust="qfq")  # 前复权
            elif period == "weekly":
                data = ak.stock_zh_a_hist(symbol=symbol, 
                                        start_date=start_date, 
                                        end_date=end_date, 
                                        period="weekly",
                                        adjust="qfq")
            elif period == "monthly":
                data = ak.stock_zh_a_hist(symbol=symbol, 
                                        start_date=start_date, 
                                        end_date=end_date, 
                                        period="monthly",
                                        adjust="qfq")
            else:
                self.logger.error(f"不支持的周期: {period}")
                return pd.DataFrame()
            
            if data is None or data.empty:
                self.logger.warning(f"获取 {symbol} 历史数据失败")
                return pd.DataFrame()
            
            # 重命名列
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            }
            
            # 选择需要的列并重命名
            available_columns = [col for col in column_mapping.keys() if col in data.columns]
            data = data[available_columns].rename(columns=column_mapping)
            
            # 设置日期索引
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
            
            # 添加股票代码
            data['symbol'] = symbol
            
            # 数据清理和验证
            data = self.clean_data(data)
            
            if self.validate_data(data):
                self.last_update = datetime.now()
                self.logger.info(f"获取 {symbol} 历史数据成功，共 {len(data)} 条记录")
                return data
            else:
                self.logger.warning(f"{symbol} 数据验证失败")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"获取 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_realtime_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        try:
            if not self.is_connected:
                self.connect()
            
            # 获取实时行情
            realtime_data = ak.stock_zh_a_spot_em()
            
            if realtime_data is None or realtime_data.empty:
                self.logger.warning("获取实时数据失败")
                return pd.DataFrame()
            
            # 格式化股票代码
            formatted_symbols = [self._format_akshare_symbol(symbol) for symbol in symbols]
            
            # 过滤指定股票
            realtime_data = realtime_data[realtime_data['代码'].isin(formatted_symbols)]
            
            # 重命名列
            column_mapping = {
                '代码': 'symbol',
                '名称': 'name',
                '最新价': 'price',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '最高': 'high',
                '最低': 'low',
                '今开': 'open',
                '昨收': 'pre_close'
            }
            
            # 选择需要的列并重命名
            available_columns = [col for col in column_mapping.keys() if col in realtime_data.columns]
            realtime_data = realtime_data[available_columns].rename(columns=column_mapping)
            
            # 添加时间戳
            realtime_data['timestamp'] = datetime.now()
            
            self.logger.info(f"获取实时数据成功，共 {len(realtime_data)} 只股票")
            return realtime_data
            
        except Exception as e:
            self.logger.error(f"获取实时数据失败: {e}")
            return pd.DataFrame()
    
    def get_index_data(self, index_code: str, 
                      start_date: str = None, 
                      end_date: str = None) -> pd.DataFrame:
        """获取指数数据"""
        try:
            if not self.is_connected:
                self.connect()
            
            # 设置默认日期
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            else:
                end_date = end_date.replace('-', '')
            
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            else:
                start_date = start_date.replace('-', '')
            
            # 获取指数数据
            data = ak.stock_zh_index_daily(symbol=index_code)
            
            if data is None or data.empty:
                self.logger.warning(f"获取指数 {index_code} 数据失败")
                return pd.DataFrame()
            
            # 过滤日期范围
            data['date'] = pd.to_datetime(data['date'])
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            data = data[(data['date'] >= start_dt) & (data['date'] <= end_dt)]
            
            # 设置索引
            data.set_index('date', inplace=True)
            
            self.logger.info(f"获取指数 {index_code} 数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            self.logger.error(f"获取指数 {index_code} 数据失败: {e}")
            return pd.DataFrame()
    
    def _format_akshare_symbol(self, symbol: str) -> str:
        """格式化AKShare股票代码"""
        # 移除市场后缀
        if '.' in symbol:
            symbol = symbol.split('.')[0]
        
        # 确保是6位数字
        symbol = symbol.zfill(6)
        
        return symbol
    
    def _get_market(self, symbol: str) -> str:
        """根据股票代码判断市场"""
        if symbol.startswith('0') or symbol.startswith('3'):
            return 'SZ'  # 深圳
        elif symbol.startswith('6'):
            return 'SH'  # 上海
        elif symbol.startswith('8') or symbol.startswith('4'):
            return 'BJ'  # 北京
        else:
            return 'Unknown'

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化分析模块
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import warnings
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from utils.logger import get_logger

warnings.filterwarnings('ignore')


class QuantitativeAnalysis:
    """量化分析类"""
    
    def __init__(self):
        self.logger = get_logger("QuantitativeAnalysis")
    
    def calculate_returns(self, prices: pd.Series, method: str = 'simple') -> pd.Series:
        """计算收益率"""
        try:
            if method == 'simple':
                returns = prices.pct_change()
            elif method == 'log':
                returns = np.log(prices / prices.shift(1))
            else:
                returns = prices.pct_change()
            
            return returns.dropna()
            
        except Exception as e:
            self.logger.error(f"计算收益率失败: {e}")
            return pd.Series()
    
    def calculate_volatility(self, returns: pd.Series, window: int = 20, annualized: bool = True) -> pd.Series:
        """计算波动率"""
        try:
            volatility = returns.rolling(window=window).std()
            
            if annualized:
                volatility = volatility * np.sqrt(252)  # 年化
            
            return volatility
            
        except Exception as e:
            self.logger.error(f"计算波动率失败: {e}")
            return pd.Series()
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        try:
            excess_returns = returns - risk_free_rate / 252  # 日化无风险利率
            sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            
            return sharpe_ratio
            
        except Exception as e:
            self.logger.error(f"计算夏普比率失败: {e}")
            return 0.0
    
    def calculate_max_drawdown(self, prices: pd.Series) -> Dict[str, float]:
        """计算最大回撤"""
        try:
            # 计算累计收益
            cumulative = (1 + self.calculate_returns(prices)).cumprod()
            
            # 计算历史最高点
            running_max = cumulative.expanding().max()
            
            # 计算回撤
            drawdown = (cumulative - running_max) / running_max
            
            # 最大回撤
            max_drawdown = drawdown.min()
            
            # 最大回撤开始和结束时间
            max_dd_end = drawdown.idxmin()
            max_dd_start = cumulative.loc[:max_dd_end].idxmax()
            
            # 回撤持续时间
            drawdown_duration = (max_dd_end - max_dd_start).days
            
            return {
                'max_drawdown': abs(max_drawdown),
                'max_drawdown_start': max_dd_start,
                'max_drawdown_end': max_dd_end,
                'drawdown_duration': drawdown_duration
            }
            
        except Exception as e:
            self.logger.error(f"计算最大回撤失败: {e}")
            return {'max_drawdown': 0.0}
    
    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算风险价值(VaR)"""
        try:
            var = np.percentile(returns, confidence_level * 100)
            return abs(var)
            
        except Exception as e:
            self.logger.error(f"计算VaR失败: {e}")
            return 0.0
    
    def calculate_cvar(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算条件风险价值(CVaR)"""
        try:
            var = self.calculate_var(returns, confidence_level)
            cvar = returns[returns <= -var].mean()
            return abs(cvar)
            
        except Exception as e:
            self.logger.error(f"计算CVaR失败: {e}")
            return 0.0
    
    def calculate_beta(self, stock_returns: pd.Series, market_returns: pd.Series) -> float:
        """计算贝塔系数"""
        try:
            # 确保数据对齐
            aligned_data = pd.concat([stock_returns, market_returns], axis=1).dropna()
            
            if len(aligned_data) < 2:
                return 1.0
            
            stock_aligned = aligned_data.iloc[:, 0]
            market_aligned = aligned_data.iloc[:, 1]
            
            covariance = np.cov(stock_aligned, market_aligned)[0, 1]
            market_variance = np.var(market_aligned)
            
            if market_variance == 0:
                return 1.0
            
            beta = covariance / market_variance
            return beta
            
        except Exception as e:
            self.logger.error(f"计算贝塔系数失败: {e}")
            return 1.0
    
    def calculate_correlation_matrix(self, data: pd.DataFrame, method: str = 'pearson') -> pd.DataFrame:
        """计算相关性矩阵"""
        try:
            if method == 'pearson':
                correlation_matrix = data.corr(method='pearson')
            elif method == 'spearman':
                correlation_matrix = data.corr(method='spearman')
            else:
                correlation_matrix = data.corr(method='pearson')
            
            return correlation_matrix
            
        except Exception as e:
            self.logger.error(f"计算相关性矩阵失败: {e}")
            return pd.DataFrame()
    
    def perform_pca_analysis(self, data: pd.DataFrame, n_components: int = None) -> Dict:
        """主成分分析"""
        try:
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(data.dropna())
            
            # 执行PCA
            if n_components is None:
                n_components = min(len(data.columns), len(data))
            
            pca = PCA(n_components=n_components)
            principal_components = pca.fit_transform(scaled_data)
            
            # 创建主成分DataFrame
            pc_columns = [f'PC{i+1}' for i in range(n_components)]
            pc_df = pd.DataFrame(
                principal_components, 
                index=data.dropna().index, 
                columns=pc_columns
            )
            
            # 计算贡献度
            explained_variance_ratio = pca.explained_variance_ratio_
            cumulative_variance_ratio = np.cumsum(explained_variance_ratio)
            
            # 特征向量
            components_df = pd.DataFrame(
                pca.components_.T,
                index=data.columns,
                columns=pc_columns
            )
            
            return {
                'principal_components': pc_df,
                'explained_variance_ratio': explained_variance_ratio,
                'cumulative_variance_ratio': cumulative_variance_ratio,
                'components': components_df,
                'n_components': n_components
            }
            
        except Exception as e:
            self.logger.error(f"主成分分析失败: {e}")
            return {}
    
    def calculate_information_ratio(self, portfolio_returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """计算信息比率"""
        try:
            # 确保数据对齐
            aligned_data = pd.concat([portfolio_returns, benchmark_returns], axis=1).dropna()
            
            if len(aligned_data) < 2:
                return 0.0
            
            portfolio_aligned = aligned_data.iloc[:, 0]
            benchmark_aligned = aligned_data.iloc[:, 1]
            
            # 计算超额收益
            excess_returns = portfolio_aligned - benchmark_aligned
            
            # 信息比率 = 超额收益均值 / 超额收益标准差
            if excess_returns.std() == 0:
                return 0.0
            
            information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            return information_ratio
            
        except Exception as e:
            self.logger.error(f"计算信息比率失败: {e}")
            return 0.0
    
    def calculate_treynor_ratio(self, returns: pd.Series, beta: float, risk_free_rate: float = 0.03) -> float:
        """计算特雷诺比率"""
        try:
            if beta == 0:
                return 0.0
            
            excess_returns = returns.mean() * 252 - risk_free_rate  # 年化超额收益
            treynor_ratio = excess_returns / beta
            
            return treynor_ratio
            
        except Exception as e:
            self.logger.error(f"计算特雷诺比率失败: {e}")
            return 0.0
    
    def calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算索提诺比率"""
        try:
            excess_returns = returns - risk_free_rate / 252
            
            # 只考虑负收益的标准差
            downside_returns = excess_returns[excess_returns < 0]
            
            if len(downside_returns) == 0:
                return float('inf')
            
            downside_deviation = downside_returns.std()
            
            if downside_deviation == 0:
                return float('inf')
            
            sortino_ratio = excess_returns.mean() / downside_deviation * np.sqrt(252)
            return sortino_ratio
            
        except Exception as e:
            self.logger.error(f"计算索提诺比率失败: {e}")
            return 0.0
    
    def calculate_calmar_ratio(self, returns: pd.Series, prices: pd.Series) -> float:
        """计算卡玛比率"""
        try:
            annual_return = returns.mean() * 252
            max_drawdown_info = self.calculate_max_drawdown(prices)
            max_drawdown = max_drawdown_info['max_drawdown']
            
            if max_drawdown == 0:
                return float('inf')
            
            calmar_ratio = annual_return / max_drawdown
            return calmar_ratio
            
        except Exception as e:
            self.logger.error(f"计算卡玛比率失败: {e}")
            return 0.0
    
    def perform_risk_analysis(self, data: pd.DataFrame, benchmark_data: pd.Series = None) -> Dict:
        """综合风险分析"""
        try:
            results = {}
            
            for column in data.columns:
                prices = data[column].dropna()
                returns = self.calculate_returns(prices)
                
                if len(returns) < 2:
                    continue
                
                # 基础指标
                analysis = {
                    'annual_return': returns.mean() * 252,
                    'annual_volatility': returns.std() * np.sqrt(252),
                    'sharpe_ratio': self.calculate_sharpe_ratio(returns),
                    'sortino_ratio': self.calculate_sortino_ratio(returns),
                    'calmar_ratio': self.calculate_calmar_ratio(returns, prices),
                    'max_drawdown': self.calculate_max_drawdown(prices)['max_drawdown'],
                    'var_5': self.calculate_var(returns, 0.05),
                    'cvar_5': self.calculate_cvar(returns, 0.05),
                    'skewness': returns.skew(),
                    'kurtosis': returns.kurtosis()
                }
                
                # 如果有基准数据，计算相对指标
                if benchmark_data is not None:
                    benchmark_returns = self.calculate_returns(benchmark_data)
                    if len(benchmark_returns) > 0:
                        analysis['beta'] = self.calculate_beta(returns, benchmark_returns)
                        analysis['treynor_ratio'] = self.calculate_treynor_ratio(
                            returns, analysis['beta']
                        )
                        analysis['information_ratio'] = self.calculate_information_ratio(
                            returns, benchmark_returns
                        )
                
                results[column] = analysis
            
            self.logger.info(f"风险分析完成，分析了 {len(results)} 个标的")
            return results
            
        except Exception as e:
            self.logger.error(f"风险分析失败: {e}")
            return {}
    
    def generate_sample_market_data(self, symbols: List[str], days: int = 252) -> pd.DataFrame:
        """生成示例市场数据"""
        try:
            dates = pd.date_range(end=datetime.now(), periods=days, freq='D')
            data = {}
            
            np.random.seed(42)
            
            for i, symbol in enumerate(symbols):
                # 为每个股票设置不同的随机种子
                np.random.seed(42 + i)
                
                # 生成价格序列
                base_price = np.random.uniform(20, 100)
                returns = np.random.normal(0.0005, 0.02, days)
                
                # 添加一些趋势和周期性
                trend = np.linspace(0, 0.1, days) * np.random.uniform(-1, 1)
                cycle = 0.05 * np.sin(np.linspace(0, 4*np.pi, days)) * np.random.uniform(0.5, 1.5)
                
                returns = returns + trend/days + cycle/days
                
                prices = [base_price]
                for ret in returns[1:]:
                    prices.append(prices[-1] * (1 + ret))
                
                data[symbol] = prices
            
            df = pd.DataFrame(data, index=dates)
            
            self.logger.info(f"生成示例市场数据: {len(symbols)} 个标的, {days} 天")
            return df
            
        except Exception as e:
            self.logger.error(f"生成示例市场数据失败: {e}")
            return pd.DataFrame()

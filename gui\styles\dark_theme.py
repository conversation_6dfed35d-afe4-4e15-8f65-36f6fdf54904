#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深色主题样式
"""


class DarkTheme:
    """深色主题类"""

    @staticmethod
    def get_stylesheet():
        """获取深色主题样式表 - 全面修复版本"""
        return """
        /* ========== 全局样式 ========== */
        * {
            color: #ffffff;
            background-color: transparent;
        }

        /* 主窗口样式 */
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        /* 菜单栏样式 */
        QMenuBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border: none;
            padding: 2px;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            border-radius: 3px;
        }

        QMenuBar::item:selected {
            background-color: #4a4a4a;
        }

        QMenuBar::item:pressed {
            background-color: #5a5a5a;
        }

        /* 菜单样式 */
        QMenu {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 2px;
        }

        QMenu::item {
            padding: 5px 20px;
            border-radius: 3px;
        }

        QMenu::item:selected {
            background-color: #4a4a4a;
        }

        QMenu::separator {
            height: 1px;
            background-color: #555555;
            margin: 2px 0px;
        }

        /* 工具栏样式 */
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            padding: 2px;
            spacing: 2px;
        }

        QToolBar::separator {
            background-color: #555555;
            width: 1px;
            margin: 2px;
        }

        /* 状态栏样式 */
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #555555;
        }

        /* 标签页样式 */
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }

        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: #4a4a4a;
            border-bottom: 2px solid #0078d4;
        }

        QTabBar::tab:hover {
            background-color: #454545;
        }

        /* 按钮样式 */
        QPushButton {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
        }

        QPushButton:hover {
            background-color: #5a5a5a;
            border-color: #777777;
        }

        QPushButton:pressed {
            background-color: #3a3a3a;
        }

        QPushButton:disabled {
            background-color: #333333;
            color: #666666;
            border-color: #444444;
        }

        /* ========== 标签样式 - 全面覆盖 ========== */
        QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        /* 确保所有嵌套标签都有正确的文字颜色 */
        QGroupBox QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        QTabWidget QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        QWidget QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        QFrame QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        QScrollArea QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        QSplitter QLabel {
            color: #ffffff !important;
            background-color: transparent !important;
        }

        /* 输入框样式 */
        QLineEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        QLineEdit:focus {
            border-color: #0078d4;
        }

        /* 文本框样式 */
        QTextEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            border-radius: 3px;
        }

        /* 列表样式 */
        QListWidget {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            border-radius: 3px;
            outline: none;
        }

        QListWidget::item {
            padding: 4px;
            border-bottom: 1px solid #555555;
        }

        QListWidget::item:selected {
            background-color: #0078d4;
        }

        QListWidget::item:hover {
            background-color: #4a4a4a;
        }

        /* 表格样式 */
        QTableWidget {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            gridline-color: #555555;
            outline: none;
        }

        QTableWidget::item {
            padding: 4px;
            border: none;
        }

        QTableWidget::item:selected {
            background-color: #0078d4;
        }

        QHeaderView::section {
            background-color: #4a4a4a;
            color: #ffffff;
            padding: 6px;
            border: none;
            border-right: 1px solid #666666;
            border-bottom: 1px solid #666666;
        }

        /* 滚动条样式 */
        QScrollBar:vertical {
            background-color: #3c3c3c;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #666666;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #777777;
        }

        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            border: none;
            background: none;
        }

        QScrollBar:horizontal {
            background-color: #3c3c3c;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #666666;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::handle:horizontal:hover {
            background-color: #777777;
        }

        QScrollBar::add-line:horizontal,
        QScrollBar::sub-line:horizontal {
            border: none;
            background: none;
        }

        /* 分割器样式 */
        QSplitter::handle {
            background-color: #555555;
        }

        QSplitter::handle:horizontal {
            width: 2px;
        }

        QSplitter::handle:vertical {
            height: 2px;
        }

        /* 组合框样式 */
        QComboBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
        }

        QComboBox:hover {
            border-color: #777777;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #ffffff;
        }

        QComboBox QAbstractItemView {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            selection-background-color: #0078d4;
        }

        /* 复选框样式 */
        QCheckBox {
            color: #ffffff;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #666666;
            border-radius: 3px;
            background-color: #3c3c3c;
        }

        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        /* 单选框样式 */
        QRadioButton {
            color: #ffffff;
            spacing: 8px;
        }

        QRadioButton::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #666666;
            border-radius: 8px;
            background-color: #3c3c3c;
        }

        QRadioButton::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        /* 分组框样式 */
        QGroupBox {
            color: #ffffff;
            border: 1px solid #666666;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
            font-weight: bold;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #ffffff;
        }

        /* 数字输入框样式 */
        QSpinBox, QDoubleSpinBox {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
        }

        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #0078d4;
        }

        /* 滑块样式 */
        QSlider::groove:horizontal {
            border: 1px solid #666666;
            height: 8px;
            background: #3c3c3c;
            border-radius: 4px;
        }

        QSlider::handle:horizontal {
            background: #0078d4;
            border: 1px solid #0078d4;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }

        QSlider::handle:horizontal:hover {
            background: #106ebe;
        }

        /* 进度条样式 */
        QProgressBar {
            border: 1px solid #666666;
            border-radius: 5px;
            text-align: center;
            background-color: #3c3c3c;
            color: #ffffff;
        }

        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 4px;
        }

        /* 对话框样式 */
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        /* 消息框样式 */
        QMessageBox {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        QMessageBox QPushButton {
            min-width: 80px;
            padding: 6px 12px;
        }

        /* 确保所有可能的白色文字配白色背景问题都被修复 */
        QCheckBox {
            color: #ffffff;
            background-color: transparent;
        }

        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            background-color: #3c3c3c;
            border: 1px solid #666666;
            border-radius: 3px;
        }

        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }

        QCheckBox::indicator:checked::before {
            content: "✓";
            color: #ffffff;
            font-weight: bold;
        }

        /* 日期选择器样式 */
        QDateEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
        }

        QDateEdit::drop-down {
            background-color: #4a4a4a;
            border: none;
            width: 20px;
        }

        QDateEdit::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
        }

        /* 日历弹出框样式 */
        QCalendarWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        QCalendarWidget QTableView {
            background-color: #3c3c3c;
            color: #ffffff;
            selection-background-color: #0078d4;
        }

        /* 确保表格内容可见 */
        QTableWidget {
            gridline-color: #555555;
        }

        QTableWidget::item {
            color: #ffffff;
            background-color: transparent;
            padding: 4px;
        }

        QTableWidget::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }

        QTableWidget::item:hover {
            background-color: #454545;
        }

        /* 表格头部样式 */
        QHeaderView::section {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
        }

        QHeaderView::section:hover {
            background-color: #5a5a5a;
        }
        """

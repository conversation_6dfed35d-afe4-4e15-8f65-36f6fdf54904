#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基本面分析功能
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fundamental_analysis_module():
    """测试基本面分析模块"""
    print("🔍 测试基本面分析模块...")
    
    try:
        from analysis.fundamental_analysis import FundamentalAnalysis
        
        # 创建基本面分析实例
        fa = FundamentalAnalysis()
        print("   ✅ 基本面分析实例创建成功")
        
        # 测试分析功能
        test_symbol = "000001.SZ"
        analysis_result = fa.analyze_stock(test_symbol)
        
        if analysis_result:
            print(f"   ✅ 基本面分析完成: {test_symbol}")
            
            # 检查分析结果结构
            expected_keys = ['basic_info', 'financial_data', 'ratios', 'evaluation']
            for key in expected_keys:
                if key in analysis_result:
                    print(f"   ✅ 包含 {key} 数据")
                else:
                    print(f"   ⚠️ 缺少 {key} 数据")
            
            # 显示评估结果
            evaluation = analysis_result.get('evaluation', {})
            if evaluation:
                rating = evaluation.get('综合评级', 'N/A')
                score = evaluation.get('综合得分', 'N/A')
                print(f"   ✅ 综合评级: {rating}, 综合得分: {score}")
            
            return True
        else:
            print("   ❌ 基本面分析返回空结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 基本面分析模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fundamental_analysis_interface():
    """测试基本面分析界面"""
    print("\n🔍 测试基本面分析界面...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 切换到基本面分析标签页
        if hasattr(analysis_widget, 'tab_widget'):
            for i in range(analysis_widget.tab_widget.count()):
                tab_name = analysis_widget.tab_widget.tabText(i)
                if "基本面分析" in tab_name:
                    analysis_widget.tab_widget.setCurrentIndex(i)
                    print("   ✅ 切换到基本面分析标签页")
                    break
        
        # 处理事件
        app.processEvents()
        
        # 查找股票代码输入框
        from PyQt5.QtWidgets import QLineEdit, QPushButton, QTableWidget
        line_edits = analysis_widget.findChildren(QLineEdit)
        
        stock_input = None
        for edit in line_edits:
            if hasattr(edit, 'placeholderText'):
                placeholder = edit.placeholderText().lower()
                if '股票' in placeholder or 'symbol' in placeholder or '代码' in placeholder:
                    stock_input = edit
                    break
        
        if not stock_input and line_edits:
            stock_input = line_edits[0]
        
        if stock_input:
            # 输入测试股票代码
            test_symbol = "000001.SZ"
            stock_input.setText(test_symbol)
            print(f"   ✅ 输入股票代码: {test_symbol}")
            
            # 查找分析按钮
            buttons = analysis_widget.findChildren(QPushButton)
            
            analyze_button = None
            for button in buttons:
                if "分析" in button.text() and ("基本面" in button.text() or "财务" in button.text()):
                    analyze_button = button
                    break
            
            if not analyze_button:
                for button in buttons:
                    if "分析" in button.text():
                        analyze_button = button
                        break
            
            if analyze_button:
                print(f"   ✅ 找到分析按钮: {analyze_button.text()}")
                
                # 模拟点击分析按钮
                analyze_button.click()
                print("   ✅ 点击分析按钮")
                
                # 处理事件，等待分析完成
                for _ in range(10):
                    app.processEvents()
                
                print("   ✅ 基本面分析操作完成")
                
                # 检查结果表格
                tables = analysis_widget.findChildren(QTableWidget)
                
                table_count = 0
                for table in tables:
                    if table.rowCount() > 0:
                        table_count += 1
                        print(f"   ✅ 发现数据表格，行数: {table.rowCount()}")
                
                if table_count > 0:
                    print(f"   ✅ 共发现 {table_count} 个有数据的表格")
                else:
                    print("   ⚠️ 未发现有数据的表格")
            else:
                print("   ❌ 未找到分析按钮")
        else:
            print("   ❌ 未找到股票代码输入框")
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基本面分析界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("基本面分析功能测试")
    print("=" * 50)
    
    # 测试1: 基本面分析模块
    module_ok = test_fundamental_analysis_module()
    
    # 测试2: 基本面分析界面
    interface_ok = test_fundamental_analysis_interface()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"基本面分析模块: {'✅ 通过' if module_ok else '❌ 失败'}")
    print(f"基本面分析界面: {'✅ 通过' if interface_ok else '❌ 失败'}")
    
    all_passed = module_ok and interface_ok
    
    if all_passed:
        print("\n🎉 基本面分析功能测试通过！")
        return True
    else:
        print("\n❌ 基本面分析功能测试失败，需要进一步完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

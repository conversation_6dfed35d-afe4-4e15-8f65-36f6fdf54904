#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据推送模块
支持WebSocket实时行情数据
"""

import json
import time
import threading
import websocket
from datetime import datetime
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
from queue import Queue, Empty

from utils.logger import get_logger


@dataclass
class TickData:
    """Tick数据结构"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    bid_price: float
    ask_price: float
    bid_volume: int
    ask_volume: int

    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'price': self.price,
            'volume': self.volume,
            'bid_price': self.bid_price,
            'ask_price': self.ask_price,
            'bid_volume': self.bid_volume,
            'ask_volume': self.ask_volume
        }


@dataclass
class MarketDepth:
    """市场深度数据"""
    symbol: str
    timestamp: datetime
    bids: List[tuple]  # [(price, volume), ...]
    asks: List[tuple]  # [(price, volume), ...]

    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'bids': self.bids,
            'asks': self.asks
        }


class RealtimeDataFeed:
    """实时数据推送基类"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger("RealtimeDataFeed")
        self.is_connected = False
        self.subscribed_symbols = set()
        self.callbacks = {
            'tick': [],
            'depth': [],
            'trade': [],
            'error': []
        }
        self.data_queue = Queue()
        self.worker_thread = None
        self.stop_event = threading.Event()

        # WebSocket支持
        self.websocket_enabled = self.config.get('websocket_enabled', False)
        self.websocket_port = self.config.get('websocket_port', 8765)
        self.websocket_server = None
        self.websocket_clients = set()

        # 启动WebSocket服务器
        if self.websocket_enabled:
            self.start_websocket_server()

    def connect(self) -> bool:
        """连接到数据源"""
        raise NotImplementedError

    def disconnect(self):
        """断开连接"""
        self.stop_event.set()
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
        self.is_connected = False

    def subscribe(self, symbols: List[str], data_types: List[str] = None):
        """订阅股票数据"""
        raise NotImplementedError

    def unsubscribe(self, symbols: List[str]):
        """取消订阅"""
        raise NotImplementedError

    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)

    def remove_callback(self, event_type: str, callback: Callable):
        """移除回调函数"""
        if event_type in self.callbacks and callback in self.callbacks[event_type]:
            self.callbacks[event_type].remove(callback)

    def _emit_event(self, event_type: str, data: Any):
        """触发事件回调"""
        for callback in self.callbacks.get(event_type, []):
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"回调函数执行失败: {e}")

        # WebSocket推送
        if self.websocket_enabled and self.websocket_clients:
            self._broadcast_websocket(event_type, data)

    def _broadcast_websocket(self, event_type: str, data: Any):
        """WebSocket广播数据"""
        try:
            message = {
                'type': event_type,
                'data': data.to_dict() if hasattr(data, 'to_dict') else str(data),
                'timestamp': datetime.now().isoformat()
            }

            # 简化的同步广播
            import json
            message_str = json.dumps(message, ensure_ascii=False)

            # 移除断开的客户端
            disconnected_clients = set()

            for client in self.websocket_clients.copy():
                try:
                    # 这里需要异步处理，暂时记录
                    self.logger.debug(f"准备发送WebSocket消息: {message_str[:100]}...")
                except Exception:
                    disconnected_clients.add(client)

            # 清理断开的客户端
            self.websocket_clients -= disconnected_clients

        except Exception as e:
            self.logger.error(f"WebSocket广播失败: {e}")

    def start_websocket_server(self):
        """启动WebSocket服务器"""
        if not self.websocket_enabled:
            return

        try:
            # 简化的WebSocket服务器实现
            def run_server():
                import socket
                import threading

                server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server_socket.bind(('localhost', self.websocket_port))
                server_socket.listen(5)

                self.logger.info(f"WebSocket服务器启动在端口: {self.websocket_port}")

                while not self.stop_event.is_set():
                    try:
                        client_socket, address = server_socket.accept()
                        self.logger.info(f"WebSocket客户端连接: {address}")

                        # 简单的客户端处理
                        def handle_client(client):
                            try:
                                while not self.stop_event.is_set():
                                    data = client.recv(1024)
                                    if not data:
                                        break
                                    # 简单的心跳响应
                                    client.send(b"pong\n")
                            except:
                                pass
                            finally:
                                client.close()

                        client_thread = threading.Thread(target=handle_client, args=(client_socket,))
                        client_thread.daemon = True
                        client_thread.start()

                    except Exception as e:
                        if not self.stop_event.is_set():
                            self.logger.error(f"WebSocket服务器错误: {e}")
                        break

                server_socket.close()

            websocket_thread = threading.Thread(target=run_server, daemon=True)
            websocket_thread.start()

        except Exception as e:
            self.logger.error(f"启动WebSocket服务器失败: {e}")


class SinaRealtimeFeed(RealtimeDataFeed):
    """新浪实时数据源"""

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.base_url = "http://hq.sinajs.cn/list="
        self.update_interval = 1.0  # 更新间隔(秒)

    def connect(self) -> bool:
        """连接到新浪数据源"""
        try:
            self.is_connected = True
            self.stop_event.clear()

            # 启动数据获取线程
            self.worker_thread = threading.Thread(target=self._data_worker)
            self.worker_thread.daemon = True
            self.worker_thread.start()

            self.logger.info("新浪实时数据源连接成功")
            return True

        except Exception as e:
            self.logger.error(f"连接新浪数据源失败: {e}")
            return False

    def subscribe(self, symbols: List[str], data_types: List[str] = None):
        """订阅股票数据"""
        for symbol in symbols:
            # 转换股票代码格式
            sina_symbol = self._convert_symbol(symbol)
            self.subscribed_symbols.add(sina_symbol)

        self.logger.info(f"订阅股票: {symbols}")

    def unsubscribe(self, symbols: List[str]):
        """取消订阅"""
        for symbol in symbols:
            sina_symbol = self._convert_symbol(symbol)
            self.subscribed_symbols.discard(sina_symbol)

        self.logger.info(f"取消订阅股票: {symbols}")

    def _convert_symbol(self, symbol: str) -> str:
        """转换股票代码格式"""
        if symbol.endswith('.SZ'):
            return f"sz{symbol[:6]}"
        elif symbol.endswith('.SH'):
            return f"sh{symbol[:6]}"
        else:
            return symbol

    def _data_worker(self):
        """数据获取工作线程"""
        import requests

        retry_count = 0
        max_retries = 3

        while not self.stop_event.is_set() and self.is_connected:
            try:
                if not self.subscribed_symbols:
                    time.sleep(self.update_interval)
                    continue

                # 构建请求URL
                symbols_str = ','.join(self.subscribed_symbols)
                url = f"{self.base_url}{symbols_str}"

                # 获取数据，增加容错机制
                response = requests.get(url, timeout=10)
                response.encoding = 'gbk'

                if response.status_code == 200:
                    self._parse_sina_data(response.text)
                    retry_count = 0  # 重置重试计数
                else:
                    self.logger.warning(f"HTTP错误: {response.status_code}")
                    retry_count += 1

                time.sleep(self.update_interval)

            except requests.exceptions.Timeout:
                self.logger.warning("请求超时，重试中...")
                retry_count += 1
                if retry_count >= max_retries:
                    self.logger.error("连续超时次数过多，暂停数据获取")
                    time.sleep(self.update_interval * 5)  # 延长等待时间
                    retry_count = 0
                else:
                    time.sleep(self.update_interval)

            except requests.exceptions.ConnectionError:
                self.logger.warning("网络连接错误，重试中...")
                retry_count += 1
                if retry_count >= max_retries:
                    self.logger.error("网络连接持续失败，暂停数据获取")
                    time.sleep(self.update_interval * 10)  # 更长的等待时间
                    retry_count = 0
                else:
                    time.sleep(self.update_interval * 2)

            except Exception as e:
                self.logger.error(f"获取实时数据失败: {e}")
                self._emit_event('error', str(e))
                retry_count += 1
                if retry_count >= max_retries:
                    self.logger.error("错误次数过多，暂停数据获取")
                    time.sleep(self.update_interval * 5)
                    retry_count = 0
                else:
                    time.sleep(self.update_interval)

    def _parse_sina_data(self, data: str):
        """解析新浪数据"""
        try:
            lines = data.strip().split('\n')

            for line in lines:
                if '=' not in line:
                    continue

                # 解析数据行
                parts = line.split('=')
                if len(parts) < 2:
                    continue

                symbol_part = parts[0].strip()
                data_part = parts[1].strip().strip('";')

                # 提取股票代码
                if 'str_' in symbol_part:
                    sina_symbol = symbol_part.split('str_')[1]
                else:
                    continue

                # 解析数据字段
                fields = data_part.split(',')
                if len(fields) < 32:
                    continue

                # 转换回标准格式
                standard_symbol = self._convert_back_symbol(sina_symbol)

                # 创建Tick数据
                tick_data = TickData(
                    symbol=standard_symbol,
                    timestamp=datetime.now(),
                    price=float(fields[3]) if fields[3] else 0.0,
                    volume=int(fields[8]) if fields[8] else 0,
                    bid_price=float(fields[11]) if fields[11] else 0.0,
                    ask_price=float(fields[21]) if fields[21] else 0.0,
                    bid_volume=int(fields[10]) if fields[10] else 0,
                    ask_volume=int(fields[20]) if fields[20] else 0
                )

                # 触发回调
                self._emit_event('tick', tick_data)

        except Exception as e:
            self.logger.error(f"解析新浪数据失败: {e}")

    def _convert_back_symbol(self, sina_symbol: str) -> str:
        """转换回标准股票代码格式"""
        if sina_symbol.startswith('sz'):
            return f"{sina_symbol[2:]}.SZ"
        elif sina_symbol.startswith('sh'):
            return f"{sina_symbol[2:]}.SH"
        else:
            return sina_symbol


class TencentRealtimeFeed(RealtimeDataFeed):
    """腾讯实时数据源"""

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.base_url = "http://qt.gtimg.cn/q="
        self.update_interval = 1.0

    def connect(self) -> bool:
        """连接到腾讯数据源"""
        try:
            self.is_connected = True
            self.stop_event.clear()

            self.worker_thread = threading.Thread(target=self._data_worker)
            self.worker_thread.daemon = True
            self.worker_thread.start()

            self.logger.info("腾讯实时数据源连接成功")
            return True

        except Exception as e:
            self.logger.error(f"连接腾讯数据源失败: {e}")
            return False

    def subscribe(self, symbols: List[str], data_types: List[str] = None):
        """订阅股票数据"""
        for symbol in symbols:
            tencent_symbol = self._convert_symbol(symbol)
            self.subscribed_symbols.add(tencent_symbol)

        self.logger.info(f"订阅股票: {symbols}")

    def unsubscribe(self, symbols: List[str]):
        """取消订阅"""
        for symbol in symbols:
            tencent_symbol = self._convert_symbol(symbol)
            self.subscribed_symbols.discard(tencent_symbol)

        self.logger.info(f"取消订阅股票: {symbols}")

    def _convert_symbol(self, symbol: str) -> str:
        """转换股票代码格式"""
        if symbol.endswith('.SZ'):
            return f"sz{symbol[:6]}"
        elif symbol.endswith('.SH'):
            return f"sh{symbol[:6]}"
        else:
            return symbol

    def _data_worker(self):
        """数据获取工作线程"""
        import requests

        while not self.stop_event.is_set() and self.is_connected:
            try:
                if not self.subscribed_symbols:
                    time.sleep(self.update_interval)
                    continue

                symbols_str = ','.join(self.subscribed_symbols)
                url = f"{self.base_url}{symbols_str}"

                response = requests.get(url, timeout=5)
                response.encoding = 'gbk'

                if response.status_code == 200:
                    self._parse_tencent_data(response.text)

                time.sleep(self.update_interval)

            except Exception as e:
                self.logger.error(f"获取腾讯实时数据失败: {e}")
                self._emit_event('error', str(e))
                time.sleep(self.update_interval)

    def _parse_tencent_data(self, data: str):
        """解析腾讯数据"""
        try:
            lines = data.strip().split('\n')

            for line in lines:
                if '=' not in line:
                    continue

                parts = line.split('=')
                if len(parts) < 2:
                    continue

                symbol_part = parts[0].strip()
                data_part = parts[1].strip().strip('";')

                # 提取股票代码
                if 'v_' in symbol_part:
                    tencent_symbol = symbol_part.split('v_')[1]
                else:
                    continue

                # 解析数据字段
                fields = data_part.split('~')
                if len(fields) < 50:
                    continue

                # 转换回标准格式
                standard_symbol = self._convert_back_symbol(tencent_symbol)

                # 创建Tick数据，增加容错处理
                try:
                    price = float(fields[3]) if fields[3] and fields[3] != '0' else 0.0
                    volume = int(float(fields[6])) if fields[6] and fields[6] != '0' else 0
                    bid_price = float(fields[9]) if fields[9] and fields[9] != '0' else 0.0
                    ask_price = float(fields[10]) if fields[10] and fields[10] != '0' else 0.0
                    bid_volume = int(float(fields[11])) if fields[11] and fields[11] != '0' else 0
                    ask_volume = int(float(fields[12])) if fields[12] and fields[12] != '0' else 0

                    tick_data = TickData(
                        symbol=standard_symbol,
                        timestamp=datetime.now(),
                        price=price,
                        volume=volume,
                        bid_price=bid_price,
                        ask_price=ask_price,
                        bid_volume=bid_volume,
                        ask_volume=ask_volume
                    )
                except (ValueError, IndexError) as e:
                    self.logger.warning(f"解析腾讯数据字段失败: {e}, 字段: {fields[:15]}")
                    continue

                self._emit_event('tick', tick_data)

        except Exception as e:
            self.logger.error(f"解析腾讯数据失败: {e}")

    def _convert_back_symbol(self, tencent_symbol: str) -> str:
        """转换回标准股票代码格式"""
        if tencent_symbol.startswith('sz'):
            return f"{tencent_symbol[2:]}.SZ"
        elif tencent_symbol.startswith('sh'):
            return f"{tencent_symbol[2:]}.SH"
        else:
            return tencent_symbol


# 数据源工厂
FEED_CLASSES = {
    'sina': SinaRealtimeFeed,
    'tencent': TencentRealtimeFeed,
}


def create_realtime_feed(feed_type: str, config: Dict[str, Any] = None) -> Optional[RealtimeDataFeed]:
    """创建实时数据源"""
    feed_class = FEED_CLASSES.get(feed_type.lower())
    if feed_class:
        return feed_class(config)
    else:
        logger = get_logger("RealtimeFeedFactory")
        logger.error(f"不支持的数据源类型: {feed_type}")
        return None

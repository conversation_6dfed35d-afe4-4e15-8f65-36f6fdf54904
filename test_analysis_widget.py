#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析中心组件导入
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_analysis_widget_import():
    """测试分析中心组件导入"""
    print("=" * 60)
    print("🔍 测试分析中心组件导入")
    print("=" * 60)
    
    try:
        print("步骤1: 导入PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5导入成功")
        
        print("\n步骤2: 创建QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ QApplication创建成功")
        
        print("\n步骤3: 导入分析中心组件...")
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        print("✅ 分析中心组件导入成功！")
        
        print("\n步骤4: 创建组件实例...")
        widget = AnalysisCenterWidget()
        print("✅ 分析中心组件创建成功！")
        
        print("\n步骤5: 显示组件...")
        widget.show()
        print("✅ 分析中心组件显示成功！")
        
        # 处理事件
        app.processEvents()
        
        print("\n🎉 所有测试通过！分析中心组件工作正常！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_analysis_widget_import()
        
        if success:
            print("\n💡 分析中心组件修复成功！")
            print("   现在可以尝试运行完整的主程序了。")
        else:
            print("\n❌ 分析中心组件仍有问题，需要进一步调试。")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n💥 测试过程崩溃: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 测试完成，退出码: {exit_code}")
    input("\n按回车键退出...")
    sys.exit(exit_code)

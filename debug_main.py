#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试主程序启动问题
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step_by_step_debug():
    """逐步调试启动过程"""
    print("=" * 60)
    print("🔧 逐步调试量化交易系统启动")
    print("=" * 60)
    
    # 步骤1：测试基础导入
    print("\n📦 步骤1：测试基础导入")
    try:
        print("  导入 sys, os, pathlib...")
        import sys, os
        from pathlib import Path
        print("  ✅ 基础模块导入成功")
    except Exception as e:
        print(f"  ❌ 基础模块导入失败: {e}")
        return False
    
    # 步骤2：测试PyQt5
    print("\n🖥️ 步骤2：测试PyQt5")
    try:
        print("  导入 PyQt5.QtWidgets...")
        from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
        print("  导入 PyQt5.QtCore...")
        from PyQt5.QtCore import Qt
        print("  ✅ PyQt5导入成功")
    except Exception as e:
        print(f"  ❌ PyQt5导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤3：创建QApplication
    print("\n🚀 步骤3：创建QApplication")
    try:
        app = QApplication.instance()
        if app is None:
            print("  创建新的QApplication...")
            app = QApplication([])
        else:
            print("  使用现有的QApplication...")
        print("  ✅ QApplication创建成功")
    except Exception as e:
        print(f"  ❌ QApplication创建失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤4：测试简单窗口
    print("\n🏠 步骤4：创建简单测试窗口")
    try:
        print("  创建简单窗口...")
        window = QMainWindow()
        window.setWindowTitle("量化交易系统 - 启动测试")
        window.setGeometry(300, 300, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("🎉 量化交易系统启动测试成功！\n\n如果您能看到这个窗口，说明GUI正常工作。")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 16px; padding: 20px;")
        layout.addWidget(label)
        
        print("  显示窗口...")
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("  ✅ 简单窗口创建并显示成功")
        
        # 处理事件
        app.processEvents()
        
        print("\n🔄 运行短暂的事件循环测试...")
        import time
        start_time = time.time()
        
        while time.time() - start_time < 3:
            app.processEvents()
            time.sleep(0.1)
        
        print("  ✅ 事件循环测试完成")
        
    except Exception as e:
        print(f"  ❌ 简单窗口创建失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤5：测试项目模块导入
    print("\n📚 步骤5：测试项目模块导入")
    try:
        print("  导入 utils.logger...")
        from utils.logger import setup_logger, get_logger
        print("  ✅ utils.logger 导入成功")
        
        print("  设置日志系统...")
        setup_logger()
        logger = get_logger("DebugTest")
        logger.info("调试测试日志")
        print("  ✅ 日志系统设置成功")
        
    except Exception as e:
        print(f"  ❌ 项目模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤6：测试主窗口导入
    print("\n🏢 步骤6：测试主窗口模块导入")
    try:
        print("  导入 gui.main_window...")
        from gui.main_window import MainWindow
        print("  ✅ gui.main_window 导入成功")
        
        print("  注意：主窗口模块导入成功，但我们不在这里创建实例")
        print("        因为主窗口可能包含复杂的初始化逻辑")
        
    except Exception as e:
        print(f"  ❌ 主窗口模块导入失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("✅ 所有基础测试通过！")
    print("🖥️ 如果您看到了测试窗口，说明GUI环境正常")
    print("📝 问题可能在主窗口的复杂初始化过程中")
    print("=" * 60)
    
    return True

def main():
    """主函数"""
    try:
        success = step_by_step_debug()
        
        if success:
            print("\n💡 建议：")
            print("  1. GUI环境正常，问题可能在主窗口初始化")
            print("  2. 检查主窗口的__init__方法是否有耗时操作")
            print("  3. 检查数据连接或其他初始化是否阻塞")
            
            print("\n🔧 下一步调试：")
            print("  可以尝试简化主窗口初始化过程")
            print("  或者添加更多调试信息到MainWindow.__init__")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n💥 调试过程崩溃: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 调试完成，退出码: {exit_code}")
    input("\n按回车键退出...")
    sys.exit(exit_code)

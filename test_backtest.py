#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测引擎测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backtesting.backtest_engine import BacktestEngine
from strategies.strategy_factory import strategy_factory
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def test_backtest_engine():
    """测试回测引擎"""
    logger = setup_logger()
    logger.info("开始测试回测引擎")
    
    try:
        # 创建回测引擎
        print("创建回测引擎...")
        backtest_engine = BacktestEngine(
            initial_capital=1000000,  # 100万初始资金
            commission_rate=0.0003    # 0.03%手续费
        )
        print("✓ 回测引擎创建成功")
        
        # 获取测试数据
        print("\n获取测试数据...")
        collector = AKShareCollector()
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        # 获取多只股票数据
        symbols = ['000001.SZ', '600036.SH', '000002.SZ']
        
        for symbol in symbols:
            print(f"正在获取 {symbol} 数据...")
            data = collector.get_stock_data(symbol, start_date, end_date)
            
            if data is None or data.empty:
                print(f"无法获取 {symbol} 数据，使用模拟数据")
                data = generate_mock_data(symbol)
            
            success = backtest_engine.add_data(symbol, data)
            if success:
                print(f"✓ {symbol} 数据添加成功，数据量: {len(data)}")
            else:
                print(f"❌ {symbol} 数据添加失败")
        
        # 添加策略
        print("\n添加回测策略...")
        
        # 创建多个策略
        strategies_config = [
            ('MA', '移动平均策略', {'short_period': 5, 'long_period': 20}),
            ('MACD', 'MACD策略', {'fast_period': 12, 'slow_period': 26}),
            ('RSI', 'RSI策略', {'rsi_period': 14, 'oversold_level': 30}),
        ]
        
        for strategy_type, strategy_name, config in strategies_config:
            try:
                strategy = strategy_factory.create_strategy(strategy_type, strategy_name, config)
                if strategy:
                    success = backtest_engine.add_strategy(strategy)
                    if success:
                        print(f"✓ 策略添加成功: {strategy_name}")
                    else:
                        print(f"❌ 策略添加失败: {strategy_name}")
                else:
                    print(f"❌ 策略创建失败: {strategy_name}")
            except Exception as e:
                print(f"❌ 策略处理失败 {strategy_name}: {e}")
        
        # 设置回测周期
        print("\n设置回测周期...")
        backtest_start = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
        backtest_end = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        backtest_engine.set_period(backtest_start, backtest_end)
        print(f"✓ 回测周期设置: {backtest_start} 至 {backtest_end}")
        
        # 运行回测
        print("\n开始运行回测...")
        performance_metrics = backtest_engine.run_backtest()
        
        if performance_metrics:
            print("✓ 回测完成")
            
            # 显示回测结果
            print(f"\n{'='*60}")
            print("回测结果摘要")
            print(f"{'='*60}")
            
            summary = backtest_engine.get_portfolio_summary()
            for key, value in summary.items():
                print(f"{key}: {value}")
            
            # 显示详细指标
            print(f"\n{'='*60}")
            print("详细性能指标")
            print(f"{'='*60}")
            
            print(f"总收益率: {performance_metrics.get('total_return', 0)*100:.2f}%")
            print(f"年化收益率: {performance_metrics.get('annualized_return', 0)*100:.2f}%")
            print(f"年化波动率: {performance_metrics.get('volatility', 0)*100:.2f}%")
            print(f"夏普比率: {performance_metrics.get('sharpe_ratio', 0):.3f}")
            print(f"最大回撤: {performance_metrics.get('max_drawdown', 0)*100:.2f}%")
            print(f"总交易次数: {performance_metrics.get('total_trades', 0)}")
            print(f"胜率: {performance_metrics.get('win_rate', 0)*100:.2f}%")
            print(f"平均盈利: ¥{performance_metrics.get('avg_win', 0):,.2f}")
            print(f"平均亏损: ¥{performance_metrics.get('avg_loss', 0):,.2f}")
            print(f"盈亏比: {performance_metrics.get('profit_factor', 0):.2f}")
            
            # 策略统计
            strategy_stats = performance_metrics.get('strategy_stats', {})
            if strategy_stats:
                print(f"\n{'='*60}")
                print("策略统计")
                print(f"{'='*60}")
                
                for strategy_name, stats in strategy_stats.items():
                    print(f"\n{strategy_name}:")
                    print(f"  总信号数: {stats.get('total_signals', 0)}")
                    print(f"  买入信号: {stats.get('buy_signals', 0)}")
                    print(f"  卖出信号: {stats.get('sell_signals', 0)}")
                    print(f"  平均置信度: {stats.get('avg_confidence', 0):.3f}")
            
            # 显示最近的交易
            trades = performance_metrics.get('trades', [])
            if trades:
                print(f"\n{'='*60}")
                print("最近10笔交易")
                print(f"{'='*60}")
                
                recent_trades = trades[-10:]
                for trade in recent_trades:
                    direction = trade['direction']
                    symbol = trade['symbol']
                    quantity = trade['quantity']
                    price = trade['price']
                    date = trade['date']
                    pnl = trade.get('pnl', 0)
                    strategy = trade.get('strategy', 'Unknown')
                    
                    pnl_str = f"盈亏: ¥{pnl:+,.2f}" if direction == 'sell' else ""
                    print(f"{date.strftime('%Y-%m-%d')} {direction.upper()} {symbol} "
                          f"{quantity}股 @¥{price:.2f} [{strategy}] {pnl_str}")
            
            # 导出结果
            print(f"\n{'='*60}")
            print("导出回测结果")
            print(f"{'='*60}")
            
            export_success = backtest_engine.export_results("backtest_results")
            if export_success:
                print("✓ 回测结果导出成功")
            else:
                print("❌ 回测结果导出失败")
            
        else:
            print("❌ 回测失败")
        
        print(f"\n{'='*60}")
        print("回测引擎测试完成")
        print(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"回测引擎测试失败: {e}")
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def generate_mock_data(symbol: str):
    """生成模拟数据"""
    dates = pd.date_range(start='2023-01-01', end='2024-05-01', freq='D')
    
    # 生成模拟价格数据
    np.random.seed(hash(symbol) % 2**32)  # 基于symbol生成不同的随机种子
    base_price = 10.0 + (hash(symbol) % 20)  # 不同股票不同基础价格
    prices = []
    
    for i in range(len(dates)):
        # 添加趋势和随机波动
        trend = 0.0001 * i * (1 + (hash(symbol) % 3) * 0.5)  # 不同趋势
        noise = np.random.normal(0, 0.02)  # 2%的随机波动
        
        # 添加一些周期性模式
        cycle = 0.001 * np.sin(2 * np.pi * i / (30 + hash(symbol) % 20))
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise + cycle)
        
        prices.append(max(0.1, price))  # 确保价格为正
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = low + (high - low) * np.random.random()
        volume = int(np.random.normal(1000000, 200000))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': max(100000, volume)
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df


if __name__ == "__main__":
    test_backtest_engine()

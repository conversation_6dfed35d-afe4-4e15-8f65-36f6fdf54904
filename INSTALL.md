# 量化交易系统安装指南

## 系统要求

### 操作系统
- Windows 10/11 (推荐)
- macOS 10.15+ (兼容)
- Linux Ubuntu 18.04+ (兼容)

### Python环境
- Python 3.8 或更高版本
- pip 包管理器

## 安装步骤

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/quantitative-trading-system.git
cd quantitative-trading-system
```

### 2. 创建虚拟环境（推荐）
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖包

#### 方法一：一键安装（推荐）
```bash
pip install -r requirements.txt
```

#### 方法二：分步安装
```bash
# 核心依赖
pip install PyQt5==5.15.10
pip install pandas numpy scipy
pip install sqlalchemy loguru

# 数据源
pip install akshare tushare

# 分析工具
pip install matplotlib seaborn
pip install scikit-learn statsmodels

# 其他工具
pip install requests python-dateutil pytz
```

### 4. 验证安装
```bash
python test_complete_system.py
```

## 常见问题解决

### 1. PyQt5 安装失败

**问题**：在某些系统上PyQt5可能安装失败

**解决方案**：
```bash
# Windows
pip install PyQt5 --only-binary=all

# 或者使用conda
conda install pyqt

# macOS
brew install pyqt5
pip install PyQt5
```

### 2. TA-Lib 安装失败

**问题**：TA-Lib是C库，需要编译

**解决方案**：

#### Windows
```bash
# 下载预编译包
pip install TA-Lib --find-links https://www.lfd.uci.edu/~gohlke/pythonlibs/
```

#### macOS
```bash
brew install ta-lib
pip install TA-Lib
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install libta-lib-dev
pip install TA-Lib

# CentOS/RHEL
sudo yum install ta-lib-devel
pip install TA-Lib
```

### 3. 数据源配置

#### AKShare配置
AKShare无需额外配置，开箱即用。

#### Tushare配置
1. 注册Tushare账号：https://tushare.pro/
2. 获取API Token
3. 在程序中配置Token：
```python
from config.settings import Settings
Settings.set_tushare_token("your_token_here")
```

### 4. 数据库初始化

首次运行时，系统会自动创建SQLite数据库：
```bash
python -c "from data.database.manager import DatabaseManager; DatabaseManager()"
```

### 5. 内存不足问题

如果处理大量数据时遇到内存不足：

1. 增加虚拟内存
2. 减少数据处理批次大小
3. 使用数据分片处理

### 6. 网络连接问题

如果数据获取失败：

1. 检查网络连接
2. 配置代理（如需要）：
```python
import os
os.environ['HTTP_PROXY'] = 'http://proxy:port'
os.environ['HTTPS_PROXY'] = 'https://proxy:port'
```

## 性能优化建议

### 1. 硬件要求
- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **存储**: SSD硬盘（推荐）
- **网络**: 稳定的互联网连接

### 2. Python优化
```bash
# 安装性能优化包
pip install numba
pip install cython

# 使用更快的JSON库
pip install ujson
```

### 3. 数据库优化
- 定期清理历史数据
- 创建适当的索引
- 使用数据压缩

## 开发环境配置

### 1. 安装开发工具
```bash
pip install pytest black flake8
pip install jupyter notebook
```

### 2. 代码格式化
```bash
black . --line-length 88
flake8 . --max-line-length 88
```

### 3. 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试
python test_data_module.py
python test_analysis_module.py
python test_strategy_module.py
```

## 部署建议

### 1. 生产环境
- 使用专用服务器
- 配置定时任务
- 设置监控和告警
- 定期备份数据

### 2. 云端部署
- 支持AWS、阿里云、腾讯云
- 使用Docker容器化
- 配置负载均衡

### 3. 安全配置
- 设置防火墙
- 加密敏感数据
- 定期更新依赖包
- 使用HTTPS连接

## 故障排除

### 1. 日志查看
```bash
# 查看系统日志
tail -f logs/trading_system.log

# 查看错误日志
tail -f logs/error.log
```

### 2. 性能监控
```bash
# 查看系统资源使用
python -c "import psutil; print(f'CPU: {psutil.cpu_percent()}%, Memory: {psutil.virtual_memory().percent}%')"
```

### 3. 数据库检查
```bash
# 检查数据库连接
python -c "from data.database.manager import DatabaseManager; print('Database OK' if DatabaseManager().get_session() else 'Database Error')"
```

## 技术支持

如果遇到安装问题，请：

1. 查看错误日志
2. 搜索已知问题
3. 提交Issue到GitHub
4. 联系技术支持

### 联系方式
- 邮箱：<EMAIL>
- QQ群：123456789
- 微信群：扫描二维码加入

---

**注意**：首次安装可能需要较长时间下载依赖包，请耐心等待。建议使用国内镜像源加速下载：

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

# 量化交易系统依赖包
# 核心框架
PyQt5==5.15.10
PyQt5-tools==********.3

# 数据处理
pandas==2.1.4
numpy==1.24.3
scipy==1.11.4

# 技术分析
TA-Lib==0.4.28
talib-binary==0.4.19

# 机器学习
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.2
xgboost==2.0.3

# 数据可视化
matplotlib==3.8.2
pyqtgraph==0.13.3
plotly==5.17.0
seaborn==0.13.0

# 数据源
tushare==1.2.89
akshare==1.12.80
yfinance==0.2.28
ccxt==4.1.77

# 网络请求
requests==2.31.0
websocket-client==1.7.0
aiohttp==3.9.1

# 数据库
sqlite3
sqlalchemy==2.0.23
alembic==1.13.1

# 工具库
python-dateutil==2.8.2
pytz==2023.3
schedule==1.2.0
configparser==6.0.0
pyyaml==6.0.1
openpyxl==3.1.2

# 日志和监控
loguru==0.7.2
psutil==5.9.6

# 数学计算
sympy==1.12
statsmodels==0.14.1

# 加密和安全
cryptography==41.0.8
keyring==24.3.0

# 开发工具
pytest==7.4.3
black==23.12.0
flake8==6.1.0

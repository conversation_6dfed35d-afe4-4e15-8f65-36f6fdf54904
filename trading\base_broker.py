#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
券商接口基类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
import pandas as pd

from utils.logger import get_logger


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"  # 市价单
    LIMIT = "limit"    # 限价单
    STOP = "stop"      # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


class OrderSide(Enum):
    """买卖方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"      # 待成交
    PARTIAL = "partial"      # 部分成交
    FILLED = "filled"        # 已成交
    CANCELLED = "cancelled"  # 已撤销
    REJECTED = "rejected"    # 已拒绝


class Position:
    """持仓信息"""
    
    def __init__(self, symbol: str, quantity: int, avg_price: float, 
                 market_value: float, unrealized_pnl: float):
        self.symbol = symbol
        self.quantity = quantity
        self.avg_price = avg_price
        self.market_value = market_value
        self.unrealized_pnl = unrealized_pnl
        self.update_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'avg_price': self.avg_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'update_time': self.update_time
        }


class Order:
    """订单信息"""
    
    def __init__(self, order_id: str, symbol: str, side: OrderSide, 
                 order_type: OrderType, quantity: int, price: float = None):
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.order_type = order_type
        self.quantity = quantity
        self.price = price
        self.filled_quantity = 0
        self.avg_fill_price = 0.0
        self.status = OrderStatus.PENDING
        self.create_time = datetime.now()
        self.update_time = datetime.now()
        self.commission = 0.0
        self.error_message = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'status': self.status.value,
            'create_time': self.create_time,
            'update_time': self.update_time,
            'commission': self.commission,
            'error_message': self.error_message
        }


class AccountInfo:
    """账户信息"""
    
    def __init__(self, account_id: str, total_assets: float, 
                 available_cash: float, market_value: float):
        self.account_id = account_id
        self.total_assets = total_assets
        self.available_cash = available_cash
        self.market_value = market_value
        self.frozen_cash = 0.0
        self.today_pnl = 0.0
        self.update_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'account_id': self.account_id,
            'total_assets': self.total_assets,
            'available_cash': self.available_cash,
            'market_value': self.market_value,
            'frozen_cash': self.frozen_cash,
            'today_pnl': self.today_pnl,
            'update_time': self.update_time
        }


class BaseBroker(ABC):
    """券商接口基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(f"Broker_{self.__class__.__name__}")
        self.is_connected = False
        self.account_info = None
        self.positions = {}  # symbol -> Position
        self.orders = {}     # order_id -> Order
        
    @abstractmethod
    def connect(self) -> bool:
        """连接到券商"""
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Optional[AccountInfo]:
        """获取账户信息"""
        pass
    
    @abstractmethod
    def get_positions(self) -> Dict[str, Position]:
        """获取持仓信息"""
        pass
    
    @abstractmethod
    def place_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                   quantity: int, price: float = None) -> Optional[str]:
        """下单"""
        pass
    
    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        pass
    
    @abstractmethod
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        pass
    
    @abstractmethod
    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        pass
    
    @abstractmethod
    def get_trades(self, symbol: str = None, start_date: datetime = None,
                  end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        pass
    
    def validate_order(self, symbol: str, side: OrderSide, quantity: int, 
                      price: float = None) -> tuple[bool, str]:
        """验证订单"""
        try:
            # 检查连接状态
            if not self.is_connected:
                return False, "未连接到券商"
            
            # 检查股票代码
            if not symbol or len(symbol) < 6:
                return False, "股票代码格式错误"
            
            # 检查数量
            if quantity <= 0 or quantity % 100 != 0:
                return False, "数量必须是100的整数倍"
            
            # 检查价格
            if price is not None and price <= 0:
                return False, "价格必须大于0"
            
            # 检查资金
            if side == OrderSide.BUY:
                if not self.account_info:
                    return False, "无法获取账户信息"
                
                required_cash = quantity * (price or 0)
                if required_cash > self.account_info.available_cash:
                    return False, "可用资金不足"
            
            # 检查持仓
            elif side == OrderSide.SELL:
                if symbol not in self.positions:
                    return False, "无持仓"
                
                position = self.positions[symbol]
                if quantity > position.quantity:
                    return False, "持仓数量不足"
            
            return True, "验证通过"
            
        except Exception as e:
            self.logger.error(f"订单验证失败: {e}")
            return False, f"验证失败: {str(e)}"
    
    def calculate_commission(self, amount: float) -> float:
        """计算手续费"""
        # 默认手续费率0.03%，最低5元
        commission_rate = self.config.get('commission_rate', 0.0003)
        min_commission = self.config.get('min_commission', 5.0)
        
        commission = amount * commission_rate
        return max(commission, min_commission)
    
    def update_account_info(self):
        """更新账户信息"""
        try:
            self.account_info = self.get_account_info()
            if self.account_info:
                self.logger.debug(f"账户信息更新成功: {self.account_info.to_dict()}")
        except Exception as e:
            self.logger.error(f"更新账户信息失败: {e}")
    
    def update_positions(self):
        """更新持仓信息"""
        try:
            self.positions = self.get_positions()
            self.logger.debug(f"持仓信息更新成功，共{len(self.positions)}只股票")
        except Exception as e:
            self.logger.error(f"更新持仓信息失败: {e}")
    
    def get_broker_name(self) -> str:
        """获取券商名称"""
        return self.__class__.__name__.replace('Broker', '')
    
    def is_market_open(self) -> bool:
        """检查市场是否开放"""
        now = datetime.now()
        weekday = now.weekday()
        
        # 周末不开市
        if weekday >= 5:
            return False
        
        # 检查交易时间
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return (morning_start <= current_time <= morning_end or 
                afternoon_start <= current_time <= afternoon_end)
    
    def __str__(self) -> str:
        return f"{self.get_broker_name()}({'已连接' if self.is_connected else '未连接'})"

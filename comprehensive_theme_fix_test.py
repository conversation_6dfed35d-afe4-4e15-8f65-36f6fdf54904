#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面主题修复测试脚本
验证所有白色文字配白色背景问题是否已修复
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QGroupBox, QTabWidget, QTableWidget, QTableWidgetItem,
    QLineEdit, QComboBox, QCheckBox, QDateEdit, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.styles.dark_theme import DarkTheme


class ComprehensiveThemeTestWindow(QMainWindow):
    """全面主题测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("全面主题修复测试")
        self.setGeometry(100, 100, 1200, 800)
        self.init_ui()
        self.apply_theme()
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("全面主题修复测试 - 检查所有文字是否清晰可见")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本组件测试
        tab_widget.addTab(self.create_basic_components_tab(), "基本组件")
        
        # 表格组件测试
        tab_widget.addTab(self.create_table_components_tab(), "表格组件")
        
        # 输入组件测试
        tab_widget.addTab(self.create_input_components_tab(), "输入组件")
        
        # 分组框测试
        tab_widget.addTab(self.create_groupbox_components_tab(), "分组框测试")
        
        layout.addWidget(tab_widget)
        
        # 状态信息
        status_label = QLabel("如果您能清晰看到所有文字内容，说明主题修复成功！")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("color: #4CAF50; background-color: transparent; font-size: 14px; font-weight: bold;")
        layout.addWidget(status_label)
    
    def create_basic_components_tab(self):
        """创建基本组件测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标签测试
        label_group = QGroupBox("标签测试")
        label_layout = QVBoxLayout(label_group)
        
        labels = [
            ("普通标签", "#ffffff"),
            ("成功状态", "#4CAF50"),
            ("错误状态", "#F44336"),
            ("警告状态", "#FF9800"),
            ("信息状态", "#2196F3")
        ]
        
        for text, color in labels:
            label = QLabel(f"这是{text}文字 - 应该清晰可见")
            label.setStyleSheet(f"color: {color}; background-color: transparent;")
            label_layout.addWidget(label)
        
        layout.addWidget(label_group)
        
        # 按钮测试
        button_group = QGroupBox("按钮测试")
        button_layout = QHBoxLayout(button_group)
        
        buttons = ["普通按钮", "主要按钮", "危险按钮", "成功按钮"]
        for btn_text in buttons:
            btn = QPushButton(btn_text)
            button_layout.addWidget(btn)
        
        layout.addWidget(button_group)
        layout.addStretch()
        
        return widget
    
    def create_table_components_tab(self):
        """创建表格组件测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 表格测试
        table_group = QGroupBox("表格测试")
        table_layout = QVBoxLayout(table_group)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["股票代码", "股票名称", "当前价格", "涨跌幅"])
        
        # 添加测试数据
        test_data = [
            ("000001.SZ", "平安银行", "12.50", "+2.04%"),
            ("600036.SH", "招商银行", "35.20", "-1.22%"),
            ("300015.SZ", "爱尔眼科", "28.80", "+3.45%"),
        ]
        
        table.setRowCount(len(test_data))
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                # 为涨跌幅列设置颜色
                if col == 3:
                    if value.startswith('+'):
                        item.setForeground(Qt.red)
                    elif value.startswith('-'):
                        item.setForeground(Qt.green)
                table.setItem(row, col, item)
        
        table_layout.addWidget(table)
        layout.addWidget(table_group)
        
        return widget
    
    def create_input_components_tab(self):
        """创建输入组件测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入框测试
        input_group = QGroupBox("输入组件测试")
        input_layout = QVBoxLayout(input_group)
        
        # 文本输入框
        text_layout = QHBoxLayout()
        text_label = QLabel("文本输入:")
        text_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        text_input = QLineEdit()
        text_input.setPlaceholderText("请输入文本")
        text_layout.addWidget(text_label)
        text_layout.addWidget(text_input)
        input_layout.addLayout(text_layout)
        
        # 下拉框
        combo_layout = QHBoxLayout()
        combo_label = QLabel("下拉选择:")
        combo_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        combo_box = QComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3"])
        combo_layout.addWidget(combo_label)
        combo_layout.addWidget(combo_box)
        input_layout.addLayout(combo_layout)
        
        # 复选框
        checkbox_layout = QHBoxLayout()
        checkbox1 = QCheckBox("选项A")
        checkbox2 = QCheckBox("选项B")
        checkbox3 = QCheckBox("选项C")
        checkbox_layout.addWidget(checkbox1)
        checkbox_layout.addWidget(checkbox2)
        checkbox_layout.addWidget(checkbox3)
        input_layout.addLayout(checkbox_layout)
        
        # 数字输入框
        number_layout = QHBoxLayout()
        number_label = QLabel("数字输入:")
        number_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        spin_box = QSpinBox()
        spin_box.setRange(0, 1000)
        double_spin_box = QDoubleSpinBox()
        double_spin_box.setRange(0.0, 100.0)
        double_spin_box.setDecimals(2)
        number_layout.addWidget(number_label)
        number_layout.addWidget(spin_box)
        number_layout.addWidget(double_spin_box)
        input_layout.addLayout(number_layout)
        
        # 日期选择
        date_layout = QHBoxLayout()
        date_label = QLabel("日期选择:")
        date_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setCalendarPopup(True)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_edit)
        input_layout.addLayout(date_layout)
        
        layout.addWidget(input_group)
        layout.addStretch()
        
        return widget
    
    def create_groupbox_components_tab(self):
        """创建分组框测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 多个分组框测试
        groups = [
            ("系统状态", ["运行状态: 正常", "数据状态: 连接", "内存使用: 45%"]),
            ("交易信息", ["账户余额: ¥100,000", "持仓数量: 5只", "今日盈亏: +¥2,000"]),
            ("市场数据", ["上证指数: 3000.00", "深证成指: 11000.00", "创业板指: 2200.00"])
        ]
        
        for group_title, items in groups:
            group = QGroupBox(group_title)
            group_layout = QVBoxLayout(group)
            
            for item_text in items:
                label = QLabel(item_text)
                label.setStyleSheet("color: #ffffff; background-color: transparent;")
                group_layout.addWidget(label)
            
            layout.addWidget(group)
        
        layout.addStretch()
        return widget
    
    def apply_theme(self):
        """应用深色主题"""
        self.setStyleSheet(DarkTheme.get_stylesheet())


def main():
    """主函数"""
    print("=" * 60)
    print("全面主题修复测试")
    print("=" * 60)
    
    print("修复内容:")
    print("1. ✓ 深色主题样式表增强")
    print("2. ✓ 所有标签文字颜色修复")
    print("3. ✓ 设置界面标签修复")
    print("4. ✓ 交易中心界面标签修复")
    print("5. ✓ 表格组件样式修复")
    print("6. ✓ 输入组件样式修复")
    print("7. ✓ 复选框和日期选择器修复")
    print("8. ✓ 分组框内标签修复")
    
    print("\n启动测试窗口...")
    
    try:
        app = QApplication(sys.argv)
        
        window = ComprehensiveThemeTestWindow()
        window.show()
        
        print("✅ 测试窗口已显示")
        print("\n请检查以下内容:")
        print("- 所有标签页中的文字是否清晰可见")
        print("- 表格内容是否正确显示")
        print("- 输入框、下拉框等组件是否正常")
        print("- 复选框和日期选择器是否可用")
        print("- 分组框内的文字是否清晰")
        print("- 没有白色文字配白色背景的问题")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()

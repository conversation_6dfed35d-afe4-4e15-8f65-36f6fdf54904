#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略模块测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from strategies.technical.ma_strategy import MovingAverageStrategy, DoubleMovingAverageStrategy
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def create_sample_data():
    """创建示例数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # 生成带趋势的价格数据
    trend = np.linspace(0, 0.3, len(dates))  # 30%的年度趋势
    noise = np.random.normal(0, 0.02, len(dates))  # 2%的日波动
    returns = trend / len(dates) + noise
    
    prices = [100]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates)),
        'symbol': ['TEST'] * len(dates)
    }, index=dates)
    
    # 确保OHLC逻辑正确
    data['high'] = data[['open', 'high', 'close']].max(axis=1)
    data['low'] = data[['open', 'low', 'close']].min(axis=1)
    
    return data


def test_moving_average_strategy():
    """测试移动平均线策略"""
    print("=" * 50)
    print("测试移动平均线策略")
    print("=" * 50)
    
    # 创建策略实例
    strategy = MovingAverageStrategy(
        name="测试MA策略",
        parameters={
            'fast_period': 5,
            'slow_period': 20,
            'ma_type': 'sma',
            'position_size': 0.1
        }
    )
    
    print(f"1. 策略初始化: {strategy.name}")
    print(f"   参数: {strategy.parameters}")
    
    # 创建测试数据
    data = create_sample_data()
    print(f"2. 测试数据: {len(data)} 条记录")
    
    # 启动策略
    strategy.start()
    print(f"3. 策略状态: {strategy.status}")
    
    # 生成信号
    print("4. 生成交易信号...")
    signals = strategy.generate_signals(data)
    
    print(f"✓ 生成信号成功，共 {len(signals)} 个信号")
    
    if signals:
        print("\n前5个信号:")
        for i, signal in enumerate(signals[:5]):
            print(f"  {i+1}. {signal['timestamp'].strftime('%Y-%m-%d')} "
                  f"{signal['direction']} {signal['symbol']} @ {signal['price']:.2f} "
                  f"({signal['signal_type']}, 置信度: {signal['confidence']:.2f})")
    
    # 获取策略信息
    print("\n5. 策略信息:")
    info = strategy.get_strategy_info()
    print(f"   类型: {info['type']}")
    print(f"   描述: {info['description']}")
    print(f"   信号数量: {info['signal_count']}")
    
    # 获取绩效指标
    print("\n6. 绩效指标:")
    metrics = strategy.get_performance_metrics()
    for key, value in metrics.items():
        if key != 'parameters':
            print(f"   {key}: {value}")
    
    return True


def test_double_moving_average_strategy():
    """测试双移动平均线策略"""
    print("\n" + "=" * 50)
    print("测试双移动平均线策略")
    print("=" * 50)
    
    # 创建增强策略实例
    strategy = DoubleMovingAverageStrategy(
        name="测试双MA策略",
        parameters={
            'fast_period': 10,
            'slow_period': 30,
            'signal_period': 9,
            'volume_factor': 1.5,
            'trend_filter': True,
            'position_size': 0.08
        }
    )
    
    print(f"1. 策略初始化: {strategy.name}")
    
    # 创建测试数据
    data = create_sample_data()
    
    # 启动策略并生成信号
    strategy.start()
    signals = strategy.generate_signals(data)
    
    print(f"2. 生成信号: {len(signals)} 个")
    
    if signals:
        print("\n信号详情:")
        for i, signal in enumerate(signals):
            print(f"  {i+1}. {signal['timestamp'].strftime('%Y-%m-%d')} "
                  f"{signal['direction']} @ {signal['price']:.2f} "
                  f"(置信度: {signal['confidence']:.2f})")
    
    # 测试策略状态管理
    print("\n3. 测试策略状态管理:")
    print(f"   当前状态: {strategy.status}")
    
    strategy.pause()
    print(f"   暂停后: {strategy.status}")
    
    strategy.start()
    print(f"   重启后: {strategy.status}")
    
    strategy.stop()
    print(f"   停止后: {strategy.status}")
    
    return True


def test_real_data_strategy():
    """测试真实数据策略"""
    print("\n" + "=" * 50)
    print("测试真实数据策略")
    print("=" * 50)
    
    try:
        # 获取真实数据
        collector = AKShareCollector()
        if not collector.connect():
            print("✗ 无法连接数据源，跳过真实数据测试")
            return True
        
        print("1. 获取真实股票数据...")
        
        # 获取平安银行数据
        stock_data = collector.get_stock_data(
            "000001", 
            start_date="2023-01-01", 
            end_date="2023-12-31"
        )
        
        if stock_data.empty:
            print("✗ 无法获取股票数据")
            return False
        
        # 添加股票代码列
        stock_data['symbol'] = '000001'
        
        print(f"✓ 获取数据成功: {len(stock_data)} 条记录")
        
        print("\n2. 应用移动平均线策略...")
        
        # 创建策略
        ma_strategy = MovingAverageStrategy(
            name="平安银行MA策略",
            parameters={
                'fast_period': 5,
                'slow_period': 20,
                'ma_type': 'ema',
                'position_size': 0.1,
                'min_volume': 50000000  # 5000万成交量过滤
            }
        )
        
        # 生成信号
        ma_strategy.start()
        ma_signals = ma_strategy.generate_signals(stock_data)
        
        print(f"✓ MA策略信号: {len(ma_signals)} 个")
        
        print("\n3. 应用双移动平均线策略...")
        
        # 创建增强策略
        dma_strategy = DoubleMovingAverageStrategy(
            name="平安银行双MA策略",
            parameters={
                'fast_period': 10,
                'slow_period': 30,
                'signal_period': 9,
                'volume_factor': 1.2,
                'position_size': 0.08
            }
        )
        
        # 生成信号
        dma_strategy.start()
        dma_signals = dma_strategy.generate_signals(stock_data)
        
        print(f"✓ 双MA策略信号: {len(dma_signals)} 个")
        
        # 比较两个策略
        print("\n4. 策略比较:")
        print(f"   基础MA策略信号数: {len(ma_signals)}")
        print(f"   增强双MA策略信号数: {len(dma_signals)}")
        
        if ma_signals:
            ma_buy_signals = [s for s in ma_signals if s['direction'] == 'buy']
            ma_sell_signals = [s for s in ma_signals if s['direction'] == 'sell']
            print(f"   MA策略 - 买入: {len(ma_buy_signals)}, 卖出: {len(ma_sell_signals)}")
        
        if dma_signals:
            dma_buy_signals = [s for s in dma_signals if s['direction'] == 'buy']
            dma_sell_signals = [s for s in dma_signals if s['direction'] == 'sell']
            print(f"   双MA策略 - 买入: {len(dma_buy_signals)}, 卖出: {len(dma_sell_signals)}")
        
        # 显示最近的信号
        if ma_signals:
            print(f"\n   MA策略最新信号:")
            latest_ma = ma_signals[-1]
            print(f"     {latest_ma['timestamp'].strftime('%Y-%m-%d')} "
                  f"{latest_ma['direction']} @ {latest_ma['price']:.2f}")
        
        if dma_signals:
            print(f"\n   双MA策略最新信号:")
            latest_dma = dma_signals[-1]
            print(f"     {latest_dma['timestamp'].strftime('%Y-%m-%d')} "
                  f"{latest_dma['direction']} @ {latest_dma['price']:.2f}")
        
        collector.disconnect()
        return True
        
    except Exception as e:
        print(f"✗ 真实数据策略测试失败: {e}")
        return False


def test_strategy_serialization():
    """测试策略序列化"""
    print("\n" + "=" * 50)
    print("测试策略序列化")
    print("=" * 50)
    
    # 创建策略
    strategy = MovingAverageStrategy(
        name="序列化测试策略",
        parameters={'fast_period': 5, 'slow_period': 20}
    )
    
    print("1. 测试字典转换...")
    strategy_dict = strategy.to_dict()
    print(f"✓ 转换为字典成功: {len(strategy_dict)} 个字段")
    
    print("2. 测试JSON转换...")
    strategy_json = strategy.to_json()
    print(f"✓ 转换为JSON成功: {len(strategy_json)} 字符")
    
    print("3. 测试从字典恢复...")
    restored_strategy = MovingAverageStrategy.from_dict(strategy_dict)
    print(f"✓ 从字典恢复成功: {restored_strategy.name}")
    
    return True


def main():
    """主函数"""
    print("量化交易系统 - 策略模块测试")
    print("开始时间:", datetime.now())
    
    # 设置日志
    logger = setup_logger()
    
    # 测试各个模块
    ma_success = test_moving_average_strategy()
    dma_success = test_double_moving_average_strategy()
    real_success = test_real_data_strategy()
    serial_success = test_strategy_serialization()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"移动平均线策略: {'✓ 通过' if ma_success else '✗ 失败'}")
    print(f"双移动平均线策略: {'✓ 通过' if dma_success else '✗ 失败'}")
    print(f"真实数据测试: {'✓ 通过' if real_success else '✗ 失败'}")
    print(f"策略序列化: {'✓ 通过' if serial_success else '✗ 失败'}")
    
    if all([ma_success, dma_success, real_success, serial_success]):
        print("\n🎉 所有测试通过！策略模块工作正常。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()

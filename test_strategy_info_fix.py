#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试策略信息获取修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from strategies.strategy_factory import StrategyFactory
from utils.logger import get_logger

def test_strategy_info_fix():
    """测试策略信息获取修复"""
    logger = get_logger("TestStrategyInfoFix")
    
    print("=" * 60)
    print("测试策略信息获取修复")
    print("=" * 60)
    
    try:
        # 创建策略工厂
        factory = StrategyFactory()
        
        # 获取所有可用策略
        strategies_info = factory.get_available_strategies()
        
        print(f"\n✓ 成功获取策略信息，共{len(strategies_info)}个策略")
        
        # 检查每个策略的信息
        for strategy_type, info in strategies_info.items():
            print(f"\n策略类型: {strategy_type}")
            print(f"  名称: {info.get('name', 'N/A')}")
            print(f"  描述: {info.get('description', 'N/A')}")
            print(f"  策略类型: {info.get('strategy_type', 'N/A')}")
            print(f"  指标: {info.get('indicators', [])}")
            
            # 检查是否有错误
            if 'error' in info:
                print(f"  ❌ 错误: {info['error']}")
            else:
                print(f"  ✓ 信息获取成功")
                
                # 检查信号条件
                signal_conditions = info.get('signal_conditions', {})
                if signal_conditions:
                    print(f"  买入条件: {signal_conditions.get('buy', 'N/A')}")
                    print(f"  卖出条件: {signal_conditions.get('sell', 'N/A')}")
                
                # 检查风险管理
                risk_management = info.get('risk_management', {})
                if risk_management:
                    print(f"  风险管理: {risk_management}")
        
        # 特别测试高级策略
        print(f"\n{'='*40}")
        print("测试高级策略创建和信息获取")
        print(f"{'='*40}")
        
        advanced_strategies = [
            ("KDJ", "KDJ策略"),
            ("AdvancedBollinger", "高级布林带策略"),
            ("MeanReversion", "均值回归策略"),
            ("Breakout", "突破策略"),
            ("TrendFollowing", "趋势跟踪策略")
        ]
        
        for strategy_type, strategy_name in advanced_strategies:
            try:
                # 创建策略实例
                strategy = factory.create_strategy(strategy_name, strategy_type, {})
                
                if strategy:
                    print(f"\n✓ {strategy_name} 创建成功")
                    
                    # 获取策略信息
                    if hasattr(strategy, 'get_strategy_info'):
                        info = strategy.get_strategy_info()
                        print(f"  策略类型: {info.get('strategy_type', 'N/A')}")
                        print(f"  指标: {info.get('indicators', [])}")
                        print(f"  信号条件: {info.get('signal_conditions', {})}")
                        print(f"  ✓ get_strategy_info方法正常工作")
                    else:
                        print(f"  ❌ 缺少get_strategy_info方法")
                else:
                    print(f"❌ {strategy_name} 创建失败")
                    
            except Exception as e:
                print(f"❌ {strategy_name} 测试失败: {e}")
        
        print(f"\n{'='*60}")
        print("策略信息获取修复测试完成")
        print(f"{'='*60}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_strategy_info_fix()
    if success:
        print("\n✅ 第一阶段修复测试通过")
    else:
        print("\n❌ 第一阶段修复测试失败")
    
    input("\n按回车键退出...")

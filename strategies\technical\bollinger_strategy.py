#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布林带策略
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from analysis.technical_indicators import TechnicalIndicators


class BollingerBandsStrategy(BaseStrategy):
    """布林带策略"""

    def __init__(self, name: str = "布林带策略", config: Dict[str, Any] = None):
        default_params = {
            'period': 20,           # 布林带周期
            'std_dev': 2.0,         # 标准差倍数
            'squeeze_threshold': 0.1, # 收缩阈值
            'breakout_threshold': 0.02, # 突破阈值
            'min_confidence': 0.6,  # 最小置信度
            'stop_loss': 0.05,      # 止损比例
            'take_profit': 0.15,    # 止盈比例
            'volume_filter': True,  # 成交量过滤
            'trend_filter': True,   # 趋势过滤
            'mean_reversion': True, # 均值回归模式
            'breakout_mode': False  # 突破模式
        }

        if config:
            default_params.update(config)

        super().__init__(name, default_params)
        self.tech_indicators = TechnicalIndicators()
        self.signal_count = 0

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成布林带交易信号"""
        signals = []

        if data is None or data.empty or len(data) < self.parameters['period'] + 10:
            self.logger.warning("数据不足，无法生成布林带信号")
            return signals

        try:
            # 计算布林带
            bb_upper, bb_middle, bb_lower = self.tech_indicators.bollinger_bands(
                data['close'],
                self.parameters['period'],
                self.parameters['std_dev']
            )

            # 计算布林带宽度和位置
            bb_width = (bb_upper - bb_lower) / bb_middle
            bb_position = (data['close'] - bb_lower) / (bb_upper - bb_lower)

            # 计算辅助指标
            rsi = self.tech_indicators.rsi(data['close'], 14)
            volume_ma = data['volume'].rolling(window=20).mean()

            # 遍历数据生成信号
            for i in range(2, len(data)):
                current_date = data.index[i]
                current_price = data['close'].iloc[i]

                # 跳过NaN值
                if (pd.isna(bb_upper.iloc[i]) or pd.isna(bb_lower.iloc[i]) or
                    pd.isna(bb_position.iloc[i]) or pd.isna(bb_width.iloc[i])):
                    continue

                signal = None

                # 均值回归模式
                if self.parameters['mean_reversion']:
                    signal = self._check_mean_reversion_signals(
                        data, i, bb_upper, bb_middle, bb_lower, bb_position, rsi, volume_ma
                    )

                # 突破模式
                elif self.parameters['breakout_mode']:
                    signal = self._check_breakout_signals(
                        data, i, bb_upper, bb_lower, bb_width, volume_ma
                    )

                if signal:
                    # 验证信号
                    if self.validate_signal(signal):
                        # 计算仓位大小
                        signal['quantity'] = self.calculate_position_size(
                            signal, current_price, 1000000
                        )

                        # 应用风险管理
                        signal = self.apply_risk_management(signal)

                        signals.append(signal)
                        self.save_signal(signal)
                        self.signal_count += 1

                        self.logger.info(f"生成{signal['signal_type']}信号: {signal['direction']} {signal['symbol']} @ {signal['price']:.2f}")

            return signals

        except Exception as e:
            self.logger.error(f"生成布林带信号失败: {e}")
            return signals

    def _check_mean_reversion_signals(self, data: pd.DataFrame, index: int,
                                     bb_upper: pd.Series, bb_middle: pd.Series, bb_lower: pd.Series,
                                     bb_position: pd.Series, rsi: pd.Series, volume_ma: pd.Series) -> Optional[Dict[str, Any]]:
        """检查均值回归信号"""
        try:
            current_price = data['close'].iloc[index]
            current_date = data.index[index]

            # 下轨反弹买入信号
            if (current_price <= bb_lower.iloc[index] * 1.01 and  # 接近或触及下轨
                bb_position.iloc[index] <= 0.1):  # 布林带位置很低

                # 检查反弹迹象
                if (index >= 1 and
                    data['close'].iloc[index] > data['close'].iloc[index-1]):  # 价格开始反弹

                    if self._check_buy_conditions(data, index, rsi, volume_ma):
                        confidence = self._calculate_mean_reversion_buy_confidence(
                            data, index, bb_position, rsi
                        )

                        if confidence >= self.parameters['min_confidence']:
                            return {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'BOLLINGER_MEAN_REVERSION',
                                'direction': 'buy',
                                'price': current_price,
                                'quantity': 0,
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'布林带下轨反弹，位置:{bb_position.iloc[index]:.2f}',
                                'stop_loss_price': bb_lower.iloc[index] * 0.98,
                                'take_profit_price': bb_middle.iloc[index]
                            }

            # 上轨回调卖出信号
            elif (current_price >= bb_upper.iloc[index] * 0.99 and  # 接近或触及上轨
                  bb_position.iloc[index] >= 0.9):  # 布林带位置很高

                if self._has_position(data.attrs.get('symbol', 'UNKNOWN')):
                    # 检查回调迹象
                    if (index >= 1 and
                        data['close'].iloc[index] < data['close'].iloc[index-1]):  # 价格开始回调

                        confidence = self._calculate_mean_reversion_sell_confidence(
                            data, index, bb_position, rsi
                        )

                        if confidence >= self.parameters['min_confidence']:
                            return {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'BOLLINGER_MEAN_REVERSION',
                                'direction': 'sell',
                                'price': current_price,
                                'quantity': 0,
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'布林带上轨回调，位置:{bb_position.iloc[index]:.2f}',
                                'stop_loss_price': 0,
                                'take_profit_price': 0
                            }

            return None

        except Exception:
            return None

    def _check_breakout_signals(self, data: pd.DataFrame, index: int,
                               bb_upper: pd.Series, bb_lower: pd.Series,
                               bb_width: pd.Series, volume_ma: pd.Series) -> Optional[Dict[str, Any]]:
        """检查突破信号"""
        try:
            current_price = data['close'].iloc[index]
            current_date = data.index[index]

            # 检查布林带收缩
            if bb_width.iloc[index] < self.parameters['squeeze_threshold']:

                # 向上突破
                if (current_price > bb_upper.iloc[index] and
                    data['close'].iloc[index-1] <= bb_upper.iloc[index-1]):

                    # 成交量确认
                    if (not pd.isna(volume_ma.iloc[index]) and
                        data['volume'].iloc[index] > volume_ma.iloc[index] * 1.5):

                        confidence = self._calculate_breakout_confidence(data, index, 'up')

                        if confidence >= self.parameters['min_confidence']:
                            return {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'BOLLINGER_BREAKOUT',
                                'direction': 'buy',
                                'price': current_price,
                                'quantity': 0,
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'布林带向上突破，宽度:{bb_width.iloc[index]:.3f}',
                                'stop_loss_price': bb_upper.iloc[index] * 0.98,
                                'take_profit_price': current_price * (1 + self.parameters['take_profit'])
                            }

                # 向下突破
                elif (current_price < bb_lower.iloc[index] and
                      data['close'].iloc[index-1] >= bb_lower.iloc[index-1]):

                    if self._has_position(data.attrs.get('symbol', 'UNKNOWN')):
                        # 成交量确认
                        if (not pd.isna(volume_ma.iloc[index]) and
                            data['volume'].iloc[index] > volume_ma.iloc[index] * 1.5):

                            confidence = self._calculate_breakout_confidence(data, index, 'down')

                            if confidence >= self.parameters['min_confidence']:
                                return {
                                    'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                    'signal_type': 'BOLLINGER_BREAKOUT',
                                    'direction': 'sell',
                                    'price': current_price,
                                    'quantity': 0,
                                    'timestamp': current_date,
                                    'confidence': confidence,
                                    'reason': f'布林带向下突破，宽度:{bb_width.iloc[index]:.3f}',
                                    'stop_loss_price': 0,
                                    'take_profit_price': 0
                                }

            return None

        except Exception:
            return None

    def _check_buy_conditions(self, data: pd.DataFrame, index: int,
                             rsi: pd.Series, volume_ma: pd.Series) -> bool:
        """检查买入附加条件"""
        try:
            # RSI不能过高
            if not pd.isna(rsi.iloc[index]) and rsi.iloc[index] > 70:
                return False

            # 成交量过滤
            if self.parameters['volume_filter']:
                if (not pd.isna(volume_ma.iloc[index]) and
                    data['volume'].iloc[index] < volume_ma.iloc[index] * 0.8):
                    return False

            return True

        except Exception:
            return False

    def _calculate_mean_reversion_buy_confidence(self, data: pd.DataFrame, index: int,
                                               bb_position: pd.Series, rsi: pd.Series) -> float:
        """计算均值回归买入置信度"""
        try:
            confidence = 0.5

            # 布林带位置越低，置信度越高
            position = bb_position.iloc[index]
            if position <= 0.05:
                confidence += 0.3
            elif position <= 0.1:
                confidence += 0.2
            elif position <= 0.2:
                confidence += 0.1

            # RSI确认
            if not pd.isna(rsi.iloc[index]):
                if rsi.iloc[index] <= 30:
                    confidence += 0.15
                elif rsi.iloc[index] <= 40:
                    confidence += 0.1

            # 价格反弹强度
            if index >= 1:
                price_change = (data['close'].iloc[index] - data['close'].iloc[index-1]) / data['close'].iloc[index-1]
                if price_change > 0:
                    confidence += min(0.1, price_change * 10)

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _calculate_mean_reversion_sell_confidence(self, data: pd.DataFrame, index: int,
                                                bb_position: pd.Series, rsi: pd.Series) -> float:
        """计算均值回归卖出置信度"""
        try:
            confidence = 0.5

            # 布林带位置越高，置信度越高
            position = bb_position.iloc[index]
            if position >= 0.95:
                confidence += 0.3
            elif position >= 0.9:
                confidence += 0.2
            elif position >= 0.8:
                confidence += 0.1

            # RSI确认
            if not pd.isna(rsi.iloc[index]):
                if rsi.iloc[index] >= 70:
                    confidence += 0.15
                elif rsi.iloc[index] >= 60:
                    confidence += 0.1

            # 价格回调强度
            if index >= 1:
                price_change = (data['close'].iloc[index-1] - data['close'].iloc[index]) / data['close'].iloc[index]
                if price_change > 0:
                    confidence += min(0.1, price_change * 10)

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _calculate_breakout_confidence(self, data: pd.DataFrame, index: int, direction: str) -> float:
        """计算突破信号置信度"""
        try:
            confidence = 0.6  # 突破信号基础置信度较高

            # 突破强度
            if index >= 1:
                price_change = abs(data['close'].iloc[index] - data['close'].iloc[index-1]) / data['close'].iloc[index-1]
                confidence += min(0.2, price_change * 10)

            # 成交量确认
            if len(data) >= index + 5:
                avg_volume = data['volume'].iloc[index-5:index].mean()
                volume_ratio = data['volume'].iloc[index] / avg_volume
                if volume_ratio > 2:
                    confidence += 0.15
                elif volume_ratio > 1.5:
                    confidence += 0.1

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.6

    def _has_position(self, symbol: str) -> bool:
        """检查是否有持仓"""
        return symbol in self.positions and self.positions[symbol]['quantity'] > 0

    def calculate_position_size(self, signal: Dict[str, Any], current_price: float,
                               available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于风险的仓位计算
            risk_per_trade = available_capital * 0.02  # 每笔交易风险2%
            stop_loss_price = signal.get('stop_loss_price', current_price * 0.95)
            risk_per_share = abs(current_price - stop_loss_price)

            if risk_per_share > 0:
                position_size = int(risk_per_trade / risk_per_share)
                # 确保是100的整数倍
                position_size = (position_size // 100) * 100
                return max(100, min(position_size, int(available_capital * 0.1 / current_price / 100) * 100))

            return 100

        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        mode = "均值回归" if self.parameters['mean_reversion'] else "突破"
        info.update({
            'strategy_type': f'布林带{mode}策略',
            'indicators': ['Bollinger Bands', 'RSI', 'Volume'],
            'signal_conditions': {
                'buy': '触及下轨反弹 或 向上突破',
                'sell': '触及上轨回调 或 向下突破'
            },
            'risk_management': {
                'stop_loss': f"{self.parameters['stop_loss']*100:.1f}%",
                'take_profit': f"{self.parameters['take_profit']*100:.1f}%"
            }
        })
        return info

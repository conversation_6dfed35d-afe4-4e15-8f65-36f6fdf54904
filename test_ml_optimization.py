#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试机器学习模块优化
"""

import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ml.model_manager import ModelManager
from strategies.strategy_factory import StrategyFactory


def test_model_manager_optimization():
    """测试模型管理器优化"""
    print("\n" + "="*50)
    print("测试模型管理器优化")
    print("="*50)
    
    try:
        # 创建模型管理器
        model_manager = ModelManager()
        print("✓ 模型管理器创建成功")
        
        # 测试创建模型
        model_name = "test_optimized_model"
        if model_manager.create_model(model_name, 'random_forest_classifier'):
            print("✓ 模型创建成功")
            
            # 测试获取优化后的模型信息
            model_info = model_manager.get_model_info(model_name)
            print(f"✓ 模型信息获取成功:")
            print(f"  - 模型名称: {model_info.get('model_name')}")
            print(f"  - 模型类型: {model_info.get('model_type')}")
            print(f"  - 模型类: {model_info.get('model_class')}")
            print(f"  - 是否存在: {model_info.get('exists')}")
            print(f"  - 是否已训练: {model_info.get('is_fitted')}")
            print(f"  - 支持概率预测: {model_info.get('supports_probability')}")
            print(f"  - 支持特征重要性: {model_info.get('has_feature_importance')}")
            
            # 创建测试数据
            np.random.seed(42)
            n_samples = 1000
            n_features = 10
            
            X = pd.DataFrame(
                np.random.randn(n_samples, n_features),
                columns=[f'feature_{i}' for i in range(n_features)]
            )
            y = pd.Series(np.random.choice([0, 1], n_samples))
            
            print("✓ 测试数据创建成功")
            
            # 测试模型训练
            print("开始训练模型...")
            metrics = model_manager.train_model(model_name, X, y)
            if metrics:
                print("✓ 模型训练成功")
                print(f"  - 训练准确率: {metrics.get('train_accuracy', 'N/A'):.3f}")
                print(f"  - 测试准确率: {metrics.get('test_accuracy', 'N/A'):.3f}")
                
                # 测试优化后的保存功能
                if model_manager.save_model(model_name, include_metadata=True):
                    print("✓ 模型保存成功（包含元数据）")
                    
                    # 测试优化后的加载功能
                    new_manager = ModelManager()
                    if new_manager.load_model(model_name):
                        print("✓ 模型加载成功")
                        
                        # 验证加载后的模型信息
                        loaded_info = new_manager.get_model_info(model_name)
                        print(f"✓ 加载后模型信息验证:")
                        print(f"  - 特征数量: {loaded_info.get('feature_count')}")
                        print(f"  - 模型版本: 加载成功")
                    else:
                        print("❌ 模型加载失败")
                else:
                    print("❌ 模型保存失败")
            else:
                print("❌ 模型训练失败")
        else:
            print("❌ 模型创建失败")
        
        print("✅ 模型管理器优化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 模型管理器优化测试失败: {e}")
        return False


def test_strategy_factory_optimization():
    """测试策略工厂优化"""
    print("\n" + "="*50)
    print("测试策略工厂优化")
    print("="*50)
    
    try:
        # 创建策略工厂
        factory = StrategyFactory()
        print("✓ 策略工厂创建成功")
        
        # 测试优化后的策略信息获取
        strategies = factory.get_available_strategies()
        print(f"✓ 获取可用策略: {len(strategies)} 种")
        
        for strategy_type, info in strategies.items():
            print(f"\n📊 {strategy_type} 策略:")
            print(f"  - 名称: {info.get('name', 'N/A')}")
            print(f"  - 描述: {info.get('description', 'N/A')}")
            print(f"  - 类型: {info.get('strategy_type', 'N/A')}")
            print(f"  - 指标: {info.get('indicators', [])}")
            print(f"  - 参数数量: {len(info.get('parameters', {}))}")
            
            if 'error' in info:
                print(f"  ⚠️ 错误: {info['error']}")
            else:
                print(f"  ✓ 信息获取成功")
        
        # 测试创建策略
        test_strategies = ['MA', 'MACD', 'RSI', 'SimpleML']
        created_count = 0
        
        for strategy_type in test_strategies:
            try:
                strategy = factory.create_strategy(strategy_type, f"test_{strategy_type}")
                if strategy:
                    print(f"✓ {strategy_type} 策略创建成功")
                    created_count += 1
                else:
                    print(f"❌ {strategy_type} 策略创建失败")
            except Exception as e:
                print(f"❌ {strategy_type} 策略创建异常: {e}")
        
        print(f"✓ 成功创建 {created_count}/{len(test_strategies)} 个策略")
        
        print("✅ 策略工厂优化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 策略工厂优化测试失败: {e}")
        return False


def test_ml_strategy_integration():
    """测试ML策略集成"""
    print("\n" + "="*50)
    print("测试ML策略集成")
    print("="*50)
    
    try:
        from strategies.ml.simple_ml_strategy import SimpleMLStrategy
        
        # 创建ML策略
        strategy = SimpleMLStrategy(
            "测试ML策略优化",
            {
                'model_type': 'random_forest_classifier',
                'prediction_threshold': 0.6,
                'feature_selection': True,
                'top_features': 10
            }
        )
        print("✓ ML策略创建成功")
        
        # 测试获取策略信息
        strategy_info = strategy.get_strategy_info()
        print("✓ 策略信息获取成功:")
        print(f"  - 策略类型: {strategy_info.get('strategy_type')}")
        print(f"  - 指标: {strategy_info.get('indicators')}")
        print(f"  - ML配置: {strategy_info.get('ml_config')}")
        
        # 测试模型性能获取
        performance = strategy.get_model_performance()
        print("✓ 模型性能信息获取成功:")
        print(f"  - 模型名称: {performance.get('model_name')}")
        print(f"  - 模型类型: {performance.get('model_type')}")
        print(f"  - 特征数量: {performance.get('feature_count')}")
        print(f"  - 信号数量: {performance.get('signal_count')}")
        
        print("✅ ML策略集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ ML策略集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试机器学习模块优化")
    
    test_results = []
    
    # 测试模型管理器优化
    test_results.append(test_model_manager_optimization())
    
    # 测试策略工厂优化
    test_results.append(test_strategy_factory_optimization())
    
    # 测试ML策略集成
    test_results.append(test_ml_strategy_integration())
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！机器学习模块优化成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()

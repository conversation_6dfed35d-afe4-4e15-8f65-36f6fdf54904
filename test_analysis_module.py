#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析模块测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis.technical_indicators import TechnicalIndicators
from analysis.risk_metrics import RiskMetrics
from analysis.performance import PerformanceAnalyzer
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def create_sample_data():
    """创建示例数据"""
    # 生成模拟股价数据
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # 模拟价格走势
    returns = np.random.normal(0.001, 0.02, len(dates))  # 日收益率
    prices = [100]  # 初始价格
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates))
    }, index=dates)
    
    # 确保高开低收的逻辑关系
    data['high'] = data[['open', 'high', 'close']].max(axis=1)
    data['low'] = data[['open', 'low', 'close']].min(axis=1)
    
    return data


def test_technical_indicators():
    """测试技术指标模块"""
    print("=" * 50)
    print("测试技术指标模块")
    print("=" * 50)
    
    # 创建示例数据
    data = create_sample_data()
    
    # 创建技术指标计算器
    tech_indicators = TechnicalIndicators()
    
    print("1. 测试单个指标计算...")
    
    # 测试移动平均线
    sma_20 = tech_indicators.sma(data['close'], 20)
    ema_20 = tech_indicators.ema(data['close'], 20)
    print(f"✓ SMA(20) 计算成功，最新值: {sma_20.iloc[-1]:.2f}")
    print(f"✓ EMA(20) 计算成功，最新值: {ema_20.iloc[-1]:.2f}")
    
    # 测试RSI
    rsi = tech_indicators.rsi(data['close'])
    print(f"✓ RSI 计算成功，最新值: {rsi.iloc[-1]:.2f}")
    
    # 测试MACD
    macd, signal, histogram = tech_indicators.macd(data['close'])
    print(f"✓ MACD 计算成功，最新值: {macd.iloc[-1]:.4f}")
    
    # 测试布林带
    bb_upper, bb_middle, bb_lower = tech_indicators.bollinger_bands(data['close'])
    print(f"✓ 布林带计算成功，上轨: {bb_upper.iloc[-1]:.2f}, 下轨: {bb_lower.iloc[-1]:.2f}")
    
    print("\n2. 测试批量指标计算...")
    
    # 计算所有指标
    result = tech_indicators.calculate_all_indicators(data)
    print(f"✓ 批量计算成功，共计算 {len(result.columns) - len(data.columns)} 个技术指标")
    
    # 显示部分结果
    print("\n技术指标示例（最新5个交易日）:")
    indicators_to_show = ['close', 'sma_20', 'ema_20', 'rsi', 'macd']
    print(result[indicators_to_show].tail().round(2))
    
    return True


def test_risk_metrics():
    """测试风险指标模块"""
    print("\n" + "=" * 50)
    print("测试风险指标模块")
    print("=" * 50)
    
    # 创建示例数据
    data = create_sample_data()
    prices = data['close']
    
    # 创建风险指标计算器
    risk_metrics = RiskMetrics()
    
    print("1. 测试单个风险指标...")
    
    # 测试收益率
    returns = risk_metrics.returns(prices)
    print(f"✓ 收益率计算成功，平均日收益率: {returns.mean() * 100:.3f}%")
    
    # 测试波动率
    volatility = risk_metrics.volatility(returns)
    print(f"✓ 年化波动率: {volatility * 100:.2f}%")
    
    # 测试夏普比率
    sharpe = risk_metrics.sharpe_ratio(returns)
    print(f"✓ 夏普比率: {sharpe:.3f}")
    
    # 测试最大回撤
    max_dd = risk_metrics.max_drawdown(prices)
    print(f"✓ 最大回撤: {max_dd['max_drawdown'] * 100:.2f}%")
    
    # 测试VaR
    var_95 = risk_metrics.var(returns, 0.05)
    print(f"✓ 95% VaR: {var_95 * 100:.2f}%")
    
    print("\n2. 测试批量风险指标计算...")
    
    # 计算所有风险指标
    all_metrics = risk_metrics.calculate_all_metrics(prices)
    
    print("✓ 批量计算成功，风险指标汇总:")
    for key, value in all_metrics.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value:.3f}")
    
    return True


def test_performance_analyzer():
    """测试绩效分析模块"""
    print("\n" + "=" * 50)
    print("测试绩效分析模块")
    print("=" * 50)
    
    # 创建示例数据
    portfolio_data = create_sample_data()
    
    # 创建基准数据（模拟市场指数）
    benchmark_data = create_sample_data()
    benchmark_data['close'] = benchmark_data['close'] * 0.8  # 基准表现稍差
    
    # 创建绩效分析器
    analyzer = PerformanceAnalyzer()
    
    print("1. 测试投资组合分析...")
    
    # 分析投资组合
    analysis_result = analyzer.analyze_portfolio(
        portfolio_data, 
        benchmark_data,
        start_date='2023-01-01',
        end_date='2023-12-31'
    )
    
    if analysis_result:
        print("✓ 投资组合分析成功")
        
        # 显示部分结果
        summary = analysis_result.get('summary', {})
        print(f"  分析期间: {summary.get('start_date')} 至 {summary.get('end_date')}")
        print(f"  总交易天数: {summary.get('trading_days')}")
        
        risk_metrics = analysis_result.get('risk_metrics', {})
        print(f"  总收益率: {risk_metrics.get('total_return', 0):.2f}%")
        print(f"  年化收益率: {risk_metrics.get('annual_return', 0):.2f}%")
        print(f"  夏普比率: {risk_metrics.get('sharpe_ratio', 0):.3f}")
    else:
        print("✗ 投资组合分析失败")
        return False
    
    print("\n2. 测试绩效报告生成...")
    
    # 生成绩效报告
    report = analyzer.generate_performance_report(analysis_result)
    
    if report and "报告生成失败" not in report:
        print("✓ 绩效报告生成成功")
        print("\n报告预览（前20行）:")
        print("\n".join(report.split('\n')[:20]))
    else:
        print("✗ 绩效报告生成失败")
        return False
    
    return True


def test_real_data():
    """测试真实数据"""
    print("\n" + "=" * 50)
    print("测试真实数据分析")
    print("=" * 50)
    
    try:
        # 获取真实数据
        collector = AKShareCollector()
        if not collector.connect():
            print("✗ 无法连接数据源，跳过真实数据测试")
            return True
        
        print("1. 获取真实股票数据...")
        
        # 获取平安银行数据
        stock_data = collector.get_stock_data(
            "000001", 
            start_date="2023-01-01", 
            end_date="2023-12-31"
        )
        
        if stock_data.empty:
            print("✗ 无法获取股票数据")
            return False
        
        print(f"✓ 获取股票数据成功，共 {len(stock_data)} 条记录")
        
        print("\n2. 计算真实数据的技术指标...")
        
        # 计算技术指标
        tech_indicators = TechnicalIndicators()
        stock_with_indicators = tech_indicators.calculate_all_indicators(stock_data)
        
        print(f"✓ 技术指标计算成功，共 {len(stock_with_indicators.columns)} 个字段")
        
        print("\n3. 分析真实数据的风险指标...")
        
        # 计算风险指标
        risk_metrics = RiskMetrics()
        risk_result = risk_metrics.calculate_all_metrics(stock_data['close'])
        
        print("✓ 风险指标计算成功")
        print(f"  年化收益率: {risk_result.get('annual_return', 0):.2f}%")
        print(f"  年化波动率: {risk_result.get('volatility', 0):.2f}%")
        print(f"  夏普比率: {risk_result.get('sharpe_ratio', 0):.3f}")
        print(f"  最大回撤: {risk_result.get('max_drawdown', {}).get('max_drawdown', 0) * 100:.2f}%")
        
        collector.disconnect()
        return True
        
    except Exception as e:
        print(f"✗ 真实数据测试失败: {e}")
        return False


def main():
    """主函数"""
    print("量化交易系统 - 分析模块测试")
    print("开始时间:", datetime.now())
    
    # 设置日志
    logger = setup_logger()
    
    # 测试各个模块
    tech_success = test_technical_indicators()
    risk_success = test_risk_metrics()
    perf_success = test_performance_analyzer()
    real_success = test_real_data()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"技术指标模块: {'✓ 通过' if tech_success else '✗ 失败'}")
    print(f"风险指标模块: {'✓ 通过' if risk_success else '✗ 失败'}")
    print(f"绩效分析模块: {'✓ 通过' if perf_success else '✗ 失败'}")
    print(f"真实数据测试: {'✓ 通过' if real_success else '✗ 失败'}")
    
    if all([tech_success, risk_success, perf_success, real_success]):
        print("\n🎉 所有测试通过！分析模块工作正常。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略中心组件
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSplitter,
    QGroupBox, QTableWidget, QTableWidgetItem, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget,
    QSpinBox, QDoubleSpinBox, QCheckBox, QListWidget, QListWidgetItem,
    QDialog, QMessageBox, QDialogButtonBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QPalette, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger


class StrategyCenterWidget(QWidget):
    """策略中心组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("StrategyCenterWidget")
        self.strategies = []  # 策略列表
        self.init_ui()
        self.load_strategies()
        self.logger.info("策略中心组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_label = QLabel("策略中心")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 策略列表标签页
        self.tab_widget.addTab(self.create_strategy_list_tab(), "策略列表")

        # 策略配置标签页
        self.tab_widget.addTab(self.create_strategy_config_tab(), "策略配置")

        # 回测分析标签页
        self.tab_widget.addTab(self.create_backtest_tab(), "回测分析")

        # 实时监控标签页
        self.tab_widget.addTab(self.create_monitor_tab(), "实时监控")

        layout.addWidget(self.tab_widget)

    def create_strategy_list_tab(self):
        """创建策略列表标签页"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # 左侧策略列表
        left_layout = QVBoxLayout()

        # 策略列表
        strategies_group = QGroupBox("策略列表")
        strategies_layout = QVBoxLayout(strategies_group)

        self.strategy_list = QListWidget()
        self.strategy_list.itemClicked.connect(self.on_strategy_selected)
        strategies_layout.addWidget(self.strategy_list)

        # 策略操作按钮
        button_layout = QHBoxLayout()

        add_btn = QPushButton("新建策略")
        add_btn.clicked.connect(self.add_strategy)
        button_layout.addWidget(add_btn)

        edit_btn = QPushButton("编辑策略")
        edit_btn.clicked.connect(self.edit_strategy)
        button_layout.addWidget(edit_btn)

        delete_btn = QPushButton("删除策略")
        delete_btn.clicked.connect(self.delete_strategy)
        button_layout.addWidget(delete_btn)

        strategies_layout.addLayout(button_layout)
        left_layout.addWidget(strategies_group)

        # 右侧策略详情
        right_layout = QVBoxLayout()

        # 策略信息
        info_group = QGroupBox("策略信息")
        info_layout = QGridLayout(info_group)

        self.strategy_name_label = QLabel("策略名称: 未选择")


        self.strategy_name_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_name_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_type_label = QLabel("策略类型: 未知")

        self.strategy_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_status_label = QLabel("运行状态: 停止")

        self.strategy_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_return_label = QLabel("累计收益: 0.00%")

        self.strategy_return_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_return_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        info_layout.addWidget(self.strategy_name_label, 0, 0)
        info_layout.addWidget(self.strategy_type_label, 0, 1)
        info_layout.addWidget(self.strategy_status_label, 1, 0)
        info_layout.addWidget(self.strategy_return_label, 1, 1)

        right_layout.addWidget(info_group)

        # 策略控制
        control_group = QGroupBox("策略控制")
        control_layout = QHBoxLayout(control_group)

        self.start_btn = QPushButton("启动策略")
        self.start_btn.clicked.connect(self.start_strategy)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止策略")
        self.stop_btn.clicked.connect(self.stop_strategy)
        control_layout.addWidget(self.stop_btn)

        self.pause_btn = QPushButton("暂停策略")
        self.pause_btn.clicked.connect(self.pause_strategy)
        control_layout.addWidget(self.pause_btn)

        control_layout.addStretch()
        right_layout.addWidget(control_group)

        # 策略参数
        params_group = QGroupBox("策略参数")
        params_layout = QVBoxLayout(params_group)

        self.params_table = QTableWidget()
        self.params_table.setColumnCount(2)
        self.params_table.setHorizontalHeaderLabels(["参数名", "参数值"])
        self.params_table.horizontalHeader().setStretchLastSection(True)
        params_layout.addWidget(self.params_table)

        right_layout.addWidget(params_group)

        # 添加到主布局
        layout.addLayout(left_layout, 1)
        layout.addLayout(right_layout, 2)

        return widget

    def create_strategy_config_tab(self):
        """创建策略配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本配置
        basic_group = QGroupBox("基本配置")
        basic_layout = QGridLayout(basic_group)

        strategy_name_label = QLabel("策略名称:")
        strategy_name_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        basic_layout.addWidget(strategy_name_label, 0, 0)
        self.config_name_input = QLineEdit()
        basic_layout.addWidget(self.config_name_input, 0, 1)

        strategy_type_label = QLabel("策略类型:")
        strategy_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        basic_layout.addWidget(strategy_type_label, 1, 0)
        self.config_type_combo = QComboBox()
        self.config_type_combo.addItems([
            "移动平均策略", "双移动平均策略", "MACD策略",
            "RSI策略", "布林带策略", "自定义策略"
        ])
        basic_layout.addWidget(self.config_type_combo, 1, 1)

        target_stocks_label = QLabel("目标股票:")
        target_stocks_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        basic_layout.addWidget(target_stocks_label, 2, 0)
        self.config_symbols_input = QLineEdit()
        self.config_symbols_input.setPlaceholderText("多个股票用逗号分隔")
        basic_layout.addWidget(self.config_symbols_input, 2, 1)

        layout.addWidget(basic_group)

        # 参数配置
        params_group = QGroupBox("参数配置")
        params_layout = QGridLayout(params_group)

        # 移动平均参数
        short_ma_label = QLabel("短期均线:")
        short_ma_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        params_layout.addWidget(short_ma_label, 0, 0)
        self.short_ma_spin = QSpinBox()
        self.short_ma_spin.setRange(1, 100)
        self.short_ma_spin.setValue(5)
        params_layout.addWidget(self.short_ma_spin, 0, 1)

        long_ma_label = QLabel("长期均线:")
        long_ma_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        params_layout.addWidget(long_ma_label, 1, 0)
        self.long_ma_spin = QSpinBox()
        self.long_ma_spin.setRange(1, 200)
        self.long_ma_spin.setValue(20)
        params_layout.addWidget(self.long_ma_spin, 1, 1)

        # 风险控制参数
        stop_loss_label = QLabel("止损比例:")
        stop_loss_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        params_layout.addWidget(stop_loss_label, 2, 0)
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(0.01, 0.5)
        self.stop_loss_spin.setValue(0.1)
        self.stop_loss_spin.setSuffix("%")
        params_layout.addWidget(self.stop_loss_spin, 2, 1)

        take_profit_label = QLabel("止盈比例:")
        take_profit_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        params_layout.addWidget(take_profit_label, 3, 0)
        self.take_profit_spin = QDoubleSpinBox()
        self.take_profit_spin.setRange(0.01, 1.0)
        self.take_profit_spin.setValue(0.2)
        self.take_profit_spin.setSuffix("%")
        params_layout.addWidget(self.take_profit_spin, 3, 1)

        layout.addWidget(params_group)

        # 高级配置
        advanced_group = QGroupBox("高级配置")
        advanced_layout = QGridLayout(advanced_group)

        self.enable_ml_check = QCheckBox("启用机器学习增强")
        advanced_layout.addWidget(self.enable_ml_check, 0, 0)

        self.enable_sentiment_check = QCheckBox("启用情感分析")
        advanced_layout.addWidget(self.enable_sentiment_check, 0, 1)

        self.enable_news_check = QCheckBox("启用新闻分析")
        advanced_layout.addWidget(self.enable_news_check, 1, 0)

        layout.addWidget(advanced_group)

        # 保存按钮
        save_layout = QHBoxLayout()
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_strategy_config)
        save_layout.addWidget(save_btn)
        save_layout.addStretch()

        layout.addLayout(save_layout)
        layout.addStretch()

        return widget

    def create_backtest_tab(self):
        """创建回测分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 回测参数
        backtest_group = QGroupBox("回测参数")
        backtest_layout = QGridLayout(backtest_group)

        start_date_label = QLabel("开始日期:")
        start_date_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        backtest_layout.addWidget(start_date_label, 0, 0)
        self.backtest_start_date = QDateEdit()
        self.backtest_start_date.setDate(QDate.currentDate().addYears(-1))
        self.backtest_start_date.setCalendarPopup(True)
        backtest_layout.addWidget(self.backtest_start_date, 0, 1)

        end_date_label = QLabel("结束日期:")
        end_date_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        backtest_layout.addWidget(end_date_label, 1, 0)
        self.backtest_end_date = QDateEdit()
        self.backtest_end_date.setDate(QDate.currentDate())
        self.backtest_end_date.setCalendarPopup(True)
        backtest_layout.addWidget(self.backtest_end_date, 1, 1)

        initial_capital_label = QLabel("初始资金:")
        initial_capital_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        backtest_layout.addWidget(initial_capital_label, 2, 0)
        self.initial_capital_spin = QSpinBox()
        self.initial_capital_spin.setRange(10000, 10000000)
        self.initial_capital_spin.setValue(1000000)
        self.initial_capital_spin.setSuffix(" 元")
        backtest_layout.addWidget(self.initial_capital_spin, 2, 1)

        start_backtest_btn = QPushButton("开始回测")
        start_backtest_btn.clicked.connect(self.start_backtest)
        backtest_layout.addWidget(start_backtest_btn, 3, 0, 1, 2)

        layout.addWidget(backtest_group)

        # 回测结果
        results_group = QGroupBox("回测结果")
        results_layout = QVBoxLayout(results_group)

        # 结果表格
        self.backtest_results_table = QTableWidget()
        self.backtest_results_table.setColumnCount(2)
        self.backtest_results_table.setHorizontalHeaderLabels(["指标", "数值"])
        self.backtest_results_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.backtest_results_table)

        layout.addWidget(results_group)

        return widget

    def create_monitor_tab(self):
        """创建实时监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 监控状态
        status_group = QGroupBox("监控状态")
        status_layout = QGridLayout(status_group)

        self.monitor_status_label = QLabel("监控状态: 停止")


        self.monitor_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.active_strategies_label = QLabel("活跃策略: 0")

        self.active_strategies_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_positions_label = QLabel("总持仓: 0")

        self.total_positions_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.daily_pnl_label = QLabel("今日盈亏: 0.00")

        self.daily_pnl_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        status_layout.addWidget(self.monitor_status_label, 0, 0)
        status_layout.addWidget(self.active_strategies_label, 0, 1)
        status_layout.addWidget(self.total_positions_label, 1, 0)
        status_layout.addWidget(self.daily_pnl_label, 1, 1)

        layout.addWidget(status_group)

        # 实时信号
        signals_group = QGroupBox("实时信号")
        signals_layout = QVBoxLayout(signals_group)

        self.signals_table = QTableWidget()
        self.signals_table.setColumnCount(6)
        self.signals_table.setHorizontalHeaderLabels([
            "时间", "策略", "股票", "信号", "价格", "置信度"
        ])
        self.signals_table.horizontalHeader().setStretchLastSection(True)
        signals_layout.addWidget(self.signals_table)

        layout.addWidget(signals_group)

        return widget

    def load_strategies(self):
        """加载策略列表"""
        # 添加示例策略
        sample_strategies = [
            {"name": "MA5_MA20策略", "type": "移动平均", "status": "停止", "return": "5.2%"},
            {"name": "MACD金叉策略", "type": "MACD", "status": "运行", "return": "3.8%"},
            {"name": "RSI超买超卖", "type": "RSI", "status": "暂停", "return": "-1.2%"},
        ]

        for strategy in sample_strategies:
            item = QListWidgetItem(f"{strategy['name']} ({strategy['status']})")
            self.strategy_list.addItem(item)
            self.strategies.append(strategy)

    def on_strategy_selected(self, item):
        """策略选择事件"""
        row = self.strategy_list.row(item)
        if row < len(self.strategies):
            strategy = self.strategies[row]
            self.strategy_name_label.setText(f"策略名称: {strategy['name']}")
            self.strategy_type_label.setText(f"策略类型: {strategy['type']}")
            self.strategy_status_label.setText(f"运行状态: {strategy['status']}")
            self.strategy_return_label.setText(f"累计收益: {strategy['return']}")

    def add_strategy(self):
        """新建策略"""
        try:
            dialog = StrategyDialog(self)
            dialog.setWindowTitle("新建策略")

            if dialog.exec_() == QDialog.Accepted:
                strategy_data = dialog.get_strategy_data()

                # 添加到策略列表
                self.strategies.append(strategy_data)

                # 更新界面
                item = QListWidgetItem(f"{strategy_data['name']} ({strategy_data['status']})")
                self.strategy_list.addItem(item)

                self.logger.info(f"新建策略成功: {strategy_data['name']}")
                QMessageBox.information(self, "成功", f"策略 '{strategy_data['name']}' 创建成功")

        except Exception as e:
            self.logger.error(f"新建策略失败: {e}")
            QMessageBox.critical(self, "错误", f"新建策略失败:\n{str(e)}")

    def edit_strategy(self):
        """编辑策略"""
        try:
            current_row = self.strategy_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要编辑的策略")
                return

            strategy = self.strategies[current_row]

            dialog = StrategyDialog(self, strategy)
            dialog.setWindowTitle("编辑策略")

            if dialog.exec_() == QDialog.Accepted:
                strategy_data = dialog.get_strategy_data()

                # 更新策略数据
                self.strategies[current_row] = strategy_data

                # 更新界面
                item = self.strategy_list.item(current_row)
                item.setText(f"{strategy_data['name']} ({strategy_data['status']})")

                # 更新详情显示
                self.on_strategy_selected(item)

                self.logger.info(f"编辑策略成功: {strategy_data['name']}")
                QMessageBox.information(self, "成功", f"策略 '{strategy_data['name']}' 更新成功")

        except Exception as e:
            self.logger.error(f"编辑策略失败: {e}")
            QMessageBox.critical(self, "错误", f"编辑策略失败:\n{str(e)}")

    def delete_strategy(self):
        """删除策略"""
        try:
            current_row = self.strategy_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要删除的策略")
                return

            strategy = self.strategies[current_row]

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除策略 '{strategy['name']}' 吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 从列表中删除
                del self.strategies[current_row]
                self.strategy_list.takeItem(current_row)

                # 清空详情显示
                self.strategy_name_label.setText("策略名称: 未选择")
                self.strategy_type_label.setText("策略类型: 未知")
                self.strategy_status_label.setText("运行状态: 停止")
                self.strategy_return_label.setText("累计收益: 0.00%")

                self.logger.info(f"删除策略成功: {strategy['name']}")
                QMessageBox.information(self, "成功", f"策略 '{strategy['name']}' 已删除")

        except Exception as e:
            self.logger.error(f"删除策略失败: {e}")
            QMessageBox.critical(self, "错误", f"删除策略失败:\n{str(e)}")

    def start_strategy(self):
        """启动策略"""
        try:
            current_row = self.strategy_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要启动的策略")
                return

            strategy = self.strategies[current_row]

            if strategy['status'] == '运行':
                QMessageBox.information(self, "提示", f"策略 '{strategy['name']}' 已在运行中")
                return

            # 更新策略状态
            strategy['status'] = '运行'
            self.strategies[current_row] = strategy

            # 更新界面
            item = self.strategy_list.item(current_row)
            item.setText(f"{strategy['name']} (运行)")
            self.strategy_status_label.setText("运行状态: 运行")

            self.logger.info(f"启动策略: {strategy['name']}")
            QMessageBox.information(self, "成功", f"策略 '{strategy['name']}' 已启动")

        except Exception as e:
            self.logger.error(f"启动策略失败: {e}")
            QMessageBox.critical(self, "错误", f"启动策略失败:\n{str(e)}")

    def stop_strategy(self):
        """停止策略"""
        try:
            current_row = self.strategy_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要停止的策略")
                return

            strategy = self.strategies[current_row]

            if strategy['status'] == '停止':
                QMessageBox.information(self, "提示", f"策略 '{strategy['name']}' 已停止")
                return

            # 确认停止
            reply = QMessageBox.question(
                self, "确认停止",
                f"确定要停止策略 '{strategy['name']}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 更新策略状态
                strategy['status'] = '停止'
                self.strategies[current_row] = strategy

                # 更新界面
                item = self.strategy_list.item(current_row)
                item.setText(f"{strategy['name']} (停止)")
                self.strategy_status_label.setText("运行状态: 停止")

                self.logger.info(f"停止策略: {strategy['name']}")
                QMessageBox.information(self, "成功", f"策略 '{strategy['name']}' 已停止")

        except Exception as e:
            self.logger.error(f"停止策略失败: {e}")
            QMessageBox.critical(self, "错误", f"停止策略失败:\n{str(e)}")

    def pause_strategy(self):
        """暂停策略"""
        try:
            current_row = self.strategy_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要暂停的策略")
                return

            strategy = self.strategies[current_row]

            if strategy['status'] != '运行':
                QMessageBox.information(self, "提示", f"只能暂停运行中的策略")
                return

            # 更新策略状态
            strategy['status'] = '暂停'
            self.strategies[current_row] = strategy

            # 更新界面
            item = self.strategy_list.item(current_row)
            item.setText(f"{strategy['name']} (暂停)")
            self.strategy_status_label.setText("运行状态: 暂停")

            self.logger.info(f"暂停策略: {strategy['name']}")
            QMessageBox.information(self, "成功", f"策略 '{strategy['name']}' 已暂停")

        except Exception as e:
            self.logger.error(f"暂停策略失败: {e}")
            QMessageBox.critical(self, "错误", f"暂停策略失败:\n{str(e)}")

    def save_strategy_config(self):
        """保存策略配置"""
        try:
            # 获取配置数据
            config_data = {
                'name': self.config_name_input.text().strip(),
                'type': self.config_type_combo.currentText(),
                'symbols': self.config_symbols_input.text().strip(),
                'short_ma': self.short_ma_spin.value(),
                'long_ma': self.long_ma_spin.value(),
                'stop_loss': self.stop_loss_spin.value(),
                'take_profit': self.take_profit_spin.value(),
                'enable_ml': self.enable_ml_check.isChecked(),
                'enable_sentiment': self.enable_sentiment_check.isChecked(),
                'enable_news': self.enable_news_check.isChecked(),
            }

            # 验证配置
            if not config_data['name']:
                QMessageBox.warning(self, "警告", "请输入策略名称")
                return

            if not config_data['symbols']:
                QMessageBox.warning(self, "警告", "请输入目标股票代码")
                return

            # 保存配置到文件
            import json
            config_file = project_root / "config" / f"{config_data['name']}_config.json"
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"策略配置已保存: {config_file}")
            QMessageBox.information(self, "成功", f"策略配置已保存到:\n{config_file}")

        except Exception as e:
            self.logger.error(f"保存策略配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存策略配置失败:\n{str(e)}")

    def start_backtest(self):
        """开始回测"""
        try:
            # 获取回测参数
            start_date = self.backtest_start_date.date().toString("yyyy-MM-dd")
            end_date = self.backtest_end_date.date().toString("yyyy-MM-dd")
            initial_capital = self.initial_capital_spin.value()

            self.logger.info(f"开始回测: {start_date} 到 {end_date}, 初始资金: {initial_capital}")

            # 模拟回测结果
            import random

            # 生成模拟回测结果
            total_return = random.uniform(-20, 50)  # 总收益率
            annual_return = total_return / 2  # 年化收益率
            max_drawdown = random.uniform(5, 25)  # 最大回撤
            sharpe_ratio = random.uniform(0.5, 2.5)  # 夏普比率
            win_rate = random.uniform(40, 80)  # 胜率
            total_trades = random.randint(50, 200)  # 总交易次数

            # 更新回测结果表格
            results = [
                ("总收益率", f"{total_return:.2f}%"),
                ("年化收益率", f"{annual_return:.2f}%"),
                ("最大回撤", f"{max_drawdown:.2f}%"),
                ("夏普比率", f"{sharpe_ratio:.2f}"),
                ("胜率", f"{win_rate:.2f}%"),
                ("总交易次数", f"{total_trades}"),
                ("初始资金", f"¥{initial_capital:,}"),
                ("最终资金", f"¥{initial_capital * (1 + total_return/100):,.2f}"),
            ]

            self.backtest_results_table.setRowCount(len(results))
            for row, (metric, value) in enumerate(results):
                self.backtest_results_table.setItem(row, 0, QTableWidgetItem(metric))
                self.backtest_results_table.setItem(row, 1, QTableWidgetItem(value))

            self.backtest_results_table.resizeColumnsToContents()

            self.logger.info("回测完成")
            QMessageBox.information(self, "成功", "回测完成！请查看回测结果。")

        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            QMessageBox.critical(self, "错误", f"回测失败:\n{str(e)}")


class StrategyDialog(QDialog):
    """策略编辑对话框"""

    def __init__(self, parent=None, strategy_data=None):
        super().__init__(parent)
        self.strategy_data = strategy_data or {}
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化界面"""
        self.setFixedSize(400, 300)
        layout = QVBoxLayout(self)

        # 基本信息
        form_layout = QGridLayout()

        strategy_name_label = QLabel("策略名称:")
        strategy_name_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        form_layout.addWidget(strategy_name_label, 0, 0)
        self.name_input = QLineEdit()
        form_layout.addWidget(self.name_input, 0, 1)

        strategy_type_label = QLabel("策略类型:")
        strategy_type_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        form_layout.addWidget(strategy_type_label, 1, 0)
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "移动平均", "MACD", "RSI", "布林带", "自定义"
        ])
        form_layout.addWidget(self.type_combo, 1, 1)

        target_stocks_label = QLabel("目标股票:")
        target_stocks_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        form_layout.addWidget(target_stocks_label, 2, 0)
        self.symbols_input = QLineEdit()
        self.symbols_input.setPlaceholderText("例如: 000001,000002")
        form_layout.addWidget(self.symbols_input, 2, 1)

        status_label = QLabel("运行状态:")
        status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        form_layout.addWidget(status_label, 3, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["停止", "运行", "暂停"])
        form_layout.addWidget(self.status_combo, 3, 1)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_data(self):
        """加载数据"""
        if self.strategy_data:
            self.name_input.setText(self.strategy_data.get('name', ''))
            self.type_combo.setCurrentText(self.strategy_data.get('type', '移动平均'))
            self.symbols_input.setText(self.strategy_data.get('symbols', ''))
            self.status_combo.setCurrentText(self.strategy_data.get('status', '停止'))

    def get_strategy_data(self):
        """获取策略数据"""
        return {
            'name': self.name_input.text().strip(),
            'type': self.type_combo.currentText(),
            'symbols': self.symbols_input.text().strip(),
            'status': self.status_combo.currentText(),
            'return': self.strategy_data.get('return', '0.00%')
        }

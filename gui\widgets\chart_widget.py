#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表显示组件
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QGroupBox, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib import style
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from utils.logger import get_logger


class ChartWidget(QWidget):
    """图表显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("ChartWidget")
        self.init_ui()
        self.logger.info("图表组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 图表区域
        if MATPLOTLIB_AVAILABLE:
            self.chart_area = self.create_chart_area()
            layout.addWidget(self.chart_area)
        else:
            placeholder = QLabel("Matplotlib未安装，无法显示图表")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: gray; font-size: 14px;")
            layout.addWidget(placeholder)
    
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("图表控制")
        layout = QHBoxLayout(group)
        
        # 图表类型选择
        layout.addWidget(QLabel("图表类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "K线图", "收益率曲线", "技术指标", "成交量", "资金流向"
        ])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        layout.addWidget(self.chart_type_combo)
        
        # 时间周期选择
        layout.addWidget(QLabel("时间周期:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "1分钟", "5分钟", "15分钟", "30分钟", "1小时", "日线", "周线", "月线"
        ])
        self.period_combo.currentTextChanged.connect(self.update_chart)
        layout.addWidget(self.period_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_chart)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        return group
    
    def create_chart_area(self):
        """创建图表区域"""
        # 设置matplotlib样式
        style.use('dark_background')
        
        # 创建图形和画布
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        
        # 初始化图表
        self.update_chart()
        
        return self.canvas
    
    def update_chart(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        try:
            chart_type = self.chart_type_combo.currentText()
            period = self.period_combo.currentText()
            
            # 清除之前的图表
            self.figure.clear()
            
            if chart_type == "K线图":
                self.plot_candlestick()
            elif chart_type == "收益率曲线":
                self.plot_returns()
            elif chart_type == "技术指标":
                self.plot_technical_indicators()
            elif chart_type == "成交量":
                self.plot_volume()
            elif chart_type == "资金流向":
                self.plot_money_flow()
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            self.logger.error(f"更新图表失败: {e}")
    
    def plot_candlestick(self):
        """绘制K线图"""
        # 生成模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 模拟股价数据
        price = 100
        prices = []
        for _ in range(100):
            change = np.random.normal(0, 2)
            price += change
            prices.append(price)
        
        # 生成OHLC数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close + np.random.normal(0, 0.5)
            high = max(open_price, close) + abs(np.random.normal(0, 1))
            low = min(open_price, close) - abs(np.random.normal(0, 1))
            data.append([date, open_price, high, low, close])
        
        df = pd.DataFrame(data, columns=['Date', 'Open', 'High', 'Low', 'Close'])
        
        # 绘制K线图
        ax = self.figure.add_subplot(111)
        
        # 绘制收盘价线
        ax.plot(df['Date'], df['Close'], color='white', linewidth=1, label='收盘价')
        
        # 设置标题和标签
        ax.set_title('股票K线图', fontsize=14, color='white')
        ax.set_xlabel('日期', color='white')
        ax.set_ylabel('价格', color='white')
        
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=10))
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 调整布局
        self.figure.tight_layout()
    
    def plot_returns(self):
        """绘制收益率曲线"""
        # 生成模拟收益率数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 策略收益率
        strategy_returns = np.random.normal(0.001, 0.02, 100)
        strategy_cumulative = (1 + pd.Series(strategy_returns)).cumprod()
        
        # 基准收益率
        benchmark_returns = np.random.normal(0.0005, 0.015, 100)
        benchmark_cumulative = (1 + pd.Series(benchmark_returns)).cumprod()
        
        # 绘制收益率曲线
        ax = self.figure.add_subplot(111)
        
        ax.plot(dates, strategy_cumulative, color='#00ff00', linewidth=2, label='策略收益')
        ax.plot(dates, benchmark_cumulative, color='#ff6600', linewidth=2, label='基准收益')
        
        # 设置标题和标签
        ax.set_title('累计收益率曲线', fontsize=14, color='white')
        ax.set_xlabel('日期', color='white')
        ax.set_ylabel('累计收益率', color='white')
        
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=10))
        
        # 设置网格和图例
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 调整布局
        self.figure.tight_layout()
    
    def plot_technical_indicators(self):
        """绘制技术指标"""
        # 生成模拟数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 价格数据
        price = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        # 移动平均线
        ma5 = pd.Series(price).rolling(5).mean()
        ma20 = pd.Series(price).rolling(20).mean()
        
        # RSI指标
        rsi = 50 + 20 * np.sin(np.arange(100) * 0.1) + np.random.normal(0, 5, 100)
        rsi = np.clip(rsi, 0, 100)
        
        # 创建子图
        ax1 = self.figure.add_subplot(211)
        ax2 = self.figure.add_subplot(212)
        
        # 绘制价格和移动平均线
        ax1.plot(dates, price, color='white', linewidth=1, label='价格')
        ax1.plot(dates, ma5, color='yellow', linewidth=1, label='MA5')
        ax1.plot(dates, ma20, color='cyan', linewidth=1, label='MA20')
        
        ax1.set_title('价格与移动平均线', fontsize=12, color='white')
        ax1.set_ylabel('价格', color='white')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 绘制RSI
        ax2.plot(dates, rsi, color='orange', linewidth=1, label='RSI')
        ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='超买线')
        ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='超卖线')
        
        ax2.set_title('RSI指标', fontsize=12, color='white')
        ax2.set_xlabel('日期', color='white')
        ax2.set_ylabel('RSI', color='white')
        ax2.set_ylim(0, 100)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 设置日期格式
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=10))
        
        # 调整布局
        self.figure.tight_layout()
    
    def plot_volume(self):
        """绘制成交量"""
        # 生成模拟成交量数据
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        volume = np.random.lognormal(10, 0.5, 100)
        
        # 绘制成交量柱状图
        ax = self.figure.add_subplot(111)
        
        bars = ax.bar(dates, volume, color='lightblue', alpha=0.7, width=0.8)
        
        # 设置标题和标签
        ax.set_title('成交量', fontsize=14, color='white')
        ax.set_xlabel('日期', color='white')
        ax.set_ylabel('成交量', color='white')
        
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=10))
        
        # 设置网格
        ax.grid(True, alpha=0.3)
        
        # 调整布局
        self.figure.tight_layout()
    
    def plot_money_flow(self):
        """绘制资金流向"""
        # 生成模拟资金流向数据
        dates = pd.date_range(start='2024-01-01', periods=20, freq='D')
        np.random.seed(42)
        
        inflow = np.random.uniform(1000, 5000, 20)
        outflow = np.random.uniform(1000, 5000, 20)
        net_flow = inflow - outflow
        
        # 绘制资金流向图
        ax = self.figure.add_subplot(111)
        
        # 绘制流入和流出
        ax.bar(dates, inflow, color='red', alpha=0.7, label='资金流入')
        ax.bar(dates, -outflow, color='green', alpha=0.7, label='资金流出')
        
        # 绘制净流入线
        ax2 = ax.twinx()
        ax2.plot(dates, net_flow, color='yellow', linewidth=2, marker='o', label='净流入')
        
        # 设置标题和标签
        ax.set_title('资金流向分析', fontsize=14, color='white')
        ax.set_xlabel('日期', color='white')
        ax.set_ylabel('资金流量', color='white')
        ax2.set_ylabel('净流入', color='white')
        
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        
        # 设置网格和图例
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
        
        # 调整布局
        self.figure.tight_layout()
    
    def refresh_chart(self):
        """刷新图表"""
        self.update_chart()
        self.logger.info("图表已刷新")

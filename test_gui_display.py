#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI显示
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("量化交易系统 - GUI测试")
        self.setGeometry(200, 200, 800, 600)
        
        # 设置窗口属性，确保显示在前台
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("🚀 量化交易系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("color: #2E86AB; margin: 20px;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel("✅ GUI界面正常显示")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setFont(QFont("Arial", 16))
        status_label.setStyleSheet("color: #28A745; margin: 10px;")
        layout.addWidget(status_label)
        
        # 系统信息
        info_label = QLabel("""
        📊 系统功能模块：
        • 数据采集与管理
        • 技术分析与指标
        • 策略开发与回测
        • 风险管理与控制
        • 机器学习模型
        • 实时交易监控
        """)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 12))
        info_label.setStyleSheet("color: #495057; margin: 10px; line-height: 1.5;")
        layout.addWidget(info_label)
        
        # 测试按钮
        test_button = QPushButton("🔧 测试系统功能")
        test_button.setFont(QFont("Arial", 14))
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        test_button.clicked.connect(self.test_system)
        layout.addWidget(test_button)
        
        # 启动完整系统按钮
        start_button = QPushButton("🚀 启动完整系统")
        start_button.setFont(QFont("Arial", 14))
        start_button.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1E7E34;
            }
            QPushButton:pressed {
                background-color: #155724;
            }
        """)
        start_button.clicked.connect(self.start_full_system)
        layout.addWidget(start_button)
        
        # 退出按钮
        exit_button = QPushButton("❌ 退出")
        exit_button.setFont(QFont("Arial", 14))
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
            QPushButton:pressed {
                background-color: #A71D2A;
            }
        """)
        exit_button.clicked.connect(self.close)
        layout.addWidget(exit_button)
        
        # 添加弹性空间
        layout.addStretch()
        
        print("✅ 测试GUI窗口创建成功")
    
    def test_system(self):
        """测试系统功能"""
        QMessageBox.information(
            self,
            "系统测试",
            "🎉 系统功能测试通过！\n\n"
            "✅ GUI界面正常\n"
            "✅ 事件处理正常\n"
            "✅ 窗口显示正常\n"
            "✅ 用户交互正常"
        )
    
    def start_full_system(self):
        """启动完整系统"""
        reply = QMessageBox.question(
            self,
            "启动完整系统",
            "确定要启动完整的量化交易系统吗？\n\n"
            "这将关闭当前测试窗口并启动主程序。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            # 启动主程序
            import subprocess
            subprocess.Popen([sys.executable, "main.py"])
    
    def closeEvent(self, event):
        """关闭事件"""
        print("🔚 测试GUI窗口关闭")
        event.accept()

def main():
    """主函数"""
    print("=" * 60)
    print("量化交易系统 - GUI显示测试")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("量化交易系统GUI测试")
        
        # 设置应用程序样式
        app.setStyleSheet("""
            QMainWindow {
                background-color: #F8F9FA;
            }
            QWidget {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
        """)
        
        # 创建主窗口
        window = TestMainWindow()
        
        # 显示窗口
        window.show()
        window.raise_()
        window.activateWindow()
        
        # 强制处理事件
        app.processEvents()
        
        print("✅ GUI测试窗口已显示")
        print("📝 请检查是否能看到窗口")
        print("🔧 可以点击按钮测试功能")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 GUI测试结束，退出码: {exit_code}")
    sys.exit(exit_code)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置界面
"""

import sys
import json
from pathlib import Path
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QGroupBox, QTabWidget,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QFileDialog, QMessageBox,
    QSlider
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
# from config.settings import Settings


class SettingsWidget(QWidget):
    """系统设置组件"""

    # 信号定义
    settings_changed = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SettingsWidget")
        self.settings_file = project_root / "config" / "user_settings.json"
        self.current_settings = self.load_settings()
        self.init_ui()
        self.logger.info("设置界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_label = QLabel("系统设置")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 基本设置
        self.tab_widget.addTab(self.create_general_tab(), "基本设置")

        # 交易设置
        self.tab_widget.addTab(self.create_trading_tab(), "交易设置")

        # 数据设置
        self.tab_widget.addTab(self.create_data_tab(), "数据设置")

        # 界面设置
        self.tab_widget.addTab(self.create_ui_tab(), "界面设置")

        # 风险设置
        self.tab_widget.addTab(self.create_risk_tab(), "风险设置")

        layout.addWidget(self.tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()

        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)

        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(reset_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(cancel_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def create_general_tab(self):
        """创建基本设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 系统设置
        system_group = QGroupBox("系统设置")
        system_layout = QGridLayout(system_group)

        # 自动启动
        auto_start_label = QLabel("自动启动:")
        auto_start_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        system_layout.addWidget(auto_start_label, 0, 0)
        self.auto_start_check = QCheckBox()
        self.auto_start_check.setChecked(self.current_settings.get('auto_start', False))
        system_layout.addWidget(self.auto_start_check, 0, 1)

        # 最小化到托盘
        minimize_label = QLabel("最小化到托盘:")
        minimize_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        system_layout.addWidget(minimize_label, 1, 0)
        self.minimize_to_tray_check = QCheckBox()
        self.minimize_to_tray_check.setChecked(self.current_settings.get('minimize_to_tray', True))
        system_layout.addWidget(self.minimize_to_tray_check, 1, 1)

        # 启动时检查更新
        update_label = QLabel("启动时检查更新:")
        update_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        system_layout.addWidget(update_label, 2, 0)
        self.check_update_check = QCheckBox()
        self.check_update_check.setChecked(self.current_settings.get('check_update', True))
        system_layout.addWidget(self.check_update_check, 2, 1)

        # 日志级别
        log_level_label = QLabel("日志级别:")
        log_level_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        system_layout.addWidget(log_level_label, 3, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText(self.current_settings.get('log_level', 'INFO'))
        system_layout.addWidget(self.log_level_combo, 3, 1)

        layout.addWidget(system_group)

        # 路径设置
        path_group = QGroupBox("路径设置")
        path_layout = QGridLayout(path_group)

        # 数据目录
        data_dir_label = QLabel("数据目录:")
        data_dir_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        path_layout.addWidget(data_dir_label, 0, 0)
        self.data_dir_edit = QLineEdit()
        self.data_dir_edit.setText(self.current_settings.get('data_dir', str(project_root / "data")))
        path_layout.addWidget(self.data_dir_edit, 0, 1)

        data_dir_btn = QPushButton("浏览")
        data_dir_btn.clicked.connect(lambda: self.browse_directory(self.data_dir_edit))
        path_layout.addWidget(data_dir_btn, 0, 2)

        # 日志目录
        log_dir_label = QLabel("日志目录:")
        log_dir_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        path_layout.addWidget(log_dir_label, 1, 0)
        self.log_dir_edit = QLineEdit()
        self.log_dir_edit.setText(self.current_settings.get('log_dir', str(project_root / "logs")))
        path_layout.addWidget(self.log_dir_edit, 1, 1)

        log_dir_btn = QPushButton("浏览")
        log_dir_btn.clicked.connect(lambda: self.browse_directory(self.log_dir_edit))
        path_layout.addWidget(log_dir_btn, 1, 2)

        layout.addWidget(path_group)
        layout.addStretch()

        return widget

    def create_trading_tab(self):
        """创建交易设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 券商设置
        broker_group = QGroupBox("券商设置")
        broker_layout = QGridLayout(broker_group)

        # 默认券商
        default_broker_label = QLabel("默认券商:")
        default_broker_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        broker_layout.addWidget(default_broker_label, 0, 0)
        self.broker_combo = QComboBox()
        self.broker_combo.addItems(["模拟交易", "华泰证券", "中信证券", "国泰君安"])
        self.broker_combo.setCurrentText(self.current_settings.get('default_broker', '模拟交易'))
        broker_layout.addWidget(self.broker_combo, 0, 1)

        # API密钥
        api_key_label = QLabel("API密钥:")
        api_key_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        broker_layout.addWidget(api_key_label, 1, 0)
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setText(self.current_settings.get('api_key', ''))
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        broker_layout.addWidget(self.api_key_edit, 1, 1)

        # 密钥
        secret_key_label = QLabel("密钥:")
        secret_key_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        broker_layout.addWidget(secret_key_label, 2, 0)
        self.secret_key_edit = QLineEdit()
        self.secret_key_edit.setText(self.current_settings.get('secret_key', ''))
        self.secret_key_edit.setEchoMode(QLineEdit.Password)
        broker_layout.addWidget(self.secret_key_edit, 2, 1)

        # 账户ID
        account_id_label = QLabel("账户ID:")
        account_id_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        broker_layout.addWidget(account_id_label, 3, 0)
        self.account_id_edit = QLineEdit()
        self.account_id_edit.setText(self.current_settings.get('account_id', ''))
        broker_layout.addWidget(self.account_id_edit, 3, 1)

        layout.addWidget(broker_group)

        # 交易参数
        trading_group = QGroupBox("交易参数")
        trading_layout = QGridLayout(trading_group)

        # 默认手续费率
        commission_rate_label = QLabel("手续费率(%):")
        commission_rate_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        trading_layout.addWidget(commission_rate_label, 0, 0)
        self.commission_rate_spin = QDoubleSpinBox()
        self.commission_rate_spin.setRange(0.0001, 1.0)
        self.commission_rate_spin.setDecimals(4)
        self.commission_rate_spin.setValue(self.current_settings.get('commission_rate', 0.0003))
        trading_layout.addWidget(self.commission_rate_spin, 0, 1)

        # 印花税率
        stamp_tax_label = QLabel("印花税率(%):")

        stamp_tax_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        trading_layout.addWidget(stamp_tax_label, 1, 0)
        self.stamp_tax_spin = QDoubleSpinBox()
        self.stamp_tax_spin.setRange(0.0, 1.0)
        self.stamp_tax_spin.setDecimals(4)
        self.stamp_tax_spin.setValue(self.current_settings.get('stamp_tax', 0.001))
        trading_layout.addWidget(self.stamp_tax_spin, 1, 1)

        # 滑点
        slippage_label = QLabel("滑点(%):")
        slippage_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        trading_layout.addWidget(slippage_label, 2, 0)
        self.slippage_spin = QDoubleSpinBox()
        self.slippage_spin.setRange(0.0, 1.0)
        self.slippage_spin.setDecimals(4)
        self.slippage_spin.setValue(self.current_settings.get('slippage', 0.001))
        trading_layout.addWidget(self.slippage_spin, 2, 1)

        layout.addWidget(trading_group)
        layout.addStretch()

        return widget

    def create_data_tab(self):
        """创建数据设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 数据源设置
        source_group = QGroupBox("数据源设置")
        source_layout = QGridLayout(source_group)

        # 主数据源
        primary_source_label = QLabel("主数据源:")

        primary_source_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        source_layout.addWidget(primary_source_label, 0, 0)
        self.primary_source_combo = QComboBox()
        self.primary_source_combo.addItems(["AKShare", "Tushare", "东方财富"])
        self.primary_source_combo.setCurrentText(self.current_settings.get('primary_source', 'AKShare'))
        source_layout.addWidget(self.primary_source_combo, 0, 1)

        # 备用数据源
        backup_source_label = QLabel("备用数据源:")

        backup_source_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        source_layout.addWidget(backup_source_label, 1, 0)
        self.backup_source_combo = QComboBox()
        self.backup_source_combo.addItems(["无", "AKShare", "Tushare", "东方财富"])
        self.backup_source_combo.setCurrentText(self.current_settings.get('backup_source', '无'))
        source_layout.addWidget(self.backup_source_combo, 1, 1)

        # 实时数据源
        realtime_source_label = QLabel("实时数据源:")

        realtime_source_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        source_layout.addWidget(realtime_source_label, 2, 0)
        self.realtime_source_combo = QComboBox()
        self.realtime_source_combo.addItems(["新浪", "腾讯", "东方财富"])
        self.realtime_source_combo.setCurrentText(self.current_settings.get('realtime_source', '新浪'))
        source_layout.addWidget(self.realtime_source_combo, 2, 1)

        layout.addWidget(source_group)

        # 数据更新设置
        update_group = QGroupBox("数据更新设置")
        update_layout = QGridLayout(update_group)

        # 自动更新
        auto_update_label = QLabel("自动更新数据:")

        auto_update_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        update_layout.addWidget(auto_update_label, 0, 0)
        self.auto_update_check = QCheckBox()
        self.auto_update_check.setChecked(self.current_settings.get('auto_update_data', True))
        update_layout.addWidget(self.auto_update_check, 0, 1)

        # 更新间隔
        update_interval_label = QLabel("更新间隔(分钟):")

        update_interval_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        update_layout.addWidget(update_interval_label, 1, 0)
        self.update_interval_spin = QSpinBox()
        self.update_interval_spin.setRange(1, 1440)
        self.update_interval_spin.setValue(self.current_settings.get('update_interval', 60))
        update_layout.addWidget(self.update_interval_spin, 1, 1)

        # 数据保留天数
        data_retention_label = QLabel("数据保留天数:")

        data_retention_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        update_layout.addWidget(data_retention_label, 2, 0)
        self.data_retention_spin = QSpinBox()
        self.data_retention_spin.setRange(30, 3650)
        self.data_retention_spin.setValue(self.current_settings.get('data_retention_days', 365))
        update_layout.addWidget(self.data_retention_spin, 2, 1)

        layout.addWidget(update_group)
        layout.addStretch()

        return widget

    def create_ui_tab(self):
        """创建界面设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_layout = QGridLayout(theme_group)

        # 主题
        theme_label = QLabel("主题:")

        theme_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        theme_layout.addWidget(theme_label, 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题", "自动"])
        self.theme_combo.setCurrentText(self.current_settings.get('theme', '深色主题'))
        theme_layout.addWidget(self.theme_combo, 0, 1)

        # 字体大小
        font_size_label = QLabel("字体大小:")

        font_size_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        theme_layout.addWidget(font_size_label, 1, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(self.current_settings.get('font_size', 12))
        theme_layout.addWidget(self.font_size_spin, 1, 1)

        # 透明度
        window_opacity_label = QLabel("窗口透明度:")

        window_opacity_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        theme_layout.addWidget(window_opacity_label, 2, 0)
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(50, 100)
        self.opacity_slider.setValue(int(self.current_settings.get('window_opacity', 1.0) * 100))
        theme_layout.addWidget(self.opacity_slider, 2, 1)

        layout.addWidget(theme_group)

        # 显示设置
        display_group = QGroupBox("显示设置")
        display_layout = QGridLayout(display_group)

        # 显示启动画面
        show_splash_label = QLabel("显示启动画面:")

        show_splash_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        display_layout.addWidget(show_splash_label, 0, 0)
        self.show_splash_check = QCheckBox()
        self.show_splash_check.setChecked(self.current_settings.get('show_splash', True))
        display_layout.addWidget(self.show_splash_check, 0, 1)

        # 显示状态栏
        show_statusbar_label = QLabel("显示状态栏:")

        show_statusbar_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        display_layout.addWidget(show_statusbar_label, 1, 0)
        self.show_statusbar_check = QCheckBox()
        self.show_statusbar_check.setChecked(self.current_settings.get('show_statusbar', True))
        display_layout.addWidget(self.show_statusbar_check, 1, 1)

        # 显示工具栏
        show_toolbar_label = QLabel("显示工具栏:")

        show_toolbar_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        display_layout.addWidget(show_toolbar_label, 2, 0)
        self.show_toolbar_check = QCheckBox()
        self.show_toolbar_check.setChecked(self.current_settings.get('show_toolbar', True))
        display_layout.addWidget(self.show_toolbar_check, 2, 1)

        layout.addWidget(display_group)
        layout.addStretch()

        return widget

    def create_risk_tab(self):
        """创建风险设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 风险控制
        risk_group = QGroupBox("风险控制")
        risk_layout = QGridLayout(risk_group)

        # 最大持仓比例
        max_position_label = QLabel("最大持仓比例(%):")

        max_position_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        risk_layout.addWidget(max_position_label, 0, 0)
        self.max_position_spin = QDoubleSpinBox()
        self.max_position_spin.setRange(1.0, 100.0)
        self.max_position_spin.setValue(self.current_settings.get('max_position_ratio', 20.0))
        risk_layout.addWidget(self.max_position_spin, 0, 1)

        # 最大回撤
        max_drawdown_label = QLabel("最大回撤(%):")

        max_drawdown_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        risk_layout.addWidget(max_drawdown_label, 1, 0)
        self.max_drawdown_spin = QDoubleSpinBox()
        self.max_drawdown_spin.setRange(1.0, 50.0)
        self.max_drawdown_spin.setValue(self.current_settings.get('max_drawdown', 10.0))
        risk_layout.addWidget(self.max_drawdown_spin, 1, 1)

        # 止损比例
        stop_loss_label = QLabel("默认止损(%):")

        stop_loss_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        risk_layout.addWidget(stop_loss_label, 2, 0)
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(1.0, 20.0)
        self.stop_loss_spin.setValue(self.current_settings.get('default_stop_loss', 5.0))
        risk_layout.addWidget(self.stop_loss_spin, 2, 1)

        # 止盈比例
        take_profit_label = QLabel("默认止盈(%):")

        take_profit_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        risk_layout.addWidget(take_profit_label, 3, 0)
        self.take_profit_spin = QDoubleSpinBox()
        self.take_profit_spin.setRange(1.0, 50.0)
        self.take_profit_spin.setValue(self.current_settings.get('default_take_profit', 15.0))
        risk_layout.addWidget(self.take_profit_spin, 3, 1)

        layout.addWidget(risk_group)

        # 报警设置
        alert_group = QGroupBox("报警设置")
        alert_layout = QGridLayout(alert_group)

        # 启用声音报警
        sound_alert_label = QLabel("启用声音报警:")

        sound_alert_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        alert_layout.addWidget(sound_alert_label, 0, 0)
        self.sound_alert_check = QCheckBox()
        self.sound_alert_check.setChecked(self.current_settings.get('sound_alert', True))
        alert_layout.addWidget(self.sound_alert_check, 0, 1)

        # 启用邮件报警
        email_alert_label = QLabel("启用邮件报警:")

        email_alert_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        alert_layout.addWidget(email_alert_label, 1, 0)
        self.email_alert_check = QCheckBox()
        self.email_alert_check.setChecked(self.current_settings.get('email_alert', False))
        alert_layout.addWidget(self.email_alert_check, 1, 1)

        # 报警邮箱
        alert_email_label = QLabel("报警邮箱:")

        alert_email_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        alert_layout.addWidget(alert_email_label, 2, 0)
        self.alert_email_edit = QLineEdit()
        self.alert_email_edit.setText(self.current_settings.get('alert_email', ''))
        alert_layout.addWidget(self.alert_email_edit, 2, 1)

        layout.addWidget(alert_group)
        layout.addStretch()

        return widget

    def browse_directory(self, line_edit: QLineEdit):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择目录", line_edit.text()
        )
        if directory:
            line_edit.setText(directory)

    def load_settings(self) -> dict:
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            return {}

    def save_settings(self):
        """保存设置"""
        try:
            # 收集所有设置
            settings = {
                # 基本设置
                'auto_start': self.auto_start_check.isChecked(),
                'minimize_to_tray': self.minimize_to_tray_check.isChecked(),
                'check_update': self.check_update_check.isChecked(),
                'log_level': self.log_level_combo.currentText(),
                'data_dir': self.data_dir_edit.text(),
                'log_dir': self.log_dir_edit.text(),

                # 交易设置
                'default_broker': self.broker_combo.currentText(),
                'api_key': self.api_key_edit.text(),
                'secret_key': self.secret_key_edit.text(),
                'account_id': self.account_id_edit.text(),
                'commission_rate': self.commission_rate_spin.value(),
                'stamp_tax': self.stamp_tax_spin.value(),
                'slippage': self.slippage_spin.value(),

                # 数据设置
                'primary_source': self.primary_source_combo.currentText(),
                'backup_source': self.backup_source_combo.currentText(),
                'realtime_source': self.realtime_source_combo.currentText(),
                'auto_update_data': self.auto_update_check.isChecked(),
                'update_interval': self.update_interval_spin.value(),
                'data_retention_days': self.data_retention_spin.value(),

                # 界面设置
                'theme': self.theme_combo.currentText(),
                'font_size': self.font_size_spin.value(),
                'window_opacity': self.opacity_slider.value() / 100.0,
                'show_splash': self.show_splash_check.isChecked(),
                'show_statusbar': self.show_statusbar_check.isChecked(),
                'show_toolbar': self.show_toolbar_check.isChecked(),

                # 风险设置
                'max_position_ratio': self.max_position_spin.value(),
                'max_drawdown': self.max_drawdown_spin.value(),
                'default_stop_loss': self.stop_loss_spin.value(),
                'default_take_profit': self.take_profit_spin.value(),
                'sound_alert': self.sound_alert_check.isChecked(),
                'email_alert': self.email_alert_check.isChecked(),
                'alert_email': self.alert_email_edit.text(),
            }

            # 确保配置目录存在
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存到文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            self.current_settings = settings
            self.settings_changed.emit(settings)

            QMessageBox.information(self, "成功", "设置已保存")
            self.logger.info("设置已保存")

        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败:\n{str(e)}")

    def reset_settings(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置为默认设置吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 删除设置文件
                if self.settings_file.exists():
                    self.settings_file.unlink()

                # 重新加载默认设置
                self.current_settings = {}

                # 重新初始化界面
                self.init_ui()

                QMessageBox.information(self, "成功", "已重置为默认设置")
                self.logger.info("已重置为默认设置")

            except Exception as e:
                self.logger.error(f"重置设置失败: {e}")
                QMessageBox.critical(self, "错误", f"重置设置失败:\n{str(e)}")

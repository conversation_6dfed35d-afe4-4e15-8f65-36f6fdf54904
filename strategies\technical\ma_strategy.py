#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动平均线策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime

from ..base_strategy import BaseStrategy
from analysis.technical_indicators import TechnicalIndicators


class MovingAverageStrategy(BaseStrategy):
    """移动平均线策略"""

    def __init__(self, name: str = "移动平均线策略", parameters: Dict[str, Any] = None):
        default_params = {
            'fast_period': 5,      # 快线周期
            'slow_period': 20,     # 慢线周期
            'ma_type': 'sma',      # 移动平均类型：sma, ema
            'min_volume': 1000000, # 最小成交量过滤
            'position_size': 0.05  # 单次交易仓位大小
        }

        if parameters:
            default_params.update(parameters)

        super().__init__(name, default_params)
        self.tech_indicators = TechnicalIndicators()

        # 策略状态
        self.last_signal = None
        self.signal_count = 0

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成交易信号"""
        signals = []

        try:
            if len(data) < self.parameters['slow_period']:
                self.logger.warning("数据长度不足，无法计算移动平均线")
                return signals

            # 计算移动平均线
            if self.parameters['ma_type'] == 'sma':
                fast_ma = self.tech_indicators.sma(data['close'], self.parameters['fast_period'])
                slow_ma = self.tech_indicators.sma(data['close'], self.parameters['slow_period'])
            else:  # ema
                fast_ma = self.tech_indicators.ema(data['close'], self.parameters['fast_period'])
                slow_ma = self.tech_indicators.ema(data['close'], self.parameters['slow_period'])

            # 计算信号
            for i in range(1, len(data)):
                current_date = data.index[i]
                current_price = data['close'].iloc[i]
                current_volume = data['volume'].iloc[i]

                # 成交量过滤
                if current_volume < self.parameters['min_volume']:
                    continue

                # 获取当前和前一个移动平均值
                fast_current = fast_ma.iloc[i]
                fast_prev = fast_ma.iloc[i-1]
                slow_current = slow_ma.iloc[i]
                slow_prev = slow_ma.iloc[i-1]

                # 跳过NaN值
                if pd.isna(fast_current) or pd.isna(slow_current) or pd.isna(fast_prev) or pd.isna(slow_prev):
                    continue

                signal = None

                # 金叉信号（快线上穿慢线）
                if fast_prev <= slow_prev and fast_current > slow_current:
                    signal = {
                        'symbol': data.get('symbol', ['UNKNOWN'])[0] if 'symbol' in data.columns else 'UNKNOWN',
                        'direction': 'buy',
                        'price': current_price,
                        'timestamp': current_date,
                        'signal_type': 'golden_cross',
                        'fast_ma': fast_current,
                        'slow_ma': slow_current,
                        'volume': current_volume,
                        'confidence': self._calculate_confidence(data.iloc[:i+1], fast_ma.iloc[:i+1], slow_ma.iloc[:i+1])
                    }

                # 死叉信号（快线下穿慢线）
                elif fast_prev >= slow_prev and fast_current < slow_current:
                    signal = {
                        'symbol': data.get('symbol', ['UNKNOWN'])[0] if 'symbol' in data.columns else 'UNKNOWN',
                        'direction': 'sell',
                        'price': current_price,
                        'timestamp': current_date,
                        'signal_type': 'death_cross',
                        'fast_ma': fast_current,
                        'slow_ma': slow_current,
                        'volume': current_volume,
                        'confidence': self._calculate_confidence(data.iloc[:i+1], fast_ma.iloc[:i+1], slow_ma.iloc[:i+1])
                    }

                if signal:
                    # 验证信号
                    if self.validate_signal(signal):
                        # 计算仓位大小
                        signal['quantity'] = self.calculate_position_size(
                            signal, current_price, 1000000  # 假设100万资金
                        )

                        # 应用风险管理
                        signal = self.apply_risk_management(signal)

                        signals.append(signal)
                        self.save_signal(signal)
                        self.signal_count += 1

                        self.logger.info(f"生成{signal['signal_type']}信号: {signal['direction']} {signal['symbol']} @ {signal['price']:.2f}")

            return signals

        except Exception as e:
            self.logger.error(f"生成信号失败: {e}")
            return signals

    def calculate_position_size(self, signal: Dict[str, Any],
                              current_price: float,
                              available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于固定仓位比例计算
            position_value = available_capital * self.parameters['position_size']
            quantity = int(position_value / current_price / 100) * 100  # 按手数取整

            # 根据信号置信度调整仓位
            confidence = signal.get('confidence', 0.5)
            quantity = int(quantity * confidence)

            return max(100, quantity)  # 最小1手

        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 100

    def _calculate_confidence(self, data: pd.DataFrame, fast_ma: pd.Series, slow_ma: pd.Series) -> float:
        """计算信号置信度"""
        try:
            # 基于多个因素计算置信度
            confidence = 0.5  # 基础置信度

            # 因子1: 移动平均线的分离度
            ma_separation = abs(fast_ma.iloc[-1] - slow_ma.iloc[-1]) / slow_ma.iloc[-1]
            confidence += min(0.2, ma_separation * 10)  # 最多增加0.2

            # 因子2: 价格相对于移动平均线的位置
            current_price = data['close'].iloc[-1]
            price_position = (current_price - slow_ma.iloc[-1]) / slow_ma.iloc[-1]
            if abs(price_position) < 0.02:  # 价格接近移动平均线
                confidence += 0.1

            # 因子3: 成交量确认
            if len(data) >= 5:
                avg_volume = data['volume'].iloc[-5:].mean()
                current_volume = data['volume'].iloc[-1]
                if current_volume > avg_volume * 1.2:  # 成交量放大
                    confidence += 0.1

            # 因子4: 趋势强度
            if len(data) >= 10:
                price_trend = (data['close'].iloc[-1] - data['close'].iloc[-10]) / data['close'].iloc[-10]
                if abs(price_trend) > 0.05:  # 趋势明显
                    confidence += 0.1

            return min(1.0, max(0.1, confidence))

        except Exception as e:
            self.logger.error(f"计算置信度失败: {e}")
            return 0.5

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': '技术分析策略',
            'description': f'基于{self.parameters["fast_period"]}日和{self.parameters["slow_period"]}日移动平均线的金叉死叉策略',
            'parameters': self.parameters,
            'signal_count': self.signal_count,
            'last_signal': self.last_signal,
            '适用市场': '股票、期货、外汇',
            '策略逻辑': [
                '快线上穿慢线时产生买入信号（金叉）',
                '快线下穿慢线时产生卖出信号（死叉）',
                '结合成交量、趋势强度等因素计算信号置信度',
                '根据置信度调整仓位大小'
            ]
        }


class DoubleMovingAverageStrategy(BaseStrategy):
    """双移动平均线策略（改进版）"""

    def __init__(self, name: str = "双移动平均线策略", parameters: Dict[str, Any] = None):
        default_params = {
            'fast_period': 10,     # 快线周期
            'slow_period': 30,     # 慢线周期
            'signal_period': 9,    # 信号线周期
            'volume_factor': 1.5,  # 成交量确认因子
            'trend_filter': True,  # 是否启用趋势过滤
            'position_size': 0.08  # 单次交易仓位大小
        }

        if parameters:
            default_params.update(parameters)

        super().__init__(name, default_params)
        self.tech_indicators = TechnicalIndicators()

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成交易信号"""
        signals = []

        try:
            if len(data) < max(self.parameters['slow_period'], self.parameters['signal_period']):
                return signals

            # 计算移动平均线
            fast_ma = self.tech_indicators.ema(data['close'], self.parameters['fast_period'])
            slow_ma = self.tech_indicators.ema(data['close'], self.parameters['slow_period'])
            signal_ma = self.tech_indicators.ema(data['close'], self.parameters['signal_period'])

            # 计算MACD作为趋势过滤器
            macd, macd_signal, macd_hist = self.tech_indicators.macd(data['close'])

            for i in range(1, len(data)):
                current_date = data.index[i]
                current_price = data['close'].iloc[i]

                # 获取移动平均值
                fast_current = fast_ma.iloc[i]
                slow_current = slow_ma.iloc[i]
                signal_current = signal_ma.iloc[i]

                fast_prev = fast_ma.iloc[i-1]
                slow_prev = slow_ma.iloc[i-1]

                # 跳过NaN值
                if any(pd.isna(x) for x in [fast_current, slow_current, signal_current, fast_prev, slow_prev]):
                    continue

                signal = None

                # 多重确认的买入信号
                if (fast_prev <= slow_prev and fast_current > slow_current and  # 金叉
                    current_price > signal_current and  # 价格在信号线上方
                    self._volume_confirmation(data, i) and  # 成交量确认
                    self._trend_confirmation(macd_hist, i)):  # 趋势确认

                    signal = {
                        'symbol': data.get('symbol', ['UNKNOWN'])[0] if 'symbol' in data.columns else 'UNKNOWN',
                        'direction': 'buy',
                        'price': current_price,
                        'timestamp': current_date,
                        'signal_type': 'enhanced_golden_cross',
                        'fast_ma': fast_current,
                        'slow_ma': slow_current,
                        'signal_ma': signal_current,
                        'confidence': self._calculate_enhanced_confidence(data.iloc[:i+1], fast_ma.iloc[:i+1], slow_ma.iloc[:i+1])
                    }

                # 多重确认的卖出信号
                elif (fast_prev >= slow_prev and fast_current < slow_current and  # 死叉
                      current_price < signal_current and  # 价格在信号线下方
                      self._volume_confirmation(data, i) and  # 成交量确认
                      not self._trend_confirmation(macd_hist, i)):  # 趋势转弱

                    signal = {
                        'symbol': data.get('symbol', ['UNKNOWN'])[0] if 'symbol' in data.columns else 'UNKNOWN',
                        'direction': 'sell',
                        'price': current_price,
                        'timestamp': current_date,
                        'signal_type': 'enhanced_death_cross',
                        'fast_ma': fast_current,
                        'slow_ma': slow_current,
                        'signal_ma': signal_current,
                        'confidence': self._calculate_enhanced_confidence(data.iloc[:i+1], fast_ma.iloc[:i+1], slow_ma.iloc[:i+1])
                    }

                if signal and self.validate_signal(signal):
                    signal['quantity'] = self.calculate_position_size(signal, current_price, 1000000)
                    signal = self.apply_risk_management(signal)
                    signals.append(signal)
                    self.save_signal(signal)

            return signals

        except Exception as e:
            self.logger.error(f"生成增强信号失败: {e}")
            return signals

    def _volume_confirmation(self, data: pd.DataFrame, index: int) -> bool:
        """成交量确认"""
        if index < 5:
            return True

        current_volume = data['volume'].iloc[index]
        avg_volume = data['volume'].iloc[index-5:index].mean()

        return current_volume > avg_volume * self.parameters['volume_factor']

    def _trend_confirmation(self, macd_hist: pd.Series, index: int) -> bool:
        """趋势确认"""
        if not self.parameters['trend_filter'] or pd.isna(macd_hist.iloc[index]):
            return True

        return macd_hist.iloc[index] > 0

    def _calculate_enhanced_confidence(self, data: pd.DataFrame, fast_ma: pd.Series, slow_ma: pd.Series) -> float:
        """计算增强置信度"""
        confidence = 0.3  # 基础置信度

        try:
            # 移动平均线分离度
            ma_separation = abs(fast_ma.iloc[-1] - slow_ma.iloc[-1]) / slow_ma.iloc[-1]
            confidence += min(0.25, ma_separation * 15)

            # 价格动量
            if len(data) >= 3:
                price_momentum = (data['close'].iloc[-1] - data['close'].iloc[-3]) / data['close'].iloc[-3]
                confidence += min(0.2, abs(price_momentum) * 5)

            # 成交量动量
            if len(data) >= 5:
                volume_ratio = data['volume'].iloc[-1] / data['volume'].iloc[-5:].mean()
                if volume_ratio > 1.5:
                    confidence += 0.15

            # 趋势一致性
            if len(data) >= 10:
                trend_consistency = sum([
                    1 if data['close'].iloc[i] > data['close'].iloc[i-1] else -1
                    for i in range(-5, 0)
                ]) / 5
                confidence += min(0.1, abs(trend_consistency) * 0.1)

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def calculate_position_size(self, signal: Dict[str, Any],
                              current_price: float,
                              available_capital: float) -> int:
        """计算仓位大小"""
        try:
            base_position = available_capital * self.parameters['position_size']
            confidence = signal.get('confidence', 0.5)

            # 根据置信度调整仓位
            adjusted_position = base_position * (0.5 + confidence)
            quantity = int(adjusted_position / current_price / 100) * 100

            return max(100, quantity)

        except Exception as e:
            self.logger.error(f"计算仓位失败: {e}")
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': '双移动平均线技术策略',
            'indicators': ['EMA', 'MACD', 'Volume'],
            'signal_conditions': {
                'buy': '快线上穿慢线且多重确认',
                'sell': '快线下穿慢线且多重确认'
            },
            'risk_management': {
                'position_size': f"{self.parameters['position_size']*100:.1f}%"
            }
        })
        return info

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("1. 测试基础导入...")
try:
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
    print("   ✅ PyQt5基础组件导入成功")
except Exception as e:
    print(f"   ❌ PyQt5导入失败: {e}")
    exit(1)

print("2. 测试工具模块...")
try:
    from utils.logger import get_logger
    print("   ✅ logger模块导入成功")
except Exception as e:
    print(f"   ❌ logger模块导入失败: {e}")
    exit(1)

print("3. 测试分析模块...")
try:
    from analysis.technical_indicators import TechnicalIndicators
    print("   ✅ 技术指标模块导入成功")
except Exception as e:
    print(f"   ❌ 技术指标模块导入失败: {e}")

try:
    from analysis.fundamental_analysis import FundamentalAnalysis
    print("   ✅ 基本面分析模块导入成功")
except Exception as e:
    print(f"   ❌ 基本面分析模块导入失败: {e}")

try:
    from analysis.quantitative_analysis import QuantitativeAnalysis
    print("   ✅ 量化分析模块导入成功")
except Exception as e:
    print(f"   ❌ 量化分析模块导入失败: {e}")

print("4. 测试基础组件...")
try:
    from gui.widgets.base_widget import BaseWidget
    print("   ✅ 基础组件导入成功")
except Exception as e:
    print(f"   ❌ 基础组件导入失败: {e}")

print("5. 测试技术图表组件...")
try:
    from gui.widgets.technical_chart_widget import TechnicalChartWidget
    print("   ✅ 技术图表组件导入成功")
except Exception as e:
    print(f"   ❌ 技术图表组件导入失败: {e}")

print("6. 测试分析中心组件...")
try:
    # 直接导入，不创建实例
    import gui.widgets.analysis_center_widget
    print("   ✅ 分析中心模块导入成功")
except Exception as e:
    print(f"   ❌ 分析中心模块导入失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")

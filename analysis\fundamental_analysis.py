#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本面分析模块
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import requests
import json
from datetime import datetime, timedelta
import warnings

from utils.logger import get_logger

warnings.filterwarnings('ignore')


class FundamentalAnalysis:
    """基本面分析类"""
    
    def __init__(self):
        self.logger = get_logger("FundamentalAnalysis")
    
    def calculate_financial_ratios(self, financial_data: Dict) -> Dict:
        """计算财务比率"""
        try:
            ratios = {}
            
            # 获取财务数据
            revenue = financial_data.get('revenue', 0)
            net_income = financial_data.get('net_income', 0)
            total_assets = financial_data.get('total_assets', 0)
            total_equity = financial_data.get('total_equity', 0)
            total_liabilities = financial_data.get('total_liabilities', 0)
            current_assets = financial_data.get('current_assets', 0)
            current_liabilities = financial_data.get('current_liabilities', 0)
            shares_outstanding = financial_data.get('shares_outstanding', 1)
            market_cap = financial_data.get('market_cap', 0)
            stock_price = financial_data.get('stock_price', 0)
            
            # 盈利能力指标
            if revenue > 0:
                ratios['净利润率'] = (net_income / revenue) * 100
                ratios['毛利率'] = financial_data.get('gross_profit', 0) / revenue * 100
            else:
                ratios['净利润率'] = 0
                ratios['毛利率'] = 0
            
            if total_equity > 0:
                ratios['净资产收益率(ROE)'] = (net_income / total_equity) * 100
            else:
                ratios['净资产收益率(ROE)'] = 0
            
            if total_assets > 0:
                ratios['总资产收益率(ROA)'] = (net_income / total_assets) * 100
            else:
                ratios['总资产收益率(ROA)'] = 0
            
            # 偿债能力指标
            if total_assets > 0:
                ratios['资产负债率'] = (total_liabilities / total_assets) * 100
            else:
                ratios['资产负债率'] = 0
            
            if current_liabilities > 0:
                ratios['流动比率'] = current_assets / current_liabilities
            else:
                ratios['流动比率'] = 0
            
            if total_equity > 0:
                ratios['权益乘数'] = total_assets / total_equity
            else:
                ratios['权益乘数'] = 0
            
            # 估值指标
            if shares_outstanding > 0:
                ratios['每股收益(EPS)'] = net_income / shares_outstanding
                ratios['每股净资产'] = total_equity / shares_outstanding
            else:
                ratios['每股收益(EPS)'] = 0
                ratios['每股净资产'] = 0
            
            if ratios['每股收益(EPS)'] > 0 and stock_price > 0:
                ratios['市盈率(PE)'] = stock_price / ratios['每股收益(EPS)']
            else:
                ratios['市盈率(PE)'] = 0
            
            if ratios['每股净资产'] > 0 and stock_price > 0:
                ratios['市净率(PB)'] = stock_price / ratios['每股净资产']
            else:
                ratios['市净率(PB)'] = 0
            
            # 营运能力指标
            if total_assets > 0 and revenue > 0:
                ratios['总资产周转率'] = revenue / total_assets
            else:
                ratios['总资产周转率'] = 0
            
            if total_equity > 0 and revenue > 0:
                ratios['权益周转率'] = revenue / total_equity
            else:
                ratios['权益周转率'] = 0
            
            # 成长性指标（需要历史数据）
            ratios['营收增长率'] = financial_data.get('revenue_growth', 0)
            ratios['净利润增长率'] = financial_data.get('net_income_growth', 0)
            ratios['总资产增长率'] = financial_data.get('assets_growth', 0)
            
            self.logger.info(f"财务比率计算完成，共 {len(ratios)} 个指标")
            return ratios
            
        except Exception as e:
            self.logger.error(f"计算财务比率失败: {e}")
            return {}
    
    def evaluate_financial_health(self, ratios: Dict) -> Dict:
        """评估财务健康状况"""
        try:
            evaluation = {}
            
            # 盈利能力评估
            roe = ratios.get('净资产收益率(ROE)', 0)
            if roe >= 15:
                evaluation['盈利能力'] = '优秀'
            elif roe >= 10:
                evaluation['盈利能力'] = '良好'
            elif roe >= 5:
                evaluation['盈利能力'] = '一般'
            else:
                evaluation['盈利能力'] = '较差'
            
            # 偿债能力评估
            debt_ratio = ratios.get('资产负债率', 0)
            current_ratio = ratios.get('流动比率', 0)
            
            if debt_ratio <= 40 and current_ratio >= 2:
                evaluation['偿债能力'] = '优秀'
            elif debt_ratio <= 60 and current_ratio >= 1.5:
                evaluation['偿债能力'] = '良好'
            elif debt_ratio <= 80 and current_ratio >= 1:
                evaluation['偿债能力'] = '一般'
            else:
                evaluation['偿债能力'] = '较差'
            
            # 估值水平评估
            pe = ratios.get('市盈率(PE)', 0)
            pb = ratios.get('市净率(PB)', 0)
            
            if 0 < pe <= 15 and 0 < pb <= 2:
                evaluation['估值水平'] = '低估'
            elif 15 < pe <= 25 and 2 < pb <= 3:
                evaluation['估值水平'] = '合理'
            elif 25 < pe <= 40 and 3 < pb <= 5:
                evaluation['估值水平'] = '偏高'
            else:
                evaluation['估值水平'] = '高估'
            
            # 成长性评估
            revenue_growth = ratios.get('营收增长率', 0)
            profit_growth = ratios.get('净利润增长率', 0)
            
            avg_growth = (revenue_growth + profit_growth) / 2
            if avg_growth >= 20:
                evaluation['成长性'] = '优秀'
            elif avg_growth >= 10:
                evaluation['成长性'] = '良好'
            elif avg_growth >= 0:
                evaluation['成长性'] = '一般'
            else:
                evaluation['成长性'] = '较差'
            
            # 综合评分
            scores = {
                '优秀': 4, '良好': 3, '一般': 2, '较差': 1,
                '低估': 4, '合理': 3, '偏高': 2, '高估': 1
            }
            
            total_score = sum(scores.get(score, 2) for score in evaluation.values())
            max_score = len(evaluation) * 4
            
            overall_score = (total_score / max_score) * 100
            
            if overall_score >= 80:
                evaluation['综合评级'] = 'A+'
            elif overall_score >= 70:
                evaluation['综合评级'] = 'A'
            elif overall_score >= 60:
                evaluation['综合评级'] = 'B+'
            elif overall_score >= 50:
                evaluation['综合评级'] = 'B'
            elif overall_score >= 40:
                evaluation['综合评级'] = 'C+'
            else:
                evaluation['综合评级'] = 'C'
            
            evaluation['综合得分'] = f"{overall_score:.1f}"
            
            self.logger.info(f"财务健康评估完成，综合评级: {evaluation['综合评级']}")
            return evaluation
            
        except Exception as e:
            self.logger.error(f"财务健康评估失败: {e}")
            return {}
    
    def generate_sample_financial_data(self, symbol: str) -> Dict:
        """生成示例财务数据"""
        try:
            # 根据股票代码生成不同的示例数据
            np.random.seed(hash(symbol) % 2**32)
            
            # 基础财务数据（单位：万元）
            base_revenue = np.random.uniform(100000, 1000000)
            
            financial_data = {
                # 损益表数据
                'revenue': base_revenue,  # 营业收入
                'gross_profit': base_revenue * np.random.uniform(0.2, 0.6),  # 毛利润
                'net_income': base_revenue * np.random.uniform(0.05, 0.25),  # 净利润
                'operating_income': base_revenue * np.random.uniform(0.08, 0.3),  # 营业利润
                
                # 资产负债表数据
                'total_assets': base_revenue * np.random.uniform(1.5, 4.0),  # 总资产
                'current_assets': base_revenue * np.random.uniform(0.5, 1.5),  # 流动资产
                'total_liabilities': base_revenue * np.random.uniform(0.8, 2.5),  # 总负债
                'current_liabilities': base_revenue * np.random.uniform(0.3, 1.0),  # 流动负债
                'total_equity': base_revenue * np.random.uniform(0.7, 1.5),  # 股东权益
                
                # 现金流量表数据
                'operating_cash_flow': base_revenue * np.random.uniform(0.05, 0.2),  # 经营现金流
                'investing_cash_flow': base_revenue * np.random.uniform(-0.1, 0.05),  # 投资现金流
                'financing_cash_flow': base_revenue * np.random.uniform(-0.05, 0.1),  # 筹资现金流
                
                # 市场数据
                'shares_outstanding': np.random.uniform(10000, 100000),  # 流通股本（万股）
                'stock_price': np.random.uniform(10, 100),  # 股价
                'market_cap': 0,  # 市值（将计算得出）
                
                # 增长率数据
                'revenue_growth': np.random.uniform(-10, 30),  # 营收增长率(%)
                'net_income_growth': np.random.uniform(-20, 50),  # 净利润增长率(%)
                'assets_growth': np.random.uniform(-5, 25),  # 总资产增长率(%)
            }
            
            # 计算市值
            financial_data['market_cap'] = (
                financial_data['shares_outstanding'] * financial_data['stock_price']
            )
            
            # 确保数据的逻辑一致性
            financial_data['total_equity'] = (
                financial_data['total_assets'] - financial_data['total_liabilities']
            )
            
            self.logger.info(f"生成示例财务数据: {symbol}")
            return financial_data
            
        except Exception as e:
            self.logger.error(f"生成示例财务数据失败: {e}")
            return {}
    
    def get_industry_averages(self, industry: str) -> Dict:
        """获取行业平均值"""
        try:
            # 不同行业的典型财务指标平均值
            industry_data = {
                '银行': {
                    '净资产收益率(ROE)': 12.5,
                    '资产负债率': 85.0,
                    '市盈率(PE)': 6.5,
                    '市净率(PB)': 0.8,
                    '净利润率': 25.0
                },
                '房地产': {
                    '净资产收益率(ROE)': 8.5,
                    '资产负债率': 75.0,
                    '市盈率(PE)': 8.5,
                    '市净率(PB)': 1.2,
                    '净利润率': 12.0
                },
                '制造业': {
                    '净资产收益率(ROE)': 10.2,
                    '资产负债率': 55.0,
                    '市盈率(PE)': 18.5,
                    '市净率(PB)': 2.1,
                    '净利润率': 8.5
                },
                '科技': {
                    '净资产收益率(ROE)': 15.8,
                    '资产负债率': 35.0,
                    '市盈率(PE)': 28.5,
                    '市净率(PB)': 3.5,
                    '净利润率': 18.0
                },
                '消费': {
                    '净资产收益率(ROE)': 12.8,
                    '资产负债率': 45.0,
                    '市盈率(PE)': 22.0,
                    '市净率(PB)': 2.8,
                    '净利润率': 15.0
                }
            }
            
            # 默认使用制造业平均值
            return industry_data.get(industry, industry_data['制造业'])
            
        except Exception as e:
            self.logger.error(f"获取行业平均值失败: {e}")
            return {}
    
    def compare_with_industry(self, ratios: Dict, industry: str = "制造业") -> Dict:
        """与行业平均值比较"""
        try:
            industry_avg = self.get_industry_averages(industry)
            comparison = {}
            
            for metric, company_value in ratios.items():
                if metric in industry_avg:
                    industry_value = industry_avg[metric]
                    
                    if industry_value > 0:
                        diff_pct = ((company_value - industry_value) / industry_value) * 100
                        
                        if diff_pct > 20:
                            comparison[metric] = '远超行业'
                        elif diff_pct > 5:
                            comparison[metric] = '超过行业'
                        elif diff_pct > -5:
                            comparison[metric] = '接近行业'
                        elif diff_pct > -20:
                            comparison[metric] = '低于行业'
                        else:
                            comparison[metric] = '远低行业'
                    else:
                        comparison[metric] = '无法比较'
            
            self.logger.info(f"行业比较完成: {industry}")
            return comparison
            
        except Exception as e:
            self.logger.error(f"行业比较失败: {e}")
            return {}
    
    def analyze_stock(self, symbol: str, industry: str = "制造业") -> Dict:
        """综合分析股票基本面"""
        try:
            # 获取财务数据
            financial_data = self.generate_sample_financial_data(symbol)
            
            # 计算财务比率
            ratios = self.calculate_financial_ratios(financial_data)
            
            # 评估财务健康状况
            evaluation = self.evaluate_financial_health(ratios)
            
            # 行业比较
            industry_comparison = self.compare_with_industry(ratios, industry)
            
            # 组合结果
            analysis_result = {
                'symbol': symbol,
                'industry': industry,
                'financial_data': financial_data,
                'ratios': ratios,
                'evaluation': evaluation,
                'industry_comparison': industry_comparison,
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.logger.info(f"基本面分析完成: {symbol}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"基本面分析失败: {e}")
            return {}

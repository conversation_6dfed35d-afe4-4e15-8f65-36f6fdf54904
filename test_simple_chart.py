#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单图表测试
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis.technical_indicators import TechnicalIndicators
from utils.logger import get_logger

def test_chart_data():
    """测试图表数据生成"""
    logger = get_logger("TestChart")
    
    try:
        print("=" * 50)
        print("技术分析图表数据测试")
        print("=" * 50)
        
        # 生成示例数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        base_price = 50
        prices = [base_price]
        
        for i in range(1, 100):
            change = np.random.normal(0.001, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))
        
        # 创建OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close if i == 0 else prices[i-1] * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
            volume = np.random.randint(1000000, 10000000)
            
            data.append({
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)
        
        print(f"✅ 生成数据成功: {len(df)} 条记录")
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        # 计算技术指标
        ti = TechnicalIndicators()
        indicators_data = ti.calculate_all_indicators(df)
        
        print(f"✅ 技术指标计算成功: {len(indicators_data.columns)} 列")
        
        # 显示最新的技术指标值
        latest = indicators_data.iloc[-1]
        
        print("\n主要技术指标最新值:")
        indicators_to_show = [
            'close', 'sma_5', 'sma_20', 'rsi_12', 'macd', 
            'kdj_k', 'kdj_d', 'bb_upper', 'bb_lower', 'cci'
        ]
        
        for indicator in indicators_to_show:
            if indicator in latest.index:
                value = latest[indicator]
                if pd.notna(value):
                    print(f"  {indicator}: {value:.4f}")
        
        # 测试信号生成
        print("\n交易信号测试:")
        
        # RSI信号
        rsi_value = latest['rsi_12']
        if rsi_value > 70:
            rsi_signal = "卖出"
        elif rsi_value < 30:
            rsi_signal = "买入"
        else:
            rsi_signal = "中性"
        print(f"  RSI({rsi_value:.2f}): {rsi_signal}")
        
        # KDJ信号
        kdj_k = latest['kdj_k']
        if kdj_k > 80:
            kdj_signal = "卖出"
        elif kdj_k < 20:
            kdj_signal = "买入"
        else:
            kdj_signal = "中性"
        print(f"  KDJ_K({kdj_k:.2f}): {kdj_signal}")
        
        # MACD信号
        macd_hist = latest['macd_histogram']
        if macd_hist > 0:
            macd_signal = "买入"
        elif macd_hist < 0:
            macd_signal = "卖出"
        else:
            macd_signal = "中性"
        print(f"  MACD_Hist({macd_hist:.4f}): {macd_signal}")
        
        print("\n✅ 技术分析数据测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matplotlib():
    """测试matplotlib功能"""
    try:
        print("\n" + "=" * 50)
        print("Matplotlib功能测试")
        print("=" * 50)
        
        import matplotlib
        matplotlib.use('Agg')  # 使用非GUI后端
        import matplotlib.pyplot as plt
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建简单图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 生成示例数据
        x = np.linspace(0, 10, 100)
        y1 = np.sin(x)
        y2 = np.cos(x)
        
        ax.plot(x, y1, label='sin(x)', color='red')
        ax.plot(x, y2, label='cos(x)', color='blue')
        ax.set_title('技术指标示例图表')
        ax.set_xlabel('时间')
        ax.set_ylabel('数值')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图表
        output_file = project_root / "test_chart.png"
        fig.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print(f"✅ 图表生成成功: {output_file}")
        print("✅ Matplotlib功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ Matplotlib测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始技术分析功能测试...")
    
    success = True
    
    # 测试数据生成和指标计算
    if not test_chart_data():
        success = False
    
    # 测试matplotlib
    if not test_matplotlib():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！技术分析功能正常工作")
        print("\n下一步可以:")
        print("1. 运行完整的GUI测试")
        print("2. 测试实时数据接口")
        print("3. 完善基本面分析功能")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统状态（非GUI）
"""

import sys
from pathlib import Path
import time
import psutil
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_gui_support():
    """检查Python GUI支持"""
    print("🔍 检查Python GUI支持...")
    
    try:
        import tkinter
        print("  ✅ tkinter 可用")
    except ImportError:
        print("  ❌ tkinter 不可用")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("  ✅ PyQt5 可用")
        
        # 测试是否可以创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ QApplication 可以创建")
        
        return True
    except Exception as e:
        print(f"  ❌ PyQt5 问题: {e}")
        return False

def check_display_environment():
    """检查显示环境"""
    print("\n🖥️ 检查显示环境...")
    
    # 检查环境变量
    display = os.environ.get('DISPLAY')
    if display:
        print(f"  ✅ DISPLAY 环境变量: {display}")
    else:
        print("  ⚠️ DISPLAY 环境变量未设置（Windows系统正常）")
    
    # 检查是否在远程会话中
    if os.environ.get('SSH_CLIENT') or os.environ.get('SSH_TTY'):
        print("  ⚠️ 检测到SSH远程会话，GUI可能无法显示")
        return False
    
    # 检查Windows桌面会话
    if sys.platform == 'win32':
        try:
            import win32gui
            desktop = win32gui.GetDesktopWindow()
            if desktop:
                print("  ✅ Windows桌面会话正常")
                return True
        except ImportError:
            print("  ⚠️ 无法检查Windows桌面会话（缺少win32gui）")
    
    return True

def check_running_processes():
    """检查运行中的相关进程"""
    print("\n🔄 检查运行中的相关进程...")
    
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('main.py' in arg or 'test_gui' in arg for arg in cmdline):
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': ' '.join(cmdline) if cmdline else 'N/A'
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if python_processes:
        print(f"  ✅ 发现 {len(python_processes)} 个相关Python进程:")
        for proc in python_processes:
            print(f"    PID {proc['pid']}: {proc['cmdline']}")
    else:
        print("  ℹ️ 未发现相关Python进程")
    
    return python_processes

def check_log_files():
    """检查日志文件"""
    print("\n📋 检查日志文件...")
    
    log_dir = project_root / "logs"
    if not log_dir.exists():
        print("  ❌ 日志目录不存在")
        return False
    
    log_files = list(log_dir.glob("*.log"))
    if not log_files:
        print("  ❌ 未找到日志文件")
        return False
    
    print(f"  ✅ 找到 {len(log_files)} 个日志文件:")
    for log_file in log_files:
        size = log_file.stat().st_size
        mtime = time.ctime(log_file.stat().st_mtime)
        print(f"    {log_file.name}: {size} bytes, 修改时间: {mtime}")
    
    # 检查最新的日志条目
    main_log = log_dir / "trading_system.log"
    if main_log.exists():
        try:
            with open(main_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    print(f"  📝 最新日志: {last_line}")
                    
                    # 检查是否有最近的活动
                    recent_lines = lines[-10:]
                    recent_activity = any('DEBUG' in line or 'INFO' in line for line in recent_lines)
                    if recent_activity:
                        print("  ✅ 检测到最近的系统活动")
                        return True
                    else:
                        print("  ⚠️ 未检测到最近的系统活动")
        except Exception as e:
            print(f"  ❌ 读取日志文件失败: {e}")
    
    return False

def test_simple_gui():
    """测试简单GUI"""
    print("\n🧪 测试简单GUI...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建一个简单的消息框
        msg = QMessageBox()
        msg.setWindowTitle("GUI测试")
        msg.setText("如果您能看到这个对话框，说明GUI正常工作！")
        msg.setStandardButtons(QMessageBox.Ok)
        
        # 设置为非模态，这样不会阻塞
        msg.setModal(False)
        msg.show()
        
        # 处理事件
        app.processEvents()
        
        print("  ✅ 简单GUI测试完成")
        print("  📝 如果看到对话框，说明GUI正常")
        
        # 等待一下然后关闭
        time.sleep(2)
        msg.close()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 简单GUI测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("量化交易系统 - 系统状态检查")
    print("=" * 60)
    
    # 检查各项系统状态
    gui_support = check_python_gui_support()
    display_ok = check_display_environment()
    processes = check_running_processes()
    logs_ok = check_log_files()
    
    print("\n" + "=" * 60)
    print("系统状态总结")
    print("=" * 60)
    
    print(f"GUI支持: {'✅ 正常' if gui_support else '❌ 异常'}")
    print(f"显示环境: {'✅ 正常' if display_ok else '❌ 异常'}")
    print(f"运行进程: {'✅ 有进程运行' if processes else 'ℹ️ 无相关进程'}")
    print(f"日志文件: {'✅ 正常' if logs_ok else '❌ 异常'}")
    
    if gui_support and display_ok:
        print("\n🧪 尝试简单GUI测试...")
        test_simple_gui()
    
    print("\n💡 建议:")
    if not gui_support:
        print("  - 检查PyQt5安装: pip install PyQt5")
    if not display_ok:
        print("  - 检查是否在图形界面环境中运行")
        print("  - 如果是远程连接，尝试启用X11转发")
    if not processes and logs_ok:
        print("  - 系统可能在后台运行，检查任务管理器")
        print("  - 尝试重新启动程序")
    
    print("\n🚀 如果一切正常，尝试运行: python main.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)

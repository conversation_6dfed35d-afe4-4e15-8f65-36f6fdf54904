#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据中心组件
"""

import sys
from pathlib import Path
# from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, # QFrame, QScrollArea, QSplitter,
    QGroupBox, QTableWidget, QTableWidgetItem, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QThread
from PyQt5.QtGui import QFont  # QPalette, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from data.collectors.akshare_collector import AKShareCollector
from gui.widgets.data_dashboard_widget import DataDashboardWidget
from gui.widgets.enhanced_download_widget import EnhancedDownloadWidget
from gui.widgets.enhanced_data_view_widget import EnhancedDataViewWidget
from gui.widgets.data_source_manager_widget import DataSourceManagerWidget
from gui.widgets.realtime_monitor_widget import RealtimeMonitorWidget


class DataDownloadThread(QThread):
    """数据下载线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)

    def __init__(self, symbol, start_date, end_date):
        super().__init__()
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.collector = AKShareCollector()

    def run(self):
        """运行数据下载"""
        try:
            self.status_updated.emit(f"正在下载 {self.symbol} 数据...")
            self.progress_updated.emit(20)

            # 下载股票数据
            data = self.collector.get_stock_data(
                self.symbol,
                self.start_date,
                self.end_date
            )

            self.progress_updated.emit(80)

            if data is not None and not data.empty:
                self.status_updated.emit(f"数据下载完成，共 {len(data)} 条记录")
                self.progress_updated.emit(100)
                self.finished_signal.emit(True, f"成功下载 {len(data)} 条数据")
            else:
                self.finished_signal.emit(False, "数据下载失败或无数据")

        except Exception as e:
            self.finished_signal.emit(False, f"下载失败: {str(e)}")


class DataCenterWidget(QWidget):
    """数据中心组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataCenterWidget")
        self.init_ui()
        self.logger.info("数据中心组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_label = QLabel("数据中心")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 数据概览仪表板标签页
        self.dashboard_widget = DataDashboardWidget()
        self.tab_widget.addTab(self.dashboard_widget, "数据概览")

        # 数据下载标签页（增强版）
        self.enhanced_download_widget = EnhancedDownloadWidget()
        self.tab_widget.addTab(self.enhanced_download_widget, "数据下载")

        # 数据查看标签页（增强版）
        self.enhanced_view_widget = EnhancedDataViewWidget()
        self.tab_widget.addTab(self.enhanced_view_widget, "数据查看")

        # 实时监控标签页
        self.realtime_monitor_widget = RealtimeMonitorWidget()
        self.tab_widget.addTab(self.realtime_monitor_widget, "实时监控")

        # 数据源管理标签页
        self.data_source_manager_widget = DataSourceManagerWidget()
        self.tab_widget.addTab(self.data_source_manager_widget, "数据源管理")

        # 数据管理标签页
        self.tab_widget.addTab(self.create_manage_tab(), "数据管理")

        layout.addWidget(self.tab_widget)

    def create_download_tab(self):
        """创建数据下载标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 下载参数设置
        params_group = QGroupBox("下载参数")
        params_layout = QGridLayout(params_group)

        # 股票代码
        股票代码_label = QLabel("股票代码:")

        股票代码_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        params_layout.addWidget(股票代码_label, 0, 0)
        self.symbol_input = QLineEdit()
        self.symbol_input.setPlaceholderText("例如: 000001.SZ")
        params_layout.addWidget(self.symbol_input, 0, 1)

        # 开始日期
        开始日期_label = QLabel("开始日期:")

        开始日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        params_layout.addWidget(开始日期_label, 1, 0)
        self.start_date_input = QDateEdit()
        self.start_date_input.setDate(QDate.currentDate().addDays(-365))
        self.start_date_input.setCalendarPopup(True)
        params_layout.addWidget(self.start_date_input, 1, 1)

        # 结束日期
        结束日期_label = QLabel("结束日期:")

        结束日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        params_layout.addWidget(结束日期_label, 2, 0)
        self.end_date_input = QDateEdit()
        self.end_date_input.setDate(QDate.currentDate())
        self.end_date_input.setCalendarPopup(True)
        params_layout.addWidget(self.end_date_input, 2, 1)

        # 数据类型
        数据类型_label = QLabel("数据类型:")

        数据类型_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")

        params_layout.addWidget(数据类型_label, 3, 0)
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems(["日线数据", "分钟数据", "基本信息"])
        params_layout.addWidget(self.data_type_combo, 3, 1)

        layout.addWidget(params_group)

        # 下载控制
        control_layout = QHBoxLayout()

        self.download_btn = QPushButton("开始下载")
        self.download_btn.clicked.connect(self.start_download)
        control_layout.addWidget(self.download_btn)

        self.stop_btn = QPushButton("停止下载")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_download)
        control_layout.addWidget(self.stop_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 下载进度
        progress_group = QGroupBox("下载进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("就绪")


        self.status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.status_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        progress_layout.addWidget(self.status_label)

        layout.addWidget(progress_group)

        # 下载日志
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

        layout.addStretch()
        return widget

    def create_view_tab(self):
        """创建数据查看标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 查询条件
        query_group = QGroupBox("查询条件")
        query_layout = QGridLayout(query_group)

        股票代码_label = QLabel("股票代码:")


        股票代码_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        params_layout.addWidget(股票代码_label, 0, 0)
        self.view_symbol_input = QLineEdit()
        query_layout.addWidget(self.view_symbol_input, 0, 1)

        日期范围_label = QLabel("日期范围:")


        日期范围_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        query_layout.addWidget(日期范围_label, 1, 0)
        date_layout = QHBoxLayout()
        self.view_start_date = QDateEdit()
        self.view_start_date.setDate(QDate.currentDate().addDays(-30))
        self.view_end_date = QDateEdit()
        self.view_end_date.setDate(QDate.currentDate())
        date_layout.addWidget(self.view_start_date)
        date_layout.addWidget(QLabel("至"))
        date_layout.addWidget(self.view_end_date)
        query_layout.addLayout(date_layout, 1, 1)

        query_btn = QPushButton("查询")
        query_btn.clicked.connect(self.query_data)
        query_layout.addWidget(query_btn, 2, 0, 1, 2)

        layout.addWidget(query_group)

        # 查询状态
        self.query_status_label = QLabel("就绪")

        self.query_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.query_status_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(self.query_status_label)

        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.data_table)

        return widget

    def create_manage_tab(self):
        """创建数据管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 数据库统计
        stats_group = QGroupBox("数据库统计")
        stats_layout = QGridLayout(stats_group)

        self.total_stocks_label = QLabel("股票数量: 0")


        self.total_stocks_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_stocks_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.total_records_label = QLabel("记录总数: 0")

        self.total_records_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_records_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.db_size_label = QLabel("数据库大小: 0 MB")

        self.db_size_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.db_size_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.last_update_label = QLabel("最后更新: 未知")

        self.last_update_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.last_update_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        stats_layout.addWidget(self.total_stocks_label, 0, 0)
        stats_layout.addWidget(self.total_records_label, 0, 1)
        stats_layout.addWidget(self.db_size_label, 1, 0)
        stats_layout.addWidget(self.last_update_label, 1, 1)

        layout.addWidget(stats_group)

        # 数据维护
        maintenance_group = QGroupBox("数据维护")
        maintenance_layout = QVBoxLayout(maintenance_group)

        button_layout = QHBoxLayout()

        refresh_stats_btn = QPushButton("刷新统计")
        refresh_stats_btn.clicked.connect(self.refresh_stats)
        button_layout.addWidget(refresh_stats_btn)

        clean_data_btn = QPushButton("清理数据")
        clean_data_btn.clicked.connect(self.clean_data)
        button_layout.addWidget(clean_data_btn)

        backup_btn = QPushButton("备份数据")
        backup_btn.clicked.connect(self.backup_data)
        button_layout.addWidget(backup_btn)

        button_layout.addStretch()
        maintenance_layout.addLayout(button_layout)

        layout.addWidget(maintenance_group)

        layout.addStretch()
        return widget

    def start_download(self):
        """开始下载数据"""
        symbol = self.symbol_input.text().strip()
        if not symbol:
            self.log_text.append("请输入股票代码")
            return

        start_date = self.start_date_input.date().toString("yyyy-MM-dd")
        end_date = self.end_date_input.date().toString("yyyy-MM-dd")

        self.download_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)

        # 创建下载线程
        self.download_thread = DataDownloadThread(symbol, start_date, end_date)
        self.download_thread.progress_updated.connect(self.progress_bar.setValue)
        self.download_thread.status_updated.connect(self.status_label.setText)
        self.download_thread.status_updated.connect(self.log_text.append)
        self.download_thread.finished_signal.connect(self.download_finished)
        self.download_thread.start()

    def stop_download(self):
        """停止下载"""
        if hasattr(self, 'download_thread') and self.download_thread.isRunning():
            self.download_thread.terminate()
            self.download_finished(False, "用户取消下载")

    def download_finished(self, success, message):
        """下载完成"""
        self.download_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log_text.append(f"下载完成: {message}")

        if success:
            self.status_label.setText("下载成功")
        else:
            self.status_label.setText("下载失败")

    def query_data(self):
        """查询数据"""
        try:
            # 获取查询参数
            symbol = self.view_symbol_input.text().strip()
            start_date = self.view_start_date.date().toString("yyyy-MM-dd")
            end_date = self.view_end_date.date().toString("yyyy-MM-dd")

            if not symbol:
                self.logger.warning("请输入股票代码")
                return

            self.logger.info(f"查询股票数据: {symbol}, {start_date} 到 {end_date}")

            # 创建示例查询结果
            import pandas as pd
            import random
            # from datetime import datetime, timedelta

            # 生成示例数据
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            data = []

            base_price = 10.0
            for i, date in enumerate(date_range):
                # 跳过周末
                if date.weekday() >= 5:
                    continue

                # 模拟价格波动
                change = random.uniform(-0.5, 0.5)
                base_price += change

                data.append({
                    '日期': date.strftime('%Y-%m-%d'),
                    '股票代码': symbol,
                    '开盘价': round(base_price + random.uniform(-0.2, 0.2), 2),
                    '最高价': round(base_price + random.uniform(0, 0.5), 2),
                    '最低价': round(base_price - random.uniform(0, 0.5), 2),
                    '收盘价': round(base_price, 2),
                    '成交量': random.randint(1000000, 10000000),
                    '成交额': round(base_price * random.randint(1000000, 10000000), 2)
                })

            # 清空表格
            self.data_table.setRowCount(0)

            if data:
                # 设置表格行数和列数
                self.data_table.setRowCount(len(data))
                self.data_table.setColumnCount(len(data[0]))
                self.data_table.setHorizontalHeaderLabels(list(data[0].keys()))

                # 填充数据
                for row, record in enumerate(data):
                    for col, (key, value) in enumerate(record.items()):
                        item = QTableWidgetItem(str(value))
                        self.data_table.setItem(row, col, item)

                # 调整列宽
                self.data_table.resizeColumnsToContents()

                self.logger.info(f"查询完成，共找到 {len(data)} 条记录")
                self.query_status_label.setText(f"查询完成，共 {len(data)} 条记录")
            else:
                self.query_status_label.setText("未找到数据")
                self.logger.info("未找到匹配的数据")

        except Exception as e:
            self.logger.error(f"查询数据失败: {e}")
            self.query_status_label.setText(f"查询失败: {str(e)}")

    def refresh_stats(self):
        """刷新统计信息"""
        try:
            import random
            from datetime import datetime

            # 模拟统计数据
            total_stocks = random.randint(100, 500)
            total_records = random.randint(10000, 100000)
            db_size = random.uniform(10, 500)
            last_update = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新统计标签
            self.total_stocks_label.setText(f"股票数量: {total_stocks}")
            self.total_records_label.setText(f"记录总数: {total_records:,}")
            self.db_size_label.setText(f"数据库大小: {db_size:.1f} MB")
            self.last_update_label.setText(f"最后更新: {last_update}")

            self.logger.info("统计信息已刷新")

        except Exception as e:
            self.logger.error(f"刷新统计信息失败: {e}")

    def clean_data(self):
        """清理数据"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self, "确认清理",
                "确定要清理过期和无效数据吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 模拟数据清理过程
                import random
                import time

                cleaned_records = random.randint(100, 1000)
                freed_space = random.uniform(5, 50)

                # 模拟清理过程
                time.sleep(1)

                self.logger.info(f"数据清理完成，清理了 {cleaned_records} 条记录，释放了 {freed_space:.1f} MB 空间")
                QMessageBox.information(
                    self, "清理完成",
                    f"数据清理完成！\n"
                    f"清理记录: {cleaned_records} 条\n"
                    f"释放空间: {freed_space:.1f} MB"
                )

                # 刷新统计信息
                self.refresh_stats()

        except Exception as e:
            self.logger.error(f"数据清理失败: {e}")
            QMessageBox.critical(self, "错误", f"数据清理失败:\n{str(e)}")

    def import_data_file(self, file_path: str):
        """导入数据文件"""
        try:
            import pandas as pd
            from pathlib import Path

            file_path = Path(file_path)
            self.logger.info(f"开始导入数据文件: {file_path}")

            # 根据文件扩展名选择读取方法
            if file_path.suffix.lower() == '.csv':
                data = pd.read_csv(file_path, encoding='utf-8')
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                data = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.json':
                data = pd.read_json(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

            # 验证数据格式
            if data.empty:
                raise ValueError("文件为空或无有效数据")

            # TODO: 将数据保存到数据库
            # 这里可以调用数据库管理器保存数据

            self.logger.info(f"成功导入 {len(data)} 条数据记录")
            return True

        except Exception as e:
            self.logger.error(f"导入数据文件失败: {e}")
            raise e

    def export_data_file(self, file_path: str):
        """导出数据文件"""
        try:
            import pandas as pd
            from pathlib import Path

            file_path = Path(file_path)
            self.logger.info(f"开始导出数据文件: {file_path}")

            # TODO: 从数据库获取数据
            # 这里创建示例数据
            data = pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=100),
                'symbol': ['000001'] * 100,
                'open': [10.0 + i * 0.1 for i in range(100)],
                'high': [10.5 + i * 0.1 for i in range(100)],
                'low': [9.5 + i * 0.1 for i in range(100)],
                'close': [10.2 + i * 0.1 for i in range(100)],
                'volume': [1000000 + i * 10000 for i in range(100)]
            })

            # 根据文件扩展名选择保存方法
            if file_path.suffix.lower() == '.csv':
                data.to_csv(file_path, index=False, encoding='utf-8')
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                data.to_excel(file_path, index=False)
            elif file_path.suffix.lower() == '.json':
                data.to_json(file_path, orient='records', date_format='iso')
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

            self.logger.info(f"成功导出 {len(data)} 条数据记录")
            return True

        except Exception as e:
            self.logger.error(f"导出数据文件失败: {e}")
            raise e

    def backup_data(self):
        """备份数据"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            from datetime import datetime
            import shutil

            # 选择备份位置
            backup_dir = QFileDialog.getExistingDirectory(
                self, "选择备份目录", ""
            )

            if backup_dir:
                # 创建备份文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"trading_data_backup_{timestamp}"
                backup_path = Path(backup_dir) / backup_name

                # 模拟备份过程
                import time
                import random

                self.logger.info(f"开始备份数据到: {backup_path}")

                # 创建备份目录
                backup_path.mkdir(parents=True, exist_ok=True)

                # 模拟备份文件
                backup_files = [
                    "stock_data.db",
                    "trading_history.csv",
                    "strategy_configs.json",
                    "user_settings.json"
                ]

                for file_name in backup_files:
                    # 模拟文件备份
                    time.sleep(0.2)
                    backup_file = backup_path / file_name
                    backup_file.write_text(f"备份文件: {file_name}\n创建时间: {datetime.now()}")

                backup_size = random.uniform(10, 100)

                self.logger.info(f"数据备份完成: {backup_path}")
                QMessageBox.information(
                    self, "备份完成",
                    f"数据备份完成！\n"
                    f"备份位置: {backup_path}\n"
                    f"备份大小: {backup_size:.1f} MB\n"
                    f"备份文件: {len(backup_files)} 个"
                )

        except Exception as e:
            self.logger.error(f"数据备份失败: {e}")
            QMessageBox.critical(self, "错误", f"数据备份失败:\n{str(e)}")

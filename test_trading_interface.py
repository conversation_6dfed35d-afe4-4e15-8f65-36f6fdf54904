#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实盘交易接口完善情况
"""

import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from trading.real_broker import create_broker, HuataiBroker, ZhongxinBroker
from trading.base_broker import OrderSide, OrderType, OrderStatus


def test_huatai_broker():
    """测试华泰证券接口"""
    print("\n" + "="*50)
    print("测试华泰证券接口")
    print("="*50)
    
    try:
        # 创建华泰券商实例
        config = {
            'api_key': 'test_api_key',
            'secret_key': 'test_secret_key',
            'account_id': 'test_account',
            'base_url': 'https://api.huatai.com',
            'sandbox': True  # 使用沙盒环境
        }
        
        broker = create_broker('huatai', config)
        if broker:
            print("✓ 华泰券商实例创建成功")
            
            # 测试连接
            if broker.connect():
                print("✓ 连接成功")
                
                # 测试获取账户信息
                account_info = broker.get_account_info()
                if account_info:
                    print(f"✓ 账户信息: 总资产={account_info.total_assets}, 可用资金={account_info.available_cash}")
                
                # 测试获取持仓
                positions = broker.get_positions()
                print(f"✓ 持仓数量: {len(positions)}")
                
                # 测试下单
                order_id = broker.place_order(
                    symbol="000001.SZ",
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=100,
                    price=12.0
                )
                if order_id:
                    print(f"✓ 下单成功: {order_id}")
                    
                    # 测试获取订单状态
                    order_status = broker.get_order_status(order_id)
                    if order_status:
                        print(f"✓ 订单状态: {order_status.status}")
                
                # 测试获取订单列表
                orders = broker.get_orders()
                print(f"✓ 订单列表: {len(orders)} 个订单")
                
                # 测试获取成交记录
                trades = broker.get_trades()
                print(f"✓ 成交记录: {len(trades)} 条记录")
                
                # 断开连接
                broker.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 连接失败")
        else:
            print("❌ 华泰券商实例创建失败")
        
        print("✅ 华泰证券接口测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 华泰证券接口测试失败: {e}")
        return False


def test_zhongxin_broker():
    """测试中信证券接口"""
    print("\n" + "="*50)
    print("测试中信证券接口")
    print("="*50)
    
    try:
        # 创建中信券商实例
        config = {
            'api_key': 'test_api_key',
            'secret_key': 'test_secret_key',
            'account_id': 'test_account',
            'base_url': 'https://api.zhongxin.com',
            'sandbox': True  # 使用沙盒环境
        }
        
        broker = create_broker('zhongxin', config)
        if broker:
            print("✓ 中信券商实例创建成功")
            
            # 测试连接
            if broker.connect():
                print("✓ 连接成功")
                
                # 测试获取账户信息
                account_info = broker.get_account_info()
                if account_info:
                    print(f"✓ 账户信息: 总资产={account_info.total_assets}, 可用资金={account_info.available_cash}")
                
                # 测试获取持仓
                positions = broker.get_positions()
                print(f"✓ 持仓数量: {len(positions)}")
                for symbol, position in positions.items():
                    print(f"  - {symbol}: {position.quantity}股, 成本价={position.avg_price}")
                
                # 测试下单
                order_id = broker.place_order(
                    symbol="000002.SZ",
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=100,
                    price=25.0
                )
                if order_id:
                    print(f"✓ 下单成功: {order_id}")
                    
                    # 测试获取订单状态
                    order_status = broker.get_order_status(order_id)
                    if order_status:
                        print(f"✓ 订单状态: {order_status.status}")
                    
                    # 测试撤单
                    if broker.cancel_order(order_id):
                        print("✓ 撤单成功")
                
                # 测试获取订单列表
                orders = broker.get_orders()
                print(f"✓ 订单列表: {len(orders)} 个订单")
                
                # 测试获取成交记录
                trades = broker.get_trades()
                print(f"✓ 成交记录: {len(trades)} 条记录")
                
                # 断开连接
                broker.disconnect()
                print("✓ 断开连接成功")
            else:
                print("❌ 连接失败")
        else:
            print("❌ 中信券商实例创建失败")
        
        print("✅ 中信证券接口测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 中信证券接口测试失败: {e}")
        return False


def test_broker_factory():
    """测试券商工厂"""
    print("\n" + "="*50)
    print("测试券商工厂")
    print("="*50)
    
    try:
        # 测试支持的券商类型
        supported_brokers = ['huatai', 'zhongxin']
        
        for broker_type in supported_brokers:
            config = {
                'api_key': 'test_key',
                'secret_key': 'test_secret',
                'account_id': 'test_account',
                'sandbox': True
            }
            
            broker = create_broker(broker_type, config)
            if broker:
                print(f"✓ {broker_type} 券商创建成功: {broker.get_broker_name()}")
            else:
                print(f"❌ {broker_type} 券商创建失败")
        
        # 测试不支持的券商类型
        unsupported_broker = create_broker('unknown', {})
        if unsupported_broker is None:
            print("✓ 不支持的券商类型正确返回None")
        
        print("✅ 券商工厂测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 券商工厂测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试实盘交易接口完善情况")
    
    test_results = []
    
    # 测试华泰证券接口
    test_results.append(test_huatai_broker())
    
    # 测试中信证券接口
    test_results.append(test_zhongxin_broker())
    
    # 测试券商工厂
    test_results.append(test_broker_factory())
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！实盘交易接口完善成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()

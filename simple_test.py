#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("测试导入分析中心组件...")
try:
    from gui.widgets.analysis_center_widget import AnalysisCenterWidget
    print("✓ 分析中心组件导入成功")
except Exception as e:
    print(f"❌ 分析中心组件导入失败: {e}")

print("测试导入策略中心组件...")
try:
    from gui.widgets.strategy_center_widget import StrategyCenterWidget
    print("✓ 策略中心组件导入成功")
except Exception as e:
    print(f"❌ 策略中心组件导入失败: {e}")

print("测试完成")

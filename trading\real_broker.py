#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实盘交易接口
支持主流券商API接入
"""

import time
import hashlib
import hmac
import requests
from datetime import datetime
from typing import Dict, Any, Optional, List

from .base_broker import BaseBroker, AccountInfo, Position, Order, OrderSide, OrderType, OrderStatus
from utils.logger import get_logger



class HuataiBroker(BaseBroker):
    """华泰证券接口"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # 直接使用配置参数
        self.api_key = config.get('api_key', '')
        self.secret_key = config.get('secret_key', '')
        self.account_id = config.get('account_id', '')
        self.base_url = config.get('base_url', 'https://api.huatai.com')
        self.sandbox = config.get('sandbox', True)

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'QuantTrading/1.0',
            'Content-Type': 'application/json'
        })

    def connect(self) -> bool:
        """连接到华泰证券"""
        try:
            self.logger.info("正在连接华泰证券...")

            # 验证API密钥
            if not self._authenticate():
                return False

            # 获取账户信息
            account_info = self.get_account_info()
            if account_info is None:
                return False

            self.is_connected = True
            self.logger.info("华泰证券连接成功")
            return True

        except Exception as e:
            self.logger.error(f"连接华泰证券失败: {e}")
            return False

    def disconnect(self) -> bool:
        """断开连接"""
        try:
            self.is_connected = False
            self.session.close()
            self.logger.info("已断开华泰证券连接")
            return True
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            return False

    def _authenticate(self) -> bool:
        """身份验证"""
        try:
            # 构建认证请求
            timestamp = str(int(time.time() * 1000))
            message = f"{timestamp}{self.api_key}"
            signature = hmac.new(
                self.secret_key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()

            auth_data = {
                'api_key': self.api_key,
                'timestamp': timestamp,
                'signature': signature
            }

            if self.sandbox:
                # 沙盒环境直接返回成功
                self.logger.info("使用沙盒环境，跳过实际认证")
                return True

            # 实际环境需要真实API调用
            response = self.session.post(
                f"{self.base_url}/auth",
                json=auth_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.session.headers['Authorization'] = f"Bearer {result.get('token')}"
                    return True

            self.logger.error(f"认证失败: {response.text}")
            return False

        except Exception as e:
            self.logger.error(f"认证过程失败: {e}")
            return False

    def get_account_info(self) -> Optional[AccountInfo]:
        """获取账户信息"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟数据
                return AccountInfo(
                    account_id=self.account_id,
                    total_assets=1000000.0,
                    available_cash=800000.0,
                    market_value=200000.0
                )

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/account/{self.account_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                return AccountInfo(
                    account_id=data['account_id'],
                    total_assets=float(data['total_assets']),
                    available_cash=float(data['available_cash']),
                    market_value=float(data['market_value']),
                    yesterday_assets=float(data['yesterday_assets']),
                    frozen_cash=float(data.get('frozen_cash', 0))
                )

            self.logger.error(f"获取账户信息失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"获取账户信息异常: {e}")
            return None

    def get_positions(self) -> Dict[str, Position]:
        """获取持仓信息"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟持仓
                return {
                    "000001.SZ": Position(
                        symbol="000001.SZ",
                        quantity=1000,
                        avg_price=12.0,
                        market_value=12500.0,
                        unrealized_pnl=500.0
                    ),
                    "600036.SH": Position(
                        symbol="600036.SH",
                        quantity=500,
                        avg_price=36.0,
                        market_value=17600.0,
                        unrealized_pnl=-400.0
                    )
                }

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/positions/{self.account_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                positions = {}

                for pos_data in data.get('positions', []):
                    position = Position(
                        symbol=pos_data['symbol'],
                        quantity=int(pos_data['quantity']),
                        avg_price=float(pos_data['avg_price']),
                        cost=float(pos_data['cost']),
                        market_value=float(pos_data['market_value'])
                    )
                    positions[position.symbol] = position

                return positions

            self.logger.error(f"获取持仓信息失败: {response.text}")
            return {}

        except Exception as e:
            self.logger.error(f"获取持仓信息异常: {e}")
            return {}

    def place_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                   quantity: int, price: float = None) -> Optional[str]:
        """下单"""
        try:
            order_data = {
                'symbol': symbol,
                'side': side.value,
                'type': order_type.value,
                'quantity': quantity,
                'account_id': self.account_id
            }

            if price is not None:
                order_data['price'] = price

            if self.sandbox:
                # 沙盒环境生成模拟订单ID
                order_id = f"SB{int(time.time())}{quantity}"

                # 创建模拟订单
                order = Order(
                    order_id=order_id,
                    symbol=symbol,
                    side=side,
                    order_type=order_type,
                    quantity=quantity,
                    price=price
                )

                self.orders[order_id] = order
                self.logger.info(f"沙盒环境下单成功: {order_id}")
                return order_id

            # 实际环境API调用
            response = self.session.post(
                f"{self.base_url}/orders",
                json=order_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    order_id = result.get('order_id')
                    self.logger.info(f"下单成功: {order_id}")
                    return order_id

            self.logger.error(f"下单失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"下单异常: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        try:
            if self.sandbox:
                # 沙盒环境模拟撤单
                if order_id in self.orders:
                    self.orders[order_id].status = OrderStatus.CANCELLED
                    self.logger.info(f"沙盒环境撤单成功: {order_id}")
                    return True
                return False

            # 实际环境API调用
            response = self.session.delete(
                f"{self.base_url}/orders/{order_id}",
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.logger.info(f"撤单成功: {order_id}")
                    return True

            self.logger.error(f"撤单失败: {response.text}")
            return False

        except Exception as e:
            self.logger.error(f"撤单异常: {e}")
            return False

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟订单状态
                if order_id in self.orders:
                    return self.orders[order_id]
                return None

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/orders/{order_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    order_data = data.get('order', {})
                    return Order(
                        order_id=order_data['order_id'],
                        symbol=order_data['symbol'],
                        side=OrderSide(order_data['side']),
                        order_type=OrderType(order_data['type']),
                        quantity=int(order_data['quantity']),
                        price=float(order_data.get('price', 0)),
                        status=OrderStatus(order_data['status']),
                        create_time=datetime.fromisoformat(order_data['create_time'])
                    )

            self.logger.error(f"获取订单状态失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"获取订单状态异常: {e}")
            return None

    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        try:
            if self.sandbox:
                # 沙盒环境过滤订单
                orders = list(self.orders.values())
                if symbol:
                    orders = [o for o in orders if o.symbol == symbol]
                if status:
                    orders = [o for o in orders if o.status == status]
                return orders

            # 实际环境API调用
            params = {}
            if symbol:
                params['symbol'] = symbol
            if status:
                params['status'] = status.value

            response = self.session.get(
                f"{self.base_url}/orders/{self.account_id}",
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    orders = []
                    for order_data in data.get('orders', []):
                        order = Order(
                            order_id=order_data['order_id'],
                            symbol=order_data['symbol'],
                            side=OrderSide(order_data['side']),
                            order_type=OrderType(order_data['type']),
                            quantity=int(order_data['quantity']),
                            price=float(order_data.get('price', 0)),
                            status=OrderStatus(order_data['status']),
                            create_time=datetime.fromisoformat(order_data['create_time'])
                        )
                        orders.append(order)
                    return orders

            self.logger.error(f"获取订单列表失败: {response.text}")
            return []

        except Exception as e:
            self.logger.error(f"获取订单列表异常: {e}")
            return []

    def get_trades(self, symbol: str = None, start_date: datetime = None,
                  end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟成交记录
                trades = []
                for order in self.orders.values():
                    if order.status == OrderStatus.FILLED:
                        if symbol and order.symbol != symbol:
                            continue
                        if start_date and order.create_time < start_date:
                            continue
                        if end_date and order.create_time > end_date:
                            continue

                        trade = {
                            'trade_id': f"T{order.order_id}",
                            'order_id': order.order_id,
                            'symbol': order.symbol,
                            'side': order.side.value,
                            'quantity': order.filled_quantity or order.quantity,
                            'price': order.avg_fill_price or order.price,
                            'amount': (order.filled_quantity or order.quantity) * (order.avg_fill_price or order.price),
                            'commission': order.commission or 0,
                            'trade_time': order.update_time or order.create_time
                        }
                        trades.append(trade)
                return trades

            # 实际环境API调用
            params = {}
            if symbol:
                params['symbol'] = symbol
            if start_date:
                params['start_date'] = start_date.strftime('%Y-%m-%d')
            if end_date:
                params['end_date'] = end_date.strftime('%Y-%m-%d')

            response = self.session.get(
                f"{self.base_url}/trades/{self.account_id}",
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('trades', [])

            self.logger.error(f"获取成交记录失败: {response.text}")
            return []

        except Exception as e:
            self.logger.error(f"获取成交记录异常: {e}")
            return []


class ZhongxinBroker(BaseBroker):
    """中信证券接口"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.session = requests.Session()
        self.base_url = config.get('base_url', 'https://api.zhongxin.com')
        self.api_key = config.get('api_key', '')
        self.secret_key = config.get('secret_key', '')
        self.account_id = config.get('account_id', '')
        self.sandbox = config.get('sandbox', True)

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'ZhongxinTrading/1.0',
            'Content-Type': 'application/json'
        })

        self.logger.info("中信证券接口初始化完成")

    def connect(self) -> bool:
        """连接到中信证券"""
        try:
            if self.sandbox:
                # 沙盒环境直接返回成功
                self.is_connected = True
                self.logger.info("中信证券沙盒环境连接成功")
                return True

            # 实际环境需要认证
            auth_data = {
                'api_key': self.api_key,
                'timestamp': str(int(time.time() * 1000))
            }

            # 生成签名
            message = f"{auth_data['timestamp']}{self.api_key}"
            signature = hmac.new(
                self.secret_key.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            auth_data['signature'] = signature

            response = self.session.post(
                f"{self.base_url}/auth",
                json=auth_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.session.headers['Authorization'] = f"Bearer {result.get('token')}"
                    self.is_connected = True
                    self.logger.info("中信证券连接成功")
                    return True

            self.logger.error(f"中信证券连接失败: {response.text}")
            return False

        except Exception as e:
            self.logger.error(f"中信证券连接异常: {e}")
            return False

    def disconnect(self) -> bool:
        """断开连接"""
        self.is_connected = False
        self.session.close()
        self.logger.info("中信证券连接已断开")
        return True

    def get_account_info(self) -> Optional[AccountInfo]:
        """获取账户信息"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟数据
                return AccountInfo(
                    account_id=self.account_id,
                    total_assets=500000.0,
                    available_cash=400000.0,
                    market_value=100000.0
                )

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/account/{self.account_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    account_data = data.get('data', {})
                    return AccountInfo(
                        account_id=account_data['account_id'],
                        total_assets=float(account_data['total_assets']),
                        available_cash=float(account_data['available_cash']),
                        market_value=float(account_data['market_value']),
                        yesterday_assets=float(account_data['yesterday_assets']),
                        frozen_cash=float(account_data.get('frozen_cash', 0))
                    )

            self.logger.error(f"获取中信账户信息失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"获取中信账户信息异常: {e}")
            return None

    def get_positions(self) -> Dict[str, Position]:
        """获取持仓信息"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟持仓
                return {
                    "000002.SZ": Position(
                        symbol="000002.SZ",
                        quantity=800,
                        avg_price=25.0,
                        market_value=20800.0,
                        unrealized_pnl=800.0
                    ),
                    "600519.SH": Position(
                        symbol="600519.SH",
                        quantity=100,
                        avg_price=1800.0,
                        market_value=185000.0,
                        unrealized_pnl=5000.0
                    )
                }

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/positions/{self.account_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    positions = {}
                    for pos_data in data.get('positions', []):
                        position = Position(
                            symbol=pos_data['symbol'],
                            quantity=int(pos_data['quantity']),
                            avg_price=float(pos_data['avg_price']),
                            cost=float(pos_data['cost']),
                            market_value=float(pos_data['market_value'])
                        )
                        positions[position.symbol] = position
                    return positions

            self.logger.error(f"获取中信持仓信息失败: {response.text}")
            return {}

        except Exception as e:
            self.logger.error(f"获取中信持仓信息异常: {e}")
            return {}

    def place_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                   quantity: int, price: float = None) -> Optional[str]:
        """下单"""
        try:
            order_data = {
                'symbol': symbol,
                'side': side.value,
                'type': order_type.value,
                'quantity': quantity,
                'account_id': self.account_id
            }

            if price is not None:
                order_data['price'] = price

            if self.sandbox:
                # 沙盒环境生成模拟订单ID
                order_id = f"ZX{int(time.time())}{quantity}"
                self.logger.info(f"中信沙盒环境下单成功: {order_id}")
                return order_id

            # 实际环境API调用
            response = self.session.post(
                f"{self.base_url}/orders",
                json=order_data,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    order_id = result.get('order_id')
                    self.logger.info(f"中信下单成功: {order_id}")
                    return order_id

            self.logger.error(f"中信下单失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"中信下单异常: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """撤销订单"""
        try:
            if self.sandbox:
                # 沙盒环境模拟撤单
                self.logger.info(f"中信沙盒环境撤单成功: {order_id}")
                return True

            # 实际环境API调用
            response = self.session.delete(
                f"{self.base_url}/orders/{order_id}",
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.logger.info(f"中信撤单成功: {order_id}")
                    return True

            self.logger.error(f"中信撤单失败: {response.text}")
            return False

        except Exception as e:
            self.logger.error(f"中信撤单异常: {e}")
            return False

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟订单状态
                return Order(
                    order_id=order_id,
                    symbol="000002.SZ",
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    quantity=100,
                    price=25.0
                )

            # 实际环境API调用
            response = self.session.get(
                f"{self.base_url}/orders/{order_id}",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    order_data = data.get('order', {})
                    return Order(
                        order_id=order_data['order_id'],
                        symbol=order_data['symbol'],
                        side=OrderSide(order_data['side']),
                        order_type=OrderType(order_data['type']),
                        quantity=int(order_data['quantity']),
                        price=float(order_data.get('price', 0)),
                        status=OrderStatus(order_data['status']),
                        create_time=datetime.fromisoformat(order_data['create_time'])
                    )

            self.logger.error(f"获取中信订单状态失败: {response.text}")
            return None

        except Exception as e:
            self.logger.error(f"获取中信订单状态异常: {e}")
            return None

    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟订单列表
                orders = [
                    Order(
                        order_id="ZX001",
                        symbol="000002.SZ",
                        side=OrderSide.BUY,
                        order_type=OrderType.LIMIT,
                        quantity=100,
                        price=25.0
                    )
                ]

                # 应用过滤条件
                if symbol:
                    orders = [o for o in orders if o.symbol == symbol]
                if status:
                    orders = [o for o in orders if o.status == status]
                return orders

            # 实际环境API调用
            params = {}
            if symbol:
                params['symbol'] = symbol
            if status:
                params['status'] = status.value

            response = self.session.get(
                f"{self.base_url}/orders/{self.account_id}",
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    orders = []
                    for order_data in data.get('orders', []):
                        order = Order(
                            order_id=order_data['order_id'],
                            symbol=order_data['symbol'],
                            side=OrderSide(order_data['side']),
                            order_type=OrderType(order_data['type']),
                            quantity=int(order_data['quantity']),
                            price=float(order_data.get('price', 0)),
                            status=OrderStatus(order_data['status']),
                            create_time=datetime.fromisoformat(order_data['create_time'])
                        )
                        orders.append(order)
                    return orders

            self.logger.error(f"获取中信订单列表失败: {response.text}")
            return []

        except Exception as e:
            self.logger.error(f"获取中信订单列表异常: {e}")
            return []

    def get_trades(self, symbol: str = None, start_date: datetime = None,
                  end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        try:
            if self.sandbox:
                # 沙盒环境返回模拟成交记录
                trades = [
                    {
                        'trade_id': 'ZXT001',
                        'order_id': 'ZX001',
                        'symbol': '000002.SZ',
                        'side': 'buy',
                        'quantity': 100,
                        'price': 25.0,
                        'amount': 2500.0,
                        'commission': 7.5,
                        'trade_time': datetime.now()
                    }
                ]

                # 应用过滤条件
                if symbol:
                    trades = [t for t in trades if t['symbol'] == symbol]
                if start_date:
                    trades = [t for t in trades if t['trade_time'] >= start_date]
                if end_date:
                    trades = [t for t in trades if t['trade_time'] <= end_date]
                return trades

            # 实际环境API调用
            params = {}
            if symbol:
                params['symbol'] = symbol
            if start_date:
                params['start_date'] = start_date.strftime('%Y-%m-%d')
            if end_date:
                params['end_date'] = end_date.strftime('%Y-%m-%d')

            response = self.session.get(
                f"{self.base_url}/trades/{self.account_id}",
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('trades', [])

            self.logger.error(f"获取中信成交记录失败: {response.text}")
            return []

        except Exception as e:
            self.logger.error(f"获取中信成交记录异常: {e}")
            return []


# 券商工厂
BROKER_CLASSES = {
    'huatai': HuataiBroker,
    'zhongxin': ZhongxinBroker,
}


def create_broker(broker_type: str, config: Dict[str, Any]) -> Optional[BaseBroker]:
    """创建券商接口"""
    broker_class = BROKER_CLASSES.get(broker_type.lower())
    if broker_class:
        return broker_class(config)
    else:
        logger = get_logger("BrokerFactory")
        logger.error(f"不支持的券商类型: {broker_type}")
        return None

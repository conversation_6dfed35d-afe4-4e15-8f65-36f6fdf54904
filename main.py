#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易系统主程序
作者: AI助手
版本: 1.0.0
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger
from gui.main_window import MainWindow


class QuantitativeTradingApp:
    """量化交易应用程序主类"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.logger = setup_logger()

    def initialize_app(self):
        """初始化应用程序"""
        try:
            # 创建QApplication实例
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("量化交易系统")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("量化交易工作室")

            # 设置应用程序图标
            icon_path = project_root / "assets" / "icon.ico"
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))

            # 设置高DPI支持
            self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

            self.logger.info("应用程序初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}")
            return False

    def create_main_window(self):
        """创建主窗口"""
        try:
            print("正在创建主窗口...")
            self.main_window = MainWindow()
            print("主窗口对象创建成功")

            # 确保窗口显示在前台
            print("正在显示窗口...")
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

            # 强制刷新窗口
            self.app.processEvents()
            print("窗口显示完成")

            self.logger.info("主窗口创建成功")
            print("✅ 主窗口创建并显示成功！")
            return True

        except Exception as e:
            error_msg = f"主窗口创建失败: {e}"
            print(f"❌ {error_msg}")
            self.logger.error(error_msg)
            QMessageBox.critical(
                None,
                "错误",
                f"主窗口创建失败:\n{str(e)}"
            )
            return False

    def run(self):
        """运行应用程序"""
        try:
            print("🚀 开始初始化应用程序...")
            if not self.initialize_app():
                print("❌ 应用程序初始化失败")
                return 1

            print("🖥️ 开始创建主窗口...")
            if not self.create_main_window():
                print("❌ 主窗口创建失败")
                return 1

            print("✅ 量化交易系统启动成功！")
            print("🔄 进入事件循环，窗口应该已经显示...")
            self.logger.info("量化交易系统启动成功")

            # 进入事件循环
            exit_code = self.app.exec()
            print(f"🏁 事件循环结束，退出码: {exit_code}")
            return exit_code

        except Exception as e:
            error_msg = f"应用程序运行失败: {e}"
            print(f"❌ {error_msg}")
            self.logger.error(error_msg)
            return 1

        finally:
            print("🔚 量化交易系统退出")
            self.logger.info("量化交易系统退出")


def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'PyQt5', 'pandas', 'numpy', 'matplotlib',
        'tushare', 'akshare', 'requests'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    return True


def main():
    """主函数"""
    print("=" * 50)
    print("量化交易系统 v1.0.0")
    print("正在启动...")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1

    # 创建并运行应用
    app = QuantitativeTradingApp()
    exit_code = app.run()

    return exit_code


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据监控组件
"""

import sys
import random
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QComboBox,
    QTableWidget, QTableWidgetItem, QTabWidget,
    QLineEdit, QSpinBox, QCheckBox, QTextEdit,
    QSplitter, QHeaderView, QMessageBox, QListWidget,
    QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from data.collectors.multi_source_manager import MultiSourceManager


class RealtimeDataThread(QThread):
    """实时数据获取线程"""
    data_updated = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, symbols, update_interval=5):
        super().__init__()
        self.symbols = symbols
        self.update_interval = update_interval
        self.manager = MultiSourceManager()
        self.running = True
        self.logger = get_logger("RealtimeDataThread")

    def run(self):
        """运行实时数据获取"""
        while self.running:
            try:
                if self.symbols:
                    # 获取实时数据
                    data, source_used = self.manager.get_realtime_data_with_fallback(self.symbols)

                    if data is not None and not data.empty:
                        # 转换为列表格式
                        data_list = []
                        for _, row in data.iterrows():
                            data_list.append({
                                'symbol': row.get('symbol', ''),
                                'name': f"股票{row.get('symbol', '')}",
                                'price': row.get('price', 0.0),
                                'change': row.get('change', 0.0),
                                'pct_change': row.get('pct_change', 0.0),
                                'volume': row.get('volume', 0),
                                'timestamp': row.get('timestamp', datetime.now()),
                                'source': source_used
                            })

                        self.data_updated.emit(data_list)
                        self.logger.debug(f"实时数据更新成功，共 {len(data_list)} 只股票")
                    else:
                        self.error_occurred.emit("获取实时数据失败")

                # 等待下次更新
                self.msleep(self.update_interval * 1000)

            except Exception as e:
                self.error_occurred.emit(f"实时数据获取错误: {str(e)}")
                self.msleep(5000)  # 错误时等待5秒

    def stop(self):
        """停止数据获取"""
        self.running = False

    def update_symbols(self, symbols):
        """更新监控股票列表"""
        self.symbols = symbols

    def update_interval_setting(self, interval):
        """更新刷新间隔"""
        self.update_interval = interval


class PriceAlertManager(QWidget):
    """价格预警管理器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("PriceAlertManager")
        self.alerts = []  # 预警规则列表
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("价格预警管理")
        title_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 添加预警规则
        add_group = QGroupBox("添加预警规则")
        add_layout = QGridLayout(add_group)

        股票代码_label = QLabel("股票代码:")


        股票代码_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        add_layout.addWidget(股票代码_label, 0, 0)
        self.alert_symbol_input = QLineEdit()
        self.alert_symbol_input.setPlaceholderText("例如: 000001")
        add_layout.addWidget(self.alert_symbol_input, 0, 1)

        预警类型_label = QLabel("预警类型:")


        预警类型_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        add_layout.addWidget(预警类型_label, 1, 0)
        self.alert_type_combo = QComboBox()
        self.alert_type_combo.addItems(["价格上涨", "价格下跌", "涨幅超过", "跌幅超过"])
        add_layout.addWidget(self.alert_type_combo, 1, 1)

        预警值_label = QLabel("预警值:")


        预警值_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        add_layout.addWidget(预警值_label, 2, 0)
        self.alert_value_input = QLineEdit()
        self.alert_value_input.setPlaceholderText("价格或百分比")
        add_layout.addWidget(self.alert_value_input, 2, 1)

        add_alert_btn = QPushButton("添加预警")
        add_alert_btn.clicked.connect(self.add_alert)
        add_layout.addWidget(add_alert_btn, 3, 0, 1, 2)

        layout.addWidget(add_group)

        # 预警规则列表
        rules_group = QGroupBox("预警规则列表")
        rules_layout = QVBoxLayout(rules_group)

        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(5)
        self.alerts_table.setHorizontalHeaderLabels([
            "股票代码", "预警类型", "预警值", "状态", "操作"
        ])
        self.alerts_table.setAlternatingRowColors(True)
        rules_layout.addWidget(self.alerts_table)

        layout.addWidget(rules_group)

        # 预警日志
        log_group = QGroupBox("预警日志")
        log_layout = QVBoxLayout(log_group)

        self.alert_log = QTextEdit()
        self.alert_log.setMaximumHeight(150)
        self.alert_log.setReadOnly(True)
        log_layout.addWidget(self.alert_log)

        layout.addWidget(log_group)

    def add_alert(self):
        """添加预警规则"""
        try:
            symbol = self.alert_symbol_input.text().strip()
            alert_type = self.alert_type_combo.currentText()
            alert_value = self.alert_value_input.text().strip()

            if not symbol or not alert_value:
                QMessageBox.warning(self, "警告", "请填写完整的预警信息")
                return

            # 验证预警值
            try:
                value = float(alert_value)
            except ValueError:
                QMessageBox.warning(self, "警告", "预警值必须是数字")
                return

            # 创建预警规则
            alert_rule = {
                'id': len(self.alerts) + 1,
                'symbol': symbol,
                'type': alert_type,
                'value': value,
                'status': '启用',
                'created_time': datetime.now()
            }

            self.alerts.append(alert_rule)
            self.update_alerts_table()

            # 清空输入框
            self.alert_symbol_input.clear()
            self.alert_value_input.clear()

            self.logger.info(f"添加预警规则: {symbol} {alert_type} {value}")
            self.alert_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] 添加预警: {symbol} {alert_type} {value}")

        except Exception as e:
            self.logger.error(f"添加预警规则失败: {e}")
            QMessageBox.critical(self, "错误", f"添加预警失败:\n{str(e)}")

    def update_alerts_table(self):
        """更新预警规则表格"""
        self.alerts_table.setRowCount(len(self.alerts))

        for row, alert in enumerate(self.alerts):
            self.alerts_table.setItem(row, 0, QTableWidgetItem(alert['symbol']))
            self.alerts_table.setItem(row, 1, QTableWidgetItem(alert['type']))
            self.alerts_table.setItem(row, 2, QTableWidgetItem(str(alert['value'])))

            status_item = QTableWidgetItem(alert['status'])
            if alert['status'] == '启用':
                status_item.setBackground(QColor("#E8F5E8"))
            else:
                status_item.setBackground(QColor("#FFEBEE"))
            self.alerts_table.setItem(row, 3, status_item)

            # 操作按钮
            delete_btn = QPushButton("删除")
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_alert(r))
            self.alerts_table.setCellWidget(row, 4, delete_btn)

        self.alerts_table.resizeColumnsToContents()

    def delete_alert(self, row):
        """删除预警规则"""
        if 0 <= row < len(self.alerts):
            alert = self.alerts[row]
            self.alerts.pop(row)
            self.update_alerts_table()
            self.logger.info(f"删除预警规则: {alert['symbol']}")
            self.alert_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] 删除预警: {alert['symbol']}")

    def check_alerts(self, realtime_data):
        """检查预警条件"""
        try:
            for data in realtime_data:
                symbol = data.get('symbol', '')
                price = data.get('price', 0.0)
                pct_change = data.get('pct_change', 0.0)

                for alert in self.alerts:
                    if alert['status'] != '启用' or alert['symbol'] != symbol:
                        continue

                    triggered = False
                    message = ""

                    if alert['type'] == '价格上涨' and price >= alert['value']:
                        triggered = True
                        message = f"{symbol} 价格达到 {price:.2f}，触发上涨预警"
                    elif alert['type'] == '价格下跌' and price <= alert['value']:
                        triggered = True
                        message = f"{symbol} 价格跌至 {price:.2f}，触发下跌预警"
                    elif alert['type'] == '涨幅超过' and pct_change >= alert['value']:
                        triggered = True
                        message = f"{symbol} 涨幅达到 {pct_change:.2f}%，触发涨幅预警"
                    elif alert['type'] == '跌幅超过' and pct_change <= -alert['value']:
                        triggered = True
                        message = f"{symbol} 跌幅达到 {pct_change:.2f}%，触发跌幅预警"

                    if triggered:
                        self.alert_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ {message}")
                        self.logger.warning(message)

                        # 可以在这里添加更多预警方式，如弹窗、邮件等

        except Exception as e:
            self.logger.error(f"检查预警条件失败: {e}")


class RealtimeMonitorWidget(QWidget):
    """实时监控主组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("RealtimeMonitorWidget")
        self.realtime_thread = None
        self.monitored_symbols = []
        self.init_ui()
        self.logger.info("实时监控组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("实时数据监控中心")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 控制面板
        self.create_control_panel(layout)

        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)

        # 左侧：实时数据表格
        self.create_realtime_table(main_splitter)

        # 右侧：预警管理
        self.create_alert_panel(main_splitter)

        # 设置分割器比例
        main_splitter.setSizes([600, 400])
        layout.addWidget(main_splitter)

        # 状态栏
        self.create_status_bar(layout)

    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_group = QGroupBox("监控控制")
        control_layout = QGridLayout(control_group)

        # 股票代码输入
        监控股票_label = QLabel("监控股票:")
        监控股票_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        control_layout.addWidget(监控股票_label, 0, 0)
        self.symbols_input = QLineEdit()
        self.symbols_input.setPlaceholderText("输入股票代码，用逗号分隔，例如: 000001,000002,600000")
        control_layout.addWidget(self.symbols_input, 0, 1, 1, 2)

        # 刷新间隔
        刷新间隔_label = QLabel("刷新间隔:")
        刷新间隔_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        control_layout.addWidget(刷新间隔_label, 1, 0)
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 60)
        self.interval_spinbox.setValue(5)
        self.interval_spinbox.setSuffix(" 秒")
        control_layout.addWidget(self.interval_spinbox, 1, 1)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        button_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)

        self.clear_btn = QPushButton("清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()
        control_layout.addLayout(button_layout, 1, 2)

        parent_layout.addWidget(control_group)

    def create_realtime_table(self, parent_splitter):
        """创建实时数据表格"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # 表格标题
        table_title = QLabel("实时行情数据")
        table_title.setFont(QFont("微软雅黑", 12, QFont.Bold))
        table_layout.addWidget(table_title)

        # 实时数据表格
        self.realtime_table = QTableWidget()
        self.realtime_table.setColumnCount(8)
        self.realtime_table.setHorizontalHeaderLabels([
            "股票代码", "股票名称", "最新价", "涨跌额", "涨跌幅", "成交量", "更新时间", "数据源"
        ])
        self.realtime_table.setAlternatingRowColors(True)
        self.realtime_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.realtime_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

        table_layout.addWidget(self.realtime_table)
        parent_splitter.addWidget(table_widget)

    def create_alert_panel(self, parent_splitter):
        """创建预警面板"""
        alert_widget = QWidget()
        alert_layout = QVBoxLayout(alert_widget)

        # 预警管理器
        self.alert_manager = PriceAlertManager()
        alert_layout.addWidget(self.alert_manager)

        parent_splitter.addWidget(alert_widget)

    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()

        self.status_label = QLabel("就绪")


        self.status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        self.monitor_count_label = QLabel("监控股票: 0")


        self.monitor_count_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        status_layout.addWidget(self.monitor_count_label)

        self.update_time_label = QLabel("最后更新: 未开始")


        self.update_time_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        status_layout.addWidget(self.update_time_label)

        parent_layout.addLayout(status_layout)

    def start_monitoring(self):
        """开始监控"""
        try:
            # 获取监控股票列表
            symbols_text = self.symbols_input.text().strip()
            if not symbols_text:
                QMessageBox.warning(self, "警告", "请输入要监控的股票代码")
                return

            self.monitored_symbols = [s.strip() for s in symbols_text.split(',') if s.strip()]

            if not self.monitored_symbols:
                QMessageBox.warning(self, "警告", "请输入有效的股票代码")
                return

            # 获取刷新间隔
            interval = self.interval_spinbox.value()

            # 创建并启动实时数据线程
            self.realtime_thread = RealtimeDataThread(self.monitored_symbols, interval)
            self.realtime_thread.data_updated.connect(self.update_realtime_data)
            self.realtime_thread.error_occurred.connect(self.handle_error)
            self.realtime_thread.start()

            # 更新界面状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.status_label.setText("监控中...")
            self.monitor_count_label.setText(f"监控股票: {len(self.monitored_symbols)}")

            self.logger.info(f"开始监控 {len(self.monitored_symbols)} 只股票")

        except Exception as e:
            self.logger.error(f"开始监控失败: {e}")
            QMessageBox.critical(self, "错误", f"开始监控失败:\n{str(e)}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            if self.realtime_thread and self.realtime_thread.isRunning():
                self.realtime_thread.stop()
                self.realtime_thread.wait()

            # 更新界面状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText("已停止")

            self.logger.info("停止监控")

        except Exception as e:
            self.logger.error(f"停止监控失败: {e}")

    def clear_data(self):
        """清空数据"""
        self.realtime_table.setRowCount(0)
        self.update_time_label.setText("最后更新: 未开始")
        self.logger.info("清空实时数据")

    def update_realtime_data(self, data_list):
        """更新实时数据"""
        try:
            self.realtime_table.setRowCount(len(data_list))

            for row, data in enumerate(data_list):
                # 股票代码
                self.realtime_table.setItem(row, 0, QTableWidgetItem(data.get('symbol', '')))

                # 股票名称
                self.realtime_table.setItem(row, 1, QTableWidgetItem(data.get('name', '')))

                # 最新价
                price = data.get('price', 0.0)
                price_item = QTableWidgetItem(f"{price:.2f}")
                self.realtime_table.setItem(row, 2, price_item)

                # 涨跌额
                change = data.get('change', 0.0)
                change_item = QTableWidgetItem(f"{change:+.2f}")
                if change > 0:
                    change_item.setForeground(QColor("red"))
                elif change < 0:
                    change_item.setForeground(QColor("green"))
                self.realtime_table.setItem(row, 3, change_item)

                # 涨跌幅
                pct_change = data.get('pct_change', 0.0)
                pct_item = QTableWidgetItem(f"{pct_change:+.2f}%")
                if pct_change > 0:
                    pct_item.setForeground(QColor("red"))
                elif pct_change < 0:
                    pct_item.setForeground(QColor("green"))
                self.realtime_table.setItem(row, 4, pct_item)

                # 成交量
                volume = data.get('volume', 0)
                volume_text = f"{volume:,}" if volume > 0 else "0"
                self.realtime_table.setItem(row, 5, QTableWidgetItem(volume_text))

                # 更新时间
                timestamp = data.get('timestamp', datetime.now())
                time_text = timestamp.strftime('%H:%M:%S') if isinstance(timestamp, datetime) else str(timestamp)
                self.realtime_table.setItem(row, 6, QTableWidgetItem(time_text))

                # 数据源
                source = data.get('source', '未知')
                self.realtime_table.setItem(row, 7, QTableWidgetItem(source))

            # 调整列宽
            self.realtime_table.resizeColumnsToContents()

            # 更新状态
            self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
            self.status_label.setText("监控中...")

            # 检查预警
            self.alert_manager.check_alerts(data_list)

        except Exception as e:
            self.logger.error(f"更新实时数据失败: {e}")

    def handle_error(self, error_message):
        """处理错误"""
        self.status_label.setText(f"错误: {error_message}")
        self.logger.error(error_message)

    def closeEvent(self, event):
        """关闭事件"""
        self.stop_monitoring()
        event.accept()

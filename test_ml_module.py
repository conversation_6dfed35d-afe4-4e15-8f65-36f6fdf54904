#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模块测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ml.feature_engineering import FeatureEngineer
from ml.model_manager import ModelManager
from strategies.ml.ml_enhanced_strategy import MLEnhancedStrategy
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def test_ml_module():
    """测试机器学习模块"""
    logger = setup_logger()
    logger.info("开始测试机器学习模块")
    
    try:
        # 获取测试数据
        collector = AKShareCollector()
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=500)).strftime('%Y-%m-%d')
        
        print("正在获取测试数据...")
        data = collector.get_stock_data('000001.SZ', start_date, end_date)
        
        if data is None or data.empty:
            print("无法获取测试数据，使用模拟数据")
            data = generate_mock_data()
        
        data.attrs['symbol'] = '000001.SZ'
        print(f"数据获取成功，共{len(data)}条记录")
        
        # 测试特征工程
        print(f"\n{'='*50}")
        print("测试特征工程模块")
        
        feature_engineer = FeatureEngineer()
        
        print("正在生成特征...")
        features_df = feature_engineer.create_features(data)
        feature_names = feature_engineer.get_feature_names()
        
        print(f"✓ 特征生成完成")
        print(f"  原始数据维度: {data.shape}")
        print(f"  特征数据维度: {features_df.shape}")
        print(f"  生成特征数量: {len(feature_names)}")
        print(f"  前10个特征: {feature_names[:10]}")
        
        # 创建目标变量
        print("\n正在创建目标变量...")
        target_direction = feature_engineer.create_target(data, 'direction', 5, 0.02)
        target_classification = feature_engineer.create_target(data, 'classification', 5, 0.02)
        
        print(f"✓ 目标变量创建完成")
        print(f"  方向目标分布: {target_direction.value_counts().to_dict()}")
        print(f"  分类目标分布: {target_classification.value_counts().to_dict()}")
        
        # 特征选择
        print("\n正在进行特征选择...")
        selected_features = feature_engineer.select_features(
            features_df, target_direction, method='correlation', top_k=20
        )
        print(f"✓ 特征选择完成，选择了{len(selected_features)}个特征")
        print(f"  选择的特征: {selected_features[:10]}...")
        
        # 测试模型管理器
        print(f"\n{'='*50}")
        print("测试模型管理器")
        
        model_manager = ModelManager()
        
        # 创建分类模型
        print("\n创建随机森林分类模型...")
        success = model_manager.create_model(
            'test_rf_classifier', 
            'random_forest_classifier',
            {'n_estimators': 50, 'max_depth': 5}
        )
        
        if success:
            print("✓ 模型创建成功")
            
            # 准备训练数据
            X = features_df[selected_features].dropna()
            y = target_direction[X.index].dropna()
            X = X[y.index]
            
            if len(X) > 100:
                print(f"\n开始训练模型，训练样本: {len(X)}")
                metrics = model_manager.train_model('test_rf_classifier', X, y)
                
                if metrics:
                    print("✓ 模型训练完成")
                    print(f"  训练准确率: {metrics.get('train_accuracy', 0):.3f}")
                    print(f"  测试准确率: {metrics.get('test_accuracy', 0):.3f}")
                    print(f"  交叉验证得分: {metrics.get('cv_mean', 0):.3f} ± {metrics.get('cv_std', 0):.3f}")
                    
                    # 测试预测
                    print("\n测试模型预测...")
                    test_X = X.tail(10)
                    predictions = model_manager.predict('test_rf_classifier', test_X)
                    probabilities = model_manager.predict_proba('test_rf_classifier', test_X)
                    
                    if predictions is not None:
                        print("✓ 预测成功")
                        print(f"  预测结果: {predictions}")
                        if probabilities is not None:
                            print(f"  预测概率: {probabilities[:3]}")
                    
                    # 保存和加载模型
                    print("\n测试模型保存和加载...")
                    if model_manager.save_model('test_rf_classifier'):
                        print("✓ 模型保存成功")
                        
                        # 创建新的模型管理器测试加载
                        new_manager = ModelManager()
                        if new_manager.load_model('test_rf_classifier'):
                            print("✓ 模型加载成功")
                        else:
                            print("❌ 模型加载失败")
                    
                    # 显示特征重要性
                    if 'feature_importance' in metrics:
                        print("\n特征重要性 (Top 10):")
                        for item in metrics['feature_importance'][:10]:
                            print(f"  {item['feature']}: {item['importance']:.4f}")
                
                else:
                    print("❌ 模型训练失败")
            else:
                print("❌ 训练数据不足")
        else:
            print("❌ 模型创建失败")
        
        # 测试ML增强策略
        print(f"\n{'='*50}")
        print("测试ML增强策略")
        
        ml_strategy = MLEnhancedStrategy(
            "测试ML策略",
            {
                'model_type': 'random_forest_classifier',
                'prediction_threshold': 0.6,
                'confidence_threshold': 0.7,
                'min_training_samples': 100
            }
        )
        
        print("✓ ML策略创建成功")
        
        # 生成信号
        print("\n正在生成ML信号...")
        signals = ml_strategy.generate_signals(data)
        
        print(f"✓ 信号生成完成，共生成{len(signals)}个信号")
        
        if signals:
            print("\nML信号详情:")
            for i, signal in enumerate(signals[-3:]):
                print(f"  {i+1}. {signal['direction'].upper()} @ {signal['price']:.2f}")
                print(f"     置信度: {signal['confidence']:.3f}")
                print(f"     原因: {signal['reason']}")
                if 'ml_probability' in signal and signal['ml_probability']:
                    print(f"     ML概率: {signal['ml_probability']:.3f}")
        
        # 获取模型性能
        performance = ml_strategy.get_model_performance()
        print(f"\nML策略性能:")
        print(f"  模型类型: {performance.get('model_type', 'N/A')}")
        print(f"  特征数量: {performance.get('feature_count', 0)}")
        print(f"  选择特征数: {performance.get('selected_features_count', 0)}")
        print(f"  信号数量: {performance.get('signal_count', 0)}")
        
        # 获取策略信息
        strategy_info = ml_strategy.get_strategy_info()
        print(f"\nML策略信息:")
        print(f"  策略类型: {strategy_info.get('strategy_type', 'N/A')}")
        print(f"  使用指标: {', '.join(strategy_info.get('indicators', []))}")
        print(f"  ML配置: {strategy_info.get('ml_config', {})}")
        
        print(f"\n{'='*50}")
        print("机器学习模块测试完成")
        
    except Exception as e:
        logger.error(f"机器学习模块测试失败: {e}")
        print(f"❌ 测试失败: {e}")


def generate_mock_data():
    """生成模拟数据"""
    dates = pd.date_range(start='2023-01-01', end='2024-05-01', freq='D')
    
    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 10.0
    prices = []
    
    for i in range(len(dates)):
        # 添加趋势和随机波动
        trend = 0.0001 * i  # 轻微上升趋势
        noise = np.random.normal(0, 0.02)  # 2%的随机波动
        
        # 添加一些周期性模式
        cycle = 0.001 * np.sin(2 * np.pi * i / 50)  # 50天周期
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise + cycle)
        
        prices.append(max(0.1, price))  # 确保价格为正
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = low + (high - low) * np.random.random()
        volume = int(np.random.normal(1000000, 200000))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': max(100000, volume)
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df


if __name__ == "__main__":
    test_ml_module()

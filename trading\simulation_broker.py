#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟交易接口
"""

import sys
from pathlib import Path
import random
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import uuid

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from trading.base_broker import (
    BaseBroker, Order, Position, AccountInfo, 
    OrderType, OrderSide, OrderStatus
)
from data.collectors.akshare_collector import AKShareCollector


class SimulationBroker(BaseBroker):
    """模拟交易接口"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.initial_cash = config.get('initial_cash', 1000000)  # 初始资金100万
        self.current_cash = self.initial_cash
        self.data_collector = AKShareCollector()
        
        # 初始化账户信息
        self.account_info = AccountInfo(
            account_id="SIM_001",
            total_assets=self.initial_cash,
            available_cash=self.initial_cash,
            market_value=0.0
        )
        
        # 模拟数据
        self.market_prices = {}  # symbol -> current_price
        self.price_update_time = {}  # symbol -> last_update_time
        
    def connect(self) -> bool:
        """连接到模拟交易系统"""
        try:
            self.logger.info("正在连接模拟交易系统...")
            time.sleep(1)  # 模拟连接延迟
            
            self.is_connected = True
            self.logger.info("模拟交易系统连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"连接模拟交易系统失败: {e}")
            return False
    
    def disconnect(self) -> bool:
        """断开连接"""
        try:
            self.is_connected = False
            self.logger.info("已断开模拟交易系统连接")
            return True
            
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            return False
    
    def get_account_info(self) -> Optional[AccountInfo]:
        """获取账户信息"""
        if not self.is_connected:
            return None
        
        try:
            # 更新市值
            total_market_value = 0.0
            for symbol, position in self.positions.items():
                current_price = self.get_current_price(symbol)
                if current_price:
                    position.market_value = position.quantity * current_price
                    position.unrealized_pnl = position.market_value - (position.quantity * position.avg_price)
                    total_market_value += position.market_value
            
            # 更新账户信息
            self.account_info.market_value = total_market_value
            self.account_info.total_assets = self.current_cash + total_market_value
            self.account_info.available_cash = self.current_cash
            self.account_info.update_time = datetime.now()
            
            return self.account_info
            
        except Exception as e:
            self.logger.error(f"获取账户信息失败: {e}")
            return None
    
    def get_positions(self) -> Dict[str, Position]:
        """获取持仓信息"""
        if not self.is_connected:
            return {}
        
        # 更新持仓市值
        for symbol, position in self.positions.items():
            current_price = self.get_current_price(symbol)
            if current_price:
                position.market_value = position.quantity * current_price
                position.unrealized_pnl = position.market_value - (position.quantity * position.avg_price)
                position.update_time = datetime.now()
        
        return self.positions
    
    def place_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                   quantity: int, price: float = None) -> Optional[str]:
        """下单"""
        if not self.is_connected:
            self.logger.error("未连接到交易系统")
            return None
        
        try:
            # 验证订单
            is_valid, error_msg = self.validate_order(symbol, side, quantity, price)
            if not is_valid:
                self.logger.error(f"订单验证失败: {error_msg}")
                return None
            
            # 生成订单ID
            order_id = f"SIM_{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:6]}"
            
            # 获取当前价格
            current_price = self.get_current_price(symbol)
            if not current_price:
                self.logger.error(f"无法获取{symbol}的当前价格")
                return None
            
            # 确定成交价格
            if order_type == OrderType.MARKET:
                fill_price = current_price
            else:
                fill_price = price or current_price
            
            # 创建订单
            order = Order(order_id, symbol, side, order_type, quantity, fill_price)
            
            # 模拟订单处理
            success = self._process_order(order, current_price)
            
            if success:
                self.orders[order_id] = order
                self.logger.info(f"订单提交成功: {order_id}")
                return order_id
            else:
                self.logger.error(f"订单处理失败: {order_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        if order_id not in self.orders:
            self.logger.error(f"订单不存在: {order_id}")
            return False
        
        order = self.orders[order_id]
        if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
            self.logger.error(f"订单状态不允许撤销: {order.status}")
            return False
        
        order.status = OrderStatus.CANCELLED
        order.update_time = datetime.now()
        
        self.logger.info(f"订单撤销成功: {order_id}")
        return True
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        return self.orders.get(order_id)
    
    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        orders = list(self.orders.values())
        
        if symbol:
            orders = [o for o in orders if o.symbol == symbol]
        
        if status:
            orders = [o for o in orders if o.status == status]
        
        return orders
    
    def get_trades(self, symbol: str = None, start_date: datetime = None,
                  end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        trades = []
        
        for order in self.orders.values():
            if order.status == OrderStatus.FILLED:
                if symbol and order.symbol != symbol:
                    continue
                
                if start_date and order.update_time < start_date:
                    continue
                
                if end_date and order.update_time > end_date:
                    continue
                
                trade = {
                    'trade_id': f"T_{order.order_id}",
                    'order_id': order.order_id,
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'quantity': order.filled_quantity,
                    'price': order.avg_fill_price,
                    'amount': order.filled_quantity * order.avg_fill_price,
                    'commission': order.commission,
                    'trade_time': order.update_time
                }
                trades.append(trade)
        
        return trades
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            # 检查缓存
            if symbol in self.market_prices:
                last_update = self.price_update_time.get(symbol, datetime.min)
                if datetime.now() - last_update < timedelta(seconds=30):
                    return self.market_prices[symbol]
            
            # 尝试从数据源获取实时价格
            try:
                data = self.data_collector.get_stock_data(symbol, 
                    (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                    datetime.now().strftime('%Y-%m-%d'))
                
                if data is not None and not data.empty:
                    current_price = float(data['close'].iloc[-1])
                    self.market_prices[symbol] = current_price
                    self.price_update_time[symbol] = datetime.now()
                    return current_price
            except:
                pass
            
            # 如果无法获取实时价格，使用模拟价格
            if symbol not in self.market_prices:
                # 生成随机价格（10-50元之间）
                self.market_prices[symbol] = random.uniform(10.0, 50.0)
                self.price_update_time[symbol] = datetime.now()
            else:
                # 模拟价格波动（±2%）
                old_price = self.market_prices[symbol]
                change_rate = random.uniform(-0.02, 0.02)
                new_price = old_price * (1 + change_rate)
                self.market_prices[symbol] = max(0.01, new_price)
                self.price_update_time[symbol] = datetime.now()
            
            return self.market_prices[symbol]
            
        except Exception as e:
            self.logger.error(f"获取{symbol}价格失败: {e}")
            return None
    
    def _process_order(self, order: Order, current_price: float) -> bool:
        """处理订单"""
        try:
            # 模拟订单延迟
            time.sleep(0.1)
            
            # 计算成交价格（添加滑点）
            slippage_rate = self.config.get('slippage_rate', 0.001)
            if order.side == OrderSide.BUY:
                fill_price = current_price * (1 + slippage_rate)
            else:
                fill_price = current_price * (1 - slippage_rate)
            
            # 限价单检查
            if order.order_type == OrderType.LIMIT:
                if order.side == OrderSide.BUY and fill_price > order.price:
                    order.status = OrderStatus.PENDING
                    return True
                elif order.side == OrderSide.SELL and fill_price < order.price:
                    order.status = OrderStatus.PENDING
                    return True
                fill_price = order.price
            
            # 计算手续费
            amount = order.quantity * fill_price
            commission = self.calculate_commission(amount)
            
            # 更新订单状态
            order.filled_quantity = order.quantity
            order.avg_fill_price = fill_price
            order.commission = commission
            order.status = OrderStatus.FILLED
            order.update_time = datetime.now()
            
            # 更新持仓和资金
            if order.side == OrderSide.BUY:
                self._update_position_buy(order.symbol, order.quantity, fill_price)
                self.current_cash -= (amount + commission)
            else:
                self._update_position_sell(order.symbol, order.quantity, fill_price)
                self.current_cash += (amount - commission)
            
            self.logger.info(f"订单成交: {order.order_id}, 价格: {fill_price:.2f}, 手续费: {commission:.2f}")
            return True
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            order.error_message = str(e)
            self.logger.error(f"订单处理失败: {e}")
            return False
    
    def _update_position_buy(self, symbol: str, quantity: int, price: float):
        """更新买入持仓"""
        if symbol in self.positions:
            position = self.positions[symbol]
            total_cost = position.quantity * position.avg_price + quantity * price
            total_quantity = position.quantity + quantity
            position.avg_price = total_cost / total_quantity
            position.quantity = total_quantity
        else:
            self.positions[symbol] = Position(symbol, quantity, price, quantity * price, 0.0)
    
    def _update_position_sell(self, symbol: str, quantity: int, price: float):
        """更新卖出持仓"""
        if symbol in self.positions:
            position = self.positions[symbol]
            position.quantity -= quantity
            if position.quantity <= 0:
                del self.positions[symbol]

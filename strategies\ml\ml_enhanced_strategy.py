#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习增强策略
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from ml.feature_engineering import FeatureEngineer
from ml.model_manager import ModelManager
from utils.logger import get_logger


class MLEnhancedStrategy(BaseStrategy):
    """机器学习增强策略"""
    
    def __init__(self, name: str = "ML增强策略", config: Dict[str, Any] = None):
        default_params = {
            'model_type': 'random_forest_classifier',  # 模型类型
            'prediction_threshold': 0.6,  # 预测阈值
            'confidence_threshold': 0.7,  # 置信度阈值
            'lookback_periods': [5, 10, 20],  # 回看周期
            'target_type': 'direction',  # 目标类型
            'target_periods': 5,  # 目标周期
            'target_threshold': 0.02,  # 目标阈值
            'retrain_frequency': 30,  # 重训练频率（天）
            'min_training_samples': 500,  # 最小训练样本数
            'feature_selection': True,  # 是否进行特征选择
            'top_features': 50,  # 选择的特征数量
            'stop_loss': 0.05,  # 止损比例
            'take_profit': 0.15,  # 止盈比例
        }
        
        if config:
            default_params.update(config)
        
        super().__init__(name, default_params)
        
        self.feature_engineer = FeatureEngineer()
        self.model_manager = ModelManager()
        self.model_name = f"{name}_model"
        self.last_training_date = None
        self.selected_features = []
        self.signal_count = 0
        
        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化机器学习模型"""
        try:
            # 尝试加载已有模型
            if not self.model_manager.load_model(self.model_name):
                # 创建新模型
                self.model_manager.create_model(
                    self.model_name,
                    self.parameters['model_type']
                )
                self.logger.info(f"创建新的ML模型: {self.model_name}")
            else:
                self.logger.info(f"加载已有ML模型: {self.model_name}")
                
        except Exception as e:
            self.logger.error(f"初始化ML模型失败: {e}")
    
    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成ML增强的交易信号"""
        signals = []
        
        if data is None or data.empty or len(data) < 100:
            self.logger.warning("数据不足，无法生成ML信号")
            return signals
        
        try:
            # 检查是否需要重新训练模型
            if self._should_retrain(data):
                self._train_model(data)
            
            # 特征工程
            features_df = self.feature_engineer.create_features(
                data, self.parameters['lookback_periods']
            )
            
            # 获取最新的特征数据
            latest_features = features_df.iloc[-1:].copy()
            
            if self.selected_features:
                latest_features = latest_features[self.selected_features]
            
            # 模型预测
            prediction = self.model_manager.predict(self.model_name, latest_features)
            prediction_proba = self.model_manager.predict_proba(self.model_name, latest_features)
            
            if prediction is None or len(prediction) == 0:
                self.logger.warning("ML模型预测失败")
                return signals
            
            # 生成交易信号
            signal = self._generate_signal_from_prediction(
                data, prediction[0], prediction_proba[0] if prediction_proba is not None else None
            )
            
            if signal:
                signals.append(signal)
                self.save_signal(signal)
                self.signal_count += 1
                
                self.logger.info(f"生成ML信号: {signal['direction']} {signal['symbol']} @ {signal['price']:.2f}")
            
            return signals
            
        except Exception as e:
            self.logger.error(f"生成ML信号失败: {e}")
            return signals
    
    def _should_retrain(self, data: pd.DataFrame) -> bool:
        """判断是否需要重新训练模型"""
        try:
            if self.last_training_date is None:
                return True
            
            # 检查时间间隔
            current_date = data.index[-1]
            days_since_training = (current_date - self.last_training_date).days
            
            if days_since_training >= self.parameters['retrain_frequency']:
                return True
            
            # 检查数据量
            if len(data) < self.parameters['min_training_samples']:
                return False
            
            return False
            
        except Exception:
            return True
    
    def _train_model(self, data: pd.DataFrame) -> bool:
        """训练机器学习模型"""
        try:
            self.logger.info("开始训练ML模型...")
            
            # 特征工程
            features_df = self.feature_engineer.create_features(
                data, self.parameters['lookback_periods']
            )
            
            # 创建目标变量
            target = self.feature_engineer.create_target(
                data,
                self.parameters['target_type'],
                self.parameters['target_periods'],
                self.parameters['target_threshold']
            )
            
            # 对齐数据
            common_index = features_df.index.intersection(target.index)
            features_df = features_df.loc[common_index]
            target = target.loc[common_index]
            
            # 移除包含NaN的行
            valid_mask = ~(features_df.isnull().any(axis=1) | target.isnull())
            features_df = features_df[valid_mask]
            target = target[valid_mask]
            
            if len(features_df) < self.parameters['min_training_samples']:
                self.logger.warning(f"训练样本不足: {len(features_df)} < {self.parameters['min_training_samples']}")
                return False
            
            # 特征选择
            if self.parameters['feature_selection']:
                feature_names = self.feature_engineer.get_feature_names()
                available_features = [f for f in feature_names if f in features_df.columns]
                
                if available_features:
                    self.selected_features = self.feature_engineer.select_features(
                        features_df[available_features], target, 
                        method='correlation', top_k=self.parameters['top_features']
                    )
                    features_df = features_df[self.selected_features]
            
            # 训练模型
            metrics = self.model_manager.train_model(
                self.model_name, features_df, target
            )
            
            if metrics:
                self.logger.info(f"模型训练完成，测试准确率: {metrics.get('test_accuracy', 'N/A'):.3f}")
                
                # 保存模型
                self.model_manager.save_model(self.model_name)
                self.last_training_date = data.index[-1]
                
                return True
            else:
                self.logger.error("模型训练失败")
                return False
                
        except Exception as e:
            self.logger.error(f"训练ML模型失败: {e}")
            return False
    
    def _generate_signal_from_prediction(self, data: pd.DataFrame, prediction: float, 
                                       prediction_proba: np.ndarray = None) -> Optional[Dict[str, Any]]:
        """根据ML预测生成交易信号"""
        try:
            current_price = data['close'].iloc[-1]
            current_date = data.index[-1]
            symbol = data.attrs.get('symbol', 'UNKNOWN')
            
            # 计算置信度
            confidence = 0.5
            if prediction_proba is not None:
                if self.parameters['target_type'] == 'direction':
                    # 二分类
                    confidence = max(prediction_proba)
                else:
                    # 多分类
                    confidence = max(prediction_proba)
            
            # 检查置信度阈值
            if confidence < self.parameters['confidence_threshold']:
                return None
            
            # 根据预测结果生成信号
            signal = None
            
            if self.parameters['target_type'] == 'direction':
                # 二分类：0=下跌，1=上涨
                if prediction == 1 and prediction_proba[1] > self.parameters['prediction_threshold']:
                    signal = {
                        'symbol': symbol,
                        'signal_type': 'ML_ENHANCED',
                        'direction': 'buy',
                        'price': current_price,
                        'quantity': 0,
                        'timestamp': current_date,
                        'confidence': confidence,
                        'reason': f'ML预测上涨，概率:{prediction_proba[1]:.3f}',
                        'stop_loss_price': current_price * (1 - self.parameters['stop_loss']),
                        'take_profit_price': current_price * (1 + self.parameters['take_profit']),
                        'ml_prediction': prediction,
                        'ml_probability': prediction_proba[1] if prediction_proba is not None else None
                    }
                
                elif prediction == 0 and prediction_proba[0] > self.parameters['prediction_threshold']:
                    if self._has_position(symbol):
                        signal = {
                            'symbol': symbol,
                            'signal_type': 'ML_ENHANCED',
                            'direction': 'sell',
                            'price': current_price,
                            'quantity': 0,
                            'timestamp': current_date,
                            'confidence': confidence,
                            'reason': f'ML预测下跌，概率:{prediction_proba[0]:.3f}',
                            'stop_loss_price': 0,
                            'take_profit_price': 0,
                            'ml_prediction': prediction,
                            'ml_probability': prediction_proba[0] if prediction_proba is not None else None
                        }
            
            elif self.parameters['target_type'] == 'classification':
                # 三分类：0=下跌，1=横盘，2=上涨
                if prediction == 2 and prediction_proba[2] > self.parameters['prediction_threshold']:
                    signal = {
                        'symbol': symbol,
                        'signal_type': 'ML_ENHANCED',
                        'direction': 'buy',
                        'price': current_price,
                        'quantity': 0,
                        'timestamp': current_date,
                        'confidence': confidence,
                        'reason': f'ML预测强势上涨，概率:{prediction_proba[2]:.3f}',
                        'stop_loss_price': current_price * (1 - self.parameters['stop_loss']),
                        'take_profit_price': current_price * (1 + self.parameters['take_profit']),
                        'ml_prediction': prediction,
                        'ml_probability': prediction_proba[2] if prediction_proba is not None else None
                    }
                
                elif prediction == 0 and prediction_proba[0] > self.parameters['prediction_threshold']:
                    if self._has_position(symbol):
                        signal = {
                            'symbol': symbol,
                            'signal_type': 'ML_ENHANCED',
                            'direction': 'sell',
                            'price': current_price,
                            'quantity': 0,
                            'timestamp': current_date,
                            'confidence': confidence,
                            'reason': f'ML预测下跌，概率:{prediction_proba[0]:.3f}',
                            'stop_loss_price': 0,
                            'take_profit_price': 0,
                            'ml_prediction': prediction,
                            'ml_probability': prediction_proba[0] if prediction_proba is not None else None
                        }
            
            if signal:
                # 验证信号
                if self.validate_signal(signal):
                    # 计算仓位大小
                    signal['quantity'] = self.calculate_position_size(
                        signal, current_price, 1000000
                    )
                    
                    # 应用风险管理
                    signal = self.apply_risk_management(signal)
                    
                    return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"生成ML信号失败: {e}")
            return None
    
    def _has_position(self, symbol: str) -> bool:
        """检查是否有持仓"""
        return symbol in self.positions and self.positions[symbol]['quantity'] > 0
    
    def calculate_position_size(self, signal: Dict[str, Any], current_price: float, 
                               available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于ML置信度的仓位计算
            base_position = available_capital * 0.1  # 基础仓位10%
            confidence_multiplier = signal.get('confidence', 0.5)
            
            # 根据置信度调整仓位
            adjusted_position = base_position * confidence_multiplier
            
            # 计算股数
            quantity = int(adjusted_position / current_price / 100) * 100
            
            return max(100, quantity)
            
        except Exception:
            return 100
    
    def get_model_performance(self) -> Dict[str, Any]:
        """获取模型性能指标"""
        try:
            model_info = self.model_manager.get_model_info(self.model_name)
            
            performance = {
                'model_name': self.model_name,
                'model_type': model_info.get('model_type', 'unknown'),
                'feature_count': model_info.get('feature_count', 0),
                'last_training_date': self.last_training_date,
                'selected_features_count': len(self.selected_features),
                'signal_count': self.signal_count
            }
            
            return performance
            
        except Exception as e:
            self.logger.error(f"获取模型性能失败: {e}")
            return {}
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': 'ML增强策略',
            'indicators': ['ML模型', '技术指标特征', '价格特征', '成交量特征'],
            'signal_conditions': {
                'buy': f'ML预测概率 > {self.parameters["prediction_threshold"]}',
                'sell': f'ML预测下跌概率 > {self.parameters["prediction_threshold"]}'
            },
            'risk_management': {
                'stop_loss': f"{self.parameters['stop_loss']*100:.1f}%",
                'take_profit': f"{self.parameters['take_profit']*100:.1f}%"
            },
            'ml_config': {
                'model_type': self.parameters['model_type'],
                'target_type': self.parameters['target_type'],
                'feature_selection': self.parameters['feature_selection'],
                'retrain_frequency': f"{self.parameters['retrain_frequency']}天"
            }
        })
        return info

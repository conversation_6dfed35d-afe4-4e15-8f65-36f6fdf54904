#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理界面组件
"""

import sys
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QComboBox,
    QTableWidget, QTableWidgetItem, QTabWidget,
    QProgressBar, QTextEdit, QSplitter, QHeaderView,
    QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from data.collectors.multi_source_manager import MultiSourceManager, DataSourceStatus


class SourceStatusUpdateThread(QThread):
    """数据源状态更新线程"""
    status_updated = pyqtSignal(dict)

    def __init__(self, manager: MultiSourceManager):
        super().__init__()
        self.manager = manager
        self.running = True

    def run(self):
        """运行状态更新"""
        while self.running:
            try:
                self.manager.check_all_sources()
                status = self.manager.get_all_source_status()
                self.status_updated.emit(status)
                self.msleep(5000)  # 每5秒更新一次
            except Exception as e:
                print(f"状态更新失败: {e}")
                self.msleep(1000)

    def stop(self):
        """停止更新"""
        self.running = False


class DataSourceStatusWidget(QWidget):
    """数据源状态监控组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataSourceStatusWidget")
        self.manager = MultiSourceManager()
        self.init_ui()
        self.start_status_monitoring()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("数据源状态监控")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 控制按钮
        control_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新状态")
        self.refresh_btn.clicked.connect(self.refresh_status)
        control_layout.addWidget(self.refresh_btn)

        self.test_all_btn = QPushButton("测试所有数据源")
        self.test_all_btn.clicked.connect(self.test_all_sources)
        control_layout.addWidget(self.test_all_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 状态表格
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(7)
        self.status_table.setHorizontalHeaderLabels([
            "数据源", "状态", "延迟(ms)", "成功率", "总请求", "成功请求", "最后检查"
        ])
        self.status_table.setAlternatingRowColors(True)
        self.status_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.status_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置表格背景为蓝色，确保白色文字可见
        self.status_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.status_table)

        # 初始化状态
        self.update_status_table()

    def start_status_monitoring(self):
        """开始状态监控"""
        self.status_thread = SourceStatusUpdateThread(self.manager)
        self.status_thread.status_updated.connect(self.on_status_updated)
        self.status_thread.start()

    def stop_status_monitoring(self):
        """停止状态监控"""
        if hasattr(self, 'status_thread'):
            self.status_thread.stop()
            self.status_thread.wait()

    def on_status_updated(self, status_dict):
        """状态更新回调"""
        self.update_status_table()

    def refresh_status(self):
        """刷新状态"""
        self.manager.check_all_sources()
        self.update_status_table()
        self.logger.info("数据源状态已刷新")

    def test_all_sources(self):
        """测试所有数据源"""
        try:
            self.test_all_btn.setEnabled(False)
            self.test_all_btn.setText("测试中...")

            # 测试所有数据源
            for source_name in self.manager.data_sources:
                self.manager.check_source_status(source_name)

            self.update_status_table()

            QMessageBox.information(self, "测试完成", "所有数据源测试完成")

        except Exception as e:
            self.logger.error(f"测试数据源失败: {e}")
            QMessageBox.critical(self, "错误", f"测试失败:\n{str(e)}")

        finally:
            self.test_all_btn.setEnabled(True)
            self.test_all_btn.setText("测试所有数据源")

    def update_status_table(self):
        """更新状态表格"""
        try:
            status_dict = self.manager.get_all_source_status()
            self.status_table.setRowCount(len(status_dict))

            for row, (source_name, status_info) in enumerate(status_dict.items()):
                # 数据源名称
                name_item = QTableWidgetItem(source_name)
                self.status_table.setItem(row, 0, name_item)

                # 状态
                status = status_info.get('status', DataSourceStatus.OFFLINE)
                status_text = status.value if hasattr(status, 'value') else str(status)
                status_item = QTableWidgetItem(status_text)

                # 根据状态设置颜色
                if status == DataSourceStatus.ONLINE:
                    status_item.setBackground(QColor("#E8F5E8"))  # 绿色
                elif status == DataSourceStatus.OFFLINE:
                    status_item.setBackground(QColor("#FFEBEE"))  # 红色
                elif status == DataSourceStatus.ERROR:
                    status_item.setBackground(QColor("#FFF3E0"))  # 橙色
                else:
                    status_item.setBackground(QColor("#F5F5F5"))  # 灰色

                self.status_table.setItem(row, 1, status_item)

                # 延迟
                latency = status_info.get('latency', 0)
                latency_item = QTableWidgetItem(f"{latency}")
                self.status_table.setItem(row, 2, latency_item)

                # 成功率
                success_rate = status_info.get('success_rate', 0.0)
                rate_item = QTableWidgetItem(f"{success_rate:.1%}")
                self.status_table.setItem(row, 3, rate_item)

                # 总请求数
                total_requests = status_info.get('total_requests', 0)
                total_item = QTableWidgetItem(str(total_requests))
                self.status_table.setItem(row, 4, total_item)

                # 成功请求数
                successful_requests = status_info.get('successful_requests', 0)
                success_item = QTableWidgetItem(str(successful_requests))
                self.status_table.setItem(row, 5, success_item)

                # 最后检查时间
                last_check = status_info.get('last_check')
                if last_check:
                    check_text = last_check.strftime('%H:%M:%S')
                else:
                    check_text = "未检查"
                check_item = QTableWidgetItem(check_text)
                self.status_table.setItem(row, 6, check_item)

            # 调整列宽
            self.status_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新状态表格失败: {e}")


class DataSourceConfigWidget(QWidget):
    """数据源配置组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataSourceConfigWidget")
        self.manager = MultiSourceManager()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("数据源配置")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 主要数据源配置
        primary_group = QGroupBox("主要数据源")
        primary_layout = QGridLayout(primary_group)

        当前主要数据源_label = QLabel("当前主要数据源:")


        当前主要数据源_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")


        primary_layout.addWidget(当前主要数据源_label, 0, 0)
        self.primary_combo = QComboBox()
        self.primary_combo.addItems(list(self.manager.data_sources.keys()))
        if self.manager.primary_source:
            self.primary_combo.setCurrentText(self.manager.primary_source)
        primary_layout.addWidget(self.primary_combo, 0, 1)

        set_primary_btn = QPushButton("设置为主要数据源")
        set_primary_btn.clicked.connect(self.set_primary_source)
        primary_layout.addWidget(set_primary_btn, 0, 2)

        layout.addWidget(primary_group)

        # 备用数据源配置
        fallback_group = QGroupBox("备用数据源")
        fallback_layout = QVBoxLayout(fallback_group)

        fallback_info = QLabel("备用数据源列表（按优先级排序）:")
        fallback_info.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        fallback_layout.addWidget(fallback_info)

        self.fallback_list = QTextEdit()
        self.fallback_list.setMaximumHeight(100)
        fallback_sources_text = "\n".join(self.manager.fallback_sources)
        self.fallback_list.setPlainText(fallback_sources_text)
        fallback_layout.addWidget(self.fallback_list)

        update_fallback_btn = QPushButton("更新备用数据源")
        update_fallback_btn.clicked.connect(self.update_fallback_sources)
        fallback_layout.addWidget(update_fallback_btn)

        layout.addWidget(fallback_group)

        # 数据源性能报告
        report_group = QGroupBox("性能报告")
        report_layout = QVBoxLayout(report_group)

        generate_report_btn = QPushButton("生成性能报告")
        generate_report_btn.clicked.connect(self.generate_performance_report)
        report_layout.addWidget(generate_report_btn)

        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setMaximumHeight(200)
        report_layout.addWidget(self.report_text)

        layout.addWidget(report_group)

        layout.addStretch()

    def set_primary_source(self):
        """设置主要数据源"""
        try:
            selected_source = self.primary_combo.currentText()
            self.manager.set_primary_source(selected_source)
            QMessageBox.information(self, "成功", f"已设置 {selected_source} 为主要数据源")
            self.logger.info(f"设置主要数据源: {selected_source}")
        except Exception as e:
            self.logger.error(f"设置主要数据源失败: {e}")
            QMessageBox.critical(self, "错误", f"设置失败:\n{str(e)}")

    def update_fallback_sources(self):
        """更新备用数据源"""
        try:
            fallback_text = self.fallback_list.toPlainText().strip()
            if fallback_text:
                fallback_sources = [line.strip() for line in fallback_text.split('\n') if line.strip()]
                self.manager.set_fallback_sources(fallback_sources)
                QMessageBox.information(self, "成功", "备用数据源已更新")
                self.logger.info(f"更新备用数据源: {fallback_sources}")
            else:
                QMessageBox.warning(self, "警告", "请输入备用数据源列表")
        except Exception as e:
            self.logger.error(f"更新备用数据源失败: {e}")
            QMessageBox.critical(self, "错误", f"更新失败:\n{str(e)}")

    def generate_performance_report(self):
        """生成性能报告"""
        try:
            report = self.manager.get_source_performance_report()

            report_text = "数据源性能报告\n"
            report_text += "=" * 50 + "\n\n"

            for source_name, metrics in report.items():
                report_text += f"数据源: {source_name}\n"
                report_text += f"  状态: {metrics['status']}\n"
                report_text += f"  成功率: {metrics['success_rate']}\n"
                report_text += f"  平均延迟: {metrics['average_latency']}\n"
                report_text += f"  总请求数: {metrics['total_requests']}\n"
                report_text += f"  成功请求数: {metrics['successful_requests']}\n"
                report_text += f"  错误次数: {metrics['error_count']}\n"
                report_text += f"  最后检查: {metrics['last_check']}\n"
                report_text += "-" * 30 + "\n"

            self.report_text.setPlainText(report_text)
            self.logger.info("性能报告已生成")

        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成报告失败:\n{str(e)}")


class DataSourceManagerWidget(QWidget):
    """数据源管理主组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataSourceManagerWidget")
        self.init_ui()
        self.logger.info("数据源管理组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("数据源管理中心")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        layout.addWidget(title_label)

        # 创建标签页
        tab_widget = QTabWidget()

        # 状态监控标签页
        self.status_widget = DataSourceStatusWidget()
        tab_widget.addTab(self.status_widget, "状态监控")

        # 配置管理标签页
        self.config_widget = DataSourceConfigWidget()
        tab_widget.addTab(self.config_widget, "配置管理")

        layout.addWidget(tab_widget)

    def closeEvent(self, event):
        """关闭事件"""
        # 停止状态监控线程
        if hasattr(self.status_widget, 'status_thread'):
            self.status_widget.stop_status_monitoring()
        event.accept()

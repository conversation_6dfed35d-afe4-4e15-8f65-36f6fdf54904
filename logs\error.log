2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:get_available_strategies:93 | 获取策略信息失败 DoubleMA: 'DoubleMovingAverageStrategy' object has no attribute 'get_strategy_info'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:get_available_strategies:93 | 获取策略信息失败 MACD: Can't instantiate abstract class MACDStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:get_available_strategies:93 | 获取策略信息失败 RSI: Can't instantiate abstract class RSIStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:get_available_strategies:93 | 获取策略信息失败 Bollinger: Can't instantiate abstract class BollingerBandsStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: MovingAverageStrategy.__init__() got an unexpected keyword argument 'config'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: DoubleMovingAverageStrategy.__init__() got an unexpected keyword argument 'config'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: Can't instantiate abstract class MACDStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: Can't instantiate abstract class RSIStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: Can't instantiate abstract class BollingerBandsStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: MovingAverageStrategy.__init__() got an unexpected keyword argument 'config'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: Can't instantiate abstract class MACDStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy:69 | 创建策略失败: Can't instantiate abstract class RSIStrategy without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:07:24 | ERROR | strategies.strategy_factory:create_strategy_combination:185 | 没有有效的策略可以组合
2025-05-25 00:09:14 | ERROR | strategies.strategy_factory:get_available_strategies:98 | 获取策略信息失败 DoubleMA: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:09:14 | ERROR | strategies.strategy_factory:get_available_strategies:98 | 获取策略信息失败 MACD: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:09:14 | ERROR | strategies.strategy_factory:get_available_strategies:98 | 获取策略信息失败 RSI: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:09:14 | ERROR | strategies.strategy_factory:get_available_strategies:98 | 获取策略信息失败 Bollinger: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:09:14 | ERROR | __main__:test_strategies:85 | 策略测试失败 MA: 'reason'
2025-05-25 00:09:14 | ERROR | __main__:test_strategies:85 | 策略测试失败 DoubleMA: 'reason'
2025-05-25 00:09:14 | ERROR | strategies.technical.macd_strategy:generate_signals:140 | 生成MACD信号失败: 'MACDStrategy' object has no attribute 'signal_count'
2025-05-25 00:09:14 | ERROR | strategies.technical.rsi_strategy:generate_signals:137 | 生成RSI信号失败: 'RSIStrategy' object has no attribute 'signal_count'
2025-05-25 00:09:14 | ERROR | strategies.technical.bollinger_strategy:generate_signals:114 | 生成布林带信号失败: 'BollingerBandsStrategy' object has no attribute 'signal_count'
2025-05-25 00:09:14 | ERROR | strategies.strategy_factory:create_strategy_combination:198 | 创建策略组合失败: Can't instantiate abstract class StrategyCombination without an implementation for abstract method 'calculate_position_size'
2025-05-25 00:23:26 | ERROR | ml.feature_engineering:_add_technical_features:142 | 添加技术指标特征失败: 'TechnicalIndicators' object has no attribute 'kdj'
2025-05-25 00:23:26 | ERROR | __main__:test_simple_ml:214 | 简化机器学习模块测试失败: "None of [DatetimeIndex(['2024-07-29', '2024-07-30', '2024-07-31', '2024-08-01',\n               '2024-08-02', '2024-08-05', '2024-08-06', '2024-08-07',\n               '2024-08-08', '2024-08-09',\n               ...\n               '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15',\n               '2025-05-16', '2025-05-19', '2025-05-20', '2025-05-21',\n               '2025-05-22', '2025-05-23'],\n              dtype='datetime64[ns]', name='date', length=197, freq=None)] are in the [columns]"
2025-05-25 00:24:26 | ERROR | ml.simple_ml:train_model:217 | 训练模型失败: "None of [DatetimeIndex(['2024-07-29', '2024-07-30', '2024-07-31', '2024-08-01',\n               '2024-08-02', '2024-08-05', '2024-08-06', '2024-08-07',\n               '2024-08-08', '2024-08-09',\n               ...\n               '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15',\n               '2025-05-16', '2025-05-19', '2025-05-20', '2025-05-21',\n               '2025-05-22', '2025-05-23'],\n              dtype='datetime64[ns]', name='date', length=197, freq=None)] are in the [columns]"
2025-05-25 00:24:26 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 测试简化ML策略_simple_model
2025-05-25 00:24:27 | ERROR | ml.simple_ml:train_model:217 | 训练模型失败: "None of [DatetimeIndex(['2024-07-29', '2024-07-30', '2024-07-31', '2024-08-01',\n               '2024-08-02', '2024-08-05', '2024-08-06', '2024-08-07',\n               '2024-08-08', '2024-08-09',\n               ...\n               '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15',\n               '2025-05-16', '2025-05-19', '2025-05-20', '2025-05-21',\n               '2025-05-22', '2025-05-23'],\n              dtype='datetime64[ns]', name='date', length=197, freq=None)] are in the [columns]"
2025-05-25 00:24:27 | ERROR | strategies.ml.simple_ml_strategy:_train_model:212 | 简化ML模型训练失败
2025-05-25 00:24:27 | ERROR | ml.simple_ml:predict:230 | 模型未训练: 测试简化ML策略_simple_model
2025-05-25 00:24:27 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 DoubleMA: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:24:27 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 MACD: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:24:27 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 RSI: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:24:27 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 Bollinger: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:24:27 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 简化ML策略_simple_model
2025-05-25 00:24:27 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 SimpleML: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:24:27 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 工厂创建的简化ML策略_simple_model
2025-05-25 00:24:27 | ERROR | ml.simple_ml:train_model:217 | 训练模型失败: "None of [DatetimeIndex(['2024-07-29', '2024-07-30', '2024-07-31', '2024-08-01',\n               '2024-08-02', '2024-08-05', '2024-08-06', '2024-08-07',\n               '2024-08-08', '2024-08-09',\n               ...\n               '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15',\n               '2025-05-16', '2025-05-19', '2025-05-20', '2025-05-21',\n               '2025-05-22', '2025-05-23'],\n              dtype='datetime64[ns]', name='date', length=197, freq=None)] are in the [columns]"
2025-05-25 00:24:27 | ERROR | strategies.ml.simple_ml_strategy:_train_model:212 | 简化ML模型训练失败
2025-05-25 00:24:27 | ERROR | ml.simple_ml:predict:230 | 模型未训练: 工厂创建的简化ML策略_simple_model
2025-05-25 00:25:23 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 测试简化ML策略_simple_model
2025-05-25 00:25:23 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 DoubleMA: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:25:23 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 MACD: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:25:23 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 RSI: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:25:23 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 Bollinger: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:25:23 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 简化ML策略_simple_model
2025-05-25 00:25:23 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 SimpleML: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:25:23 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 工厂创建的简化ML策略_simple_model
2025-05-25 00:53:29 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 DoubleMA: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:53:29 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 MACD: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:53:29 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 RSI: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:53:29 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 Bollinger: 'super' object has no attribute 'get_strategy_info'
2025-05-25 00:53:29 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 简化ML策略_simple_model
2025-05-25 00:53:29 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 SimpleML: 'super' object has no attribute 'get_strategy_info'
2025-05-25 10:56:54 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 DoubleMA: 'super' object has no attribute 'get_strategy_info'
2025-05-25 10:56:54 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 MACD: 'super' object has no attribute 'get_strategy_info'
2025-05-25 10:56:54 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 RSI: 'super' object has no attribute 'get_strategy_info'
2025-05-25 10:56:54 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 Bollinger: 'super' object has no attribute 'get_strategy_info'
2025-05-25 10:56:54 | ERROR | ml.simple_ml:load_model:294 | 模型配置文件不存在: 简化ML策略_simple_model
2025-05-25 10:56:54 | ERROR | strategies.strategy_factory:get_available_strategies:100 | 获取策略信息失败 SimpleML: 'super' object has no attribute 'get_strategy_info'
2025-05-25 15:26:59 | ERROR | gui.main_window:show_settings:305 | 打开设置界面失败: 'SettingsWidget' object has no attribute 'setModal'
2025-05-25 21:32:26 | ERROR | data.collectors.akshare_collector:connect:34 | AKShare连接失败: HTTPSConnectionPool(host='82.push2.eastmoney.com', port=443): Read timed out. (read timeout=15)
2025-05-25 21:36:42 | ERROR | data.collectors.akshare_collector:connect:34 | AKShare连接失败: HTTPSConnectionPool(host='82.push2.eastmoney.com', port=443): Read timed out. (read timeout=15)

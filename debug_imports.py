#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试主窗口导入问题
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_individual_imports():
    """逐个测试主窗口的依赖导入"""
    print("=" * 60)
    print("🔍 逐个测试主窗口依赖模块导入")
    print("=" * 60)
    
    # 主窗口的导入列表（按照main_window.py中的顺序）
    imports_to_test = [
        ("PyQt5基础", "from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QMenuBar, QStatusBar, QToolBar, QAction, QMessageBox, QSplitter, QLabel, QFileDialog"),
        ("PyQt5核心", "from PyQt5.QtCore import Qt, QTimer, pyqtSignal"),
        ("PyQt5图形", "from PyQt5.QtGui import QIcon, QFont, QPixmap"),
        ("配置设置", "from config.settings import Settings"),
        ("日志工具", "from utils.logger import get_logger"),
        ("深色主题", "from gui.styles.dark_theme import DarkTheme"),
        ("数据中心组件", "from gui.widgets.data_center_widget import DataCenterWidget"),
        ("策略中心组件", "from gui.widgets.strategy_center_widget import StrategyCenterWidget"),
        ("交易中心组件", "from gui.widgets.trading_center_widget import TradingCenterWidget"),
        ("分析中心组件", "from gui.widgets.analysis_center_widget import AnalysisCenterWidget"),
        ("仪表盘组件", "from gui.widgets.dashboard_widget import DashboardWidget"),
        ("设置组件", "from gui.widgets.settings_widget import SettingsWidget"),
    ]
    
    failed_imports = []
    
    for name, import_statement in imports_to_test:
        print(f"\n📦 测试 {name}...")
        try:
            print(f"  执行: {import_statement}")
            exec(import_statement)
            print(f"  ✅ {name} 导入成功")
        except Exception as e:
            print(f"  ❌ {name} 导入失败: {e}")
            print(f"  详细错误:")
            traceback.print_exc()
            failed_imports.append((name, str(e)))
            print()
    
    print("\n" + "=" * 60)
    print("导入测试总结")
    print("=" * 60)
    
    if failed_imports:
        print(f"❌ 发现 {len(failed_imports)} 个导入问题:")
        for name, error in failed_imports:
            print(f"  - {name}: {error}")
        
        print("\n💡 建议:")
        print("  1. 修复失败的导入模块")
        print("  2. 检查模块文件是否存在")
        print("  3. 检查模块内部是否有语法错误")
        
        return False
    else:
        print("✅ 所有导入测试通过！")
        print("🤔 问题可能在模块的初始化过程中")
        return True

def test_widget_creation():
    """测试组件创建"""
    print("\n" + "=" * 60)
    print("🏗️ 测试组件创建")
    print("=" * 60)
    
    try:
        # 先确保QApplication存在
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 测试创建各个组件
        widgets_to_test = [
            ("DashboardWidget", "from gui.widgets.dashboard_widget import DashboardWidget; widget = DashboardWidget()"),
            ("DataCenterWidget", "from gui.widgets.data_center_widget import DataCenterWidget; widget = DataCenterWidget()"),
            ("StrategyCenterWidget", "from gui.widgets.strategy_center_widget import StrategyCenterWidget; widget = StrategyCenterWidget()"),
            ("TradingCenterWidget", "from gui.widgets.trading_center_widget import TradingCenterWidget; widget = TradingCenterWidget()"),
            ("AnalysisCenterWidget", "from gui.widgets.analysis_center_widget import AnalysisCenterWidget; widget = AnalysisCenterWidget()"),
        ]
        
        for name, creation_code in widgets_to_test:
            print(f"\n🔧 测试创建 {name}...")
            try:
                exec(creation_code)
                print(f"  ✅ {name} 创建成功")
            except Exception as e:
                print(f"  ❌ {name} 创建失败: {e}")
                traceback.print_exc()
                return False
        
        print("\n✅ 所有组件创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 组件创建测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        # 测试导入
        imports_ok = test_individual_imports()
        
        if imports_ok:
            # 如果导入都成功，测试组件创建
            widgets_ok = test_widget_creation()
            
            if widgets_ok:
                print("\n🎉 所有测试通过！")
                print("💡 问题可能在主窗口的初始化逻辑中")
                print("   建议检查 MainWindow.__init__ 方法")
            else:
                print("\n❌ 组件创建测试失败")
        else:
            print("\n❌ 导入测试失败")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 测试过程崩溃: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏁 测试完成，退出码: {exit_code}")
    input("\n按回车键退出...")
    sys.exit(exit_code)

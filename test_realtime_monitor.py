#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实时监控功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    try:
        print("测试导入模块...")
        
        from gui.widgets.realtime_monitor_widget import (
            RealtimeMonitorWidget, RealtimeDataThread, PriceAlertManager
        )
        print("✓ 实时监控组件导入成功")
        
        from gui.widgets.data_center_widget import DataCenterWidget
        print("✓ 数据中心组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_realtime_data_thread():
    """测试实时数据线程"""
    try:
        print("\n测试实时数据线程...")
        
        from gui.widgets.realtime_monitor_widget import RealtimeDataThread
        
        # 创建实时数据线程
        symbols = ["000001", "000002"]
        thread = RealtimeDataThread(symbols, update_interval=1)
        print("✓ 实时数据线程创建成功")
        
        # 测试更新股票列表
        thread.update_symbols(["600000", "600036"])
        print("✓ 更新监控股票列表成功")
        
        # 测试更新刷新间隔
        thread.update_interval_setting(3)
        print("✓ 更新刷新间隔成功")
        
        # 测试停止功能
        thread.stop()
        print("✓ 停止功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 实时数据线程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_price_alert_manager():
    """测试价格预警管理器"""
    try:
        print("\n测试价格预警管理器...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.realtime_monitor_widget import PriceAlertManager
        
        # 创建预警管理器
        alert_manager = PriceAlertManager()
        print("✓ 价格预警管理器创建成功")
        
        # 模拟添加预警规则
        alert_manager.alert_symbol_input.setText("000001")
        alert_manager.alert_type_combo.setCurrentText("价格上涨")
        alert_manager.alert_value_input.setText("15.0")
        alert_manager.add_alert()
        print("✓ 添加预警规则成功")
        
        # 检查预警规则数量
        if len(alert_manager.alerts) > 0:
            print(f"✓ 预警规则数量: {len(alert_manager.alerts)}")
        else:
            print("✗ 预警规则添加失败")
            return False
        
        # 测试预警检查
        test_data = [
            {
                'symbol': '000001',
                'price': 16.0,
                'pct_change': 5.0
            }
        ]
        alert_manager.check_alerts(test_data)
        print("✓ 预警检查功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 价格预警管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_realtime_monitor_widget():
    """测试实时监控主组件"""
    try:
        print("\n测试实时监控主组件...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.realtime_monitor_widget import RealtimeMonitorWidget
        
        # 创建实时监控组件
        monitor_widget = RealtimeMonitorWidget()
        print("✓ 实时监控主组件创建成功")
        
        # 测试设置监控股票
        monitor_widget.symbols_input.setText("000001,000002,600000")
        print("✓ 设置监控股票成功")
        
        # 测试设置刷新间隔
        monitor_widget.interval_spinbox.setValue(3)
        print("✓ 设置刷新间隔成功")
        
        # 测试模拟数据更新
        from datetime import datetime
        test_data = [
            {
                'symbol': '000001',
                'name': '平安银行',
                'price': 12.50,
                'change': 0.30,
                'pct_change': 2.46,
                'volume': 1500000,
                'timestamp': datetime.now(),
                'source': 'AKShare'
            },
            {
                'symbol': '000002',
                'name': '万科A',
                'price': 8.90,
                'change': -0.15,
                'pct_change': -1.66,
                'volume': 2300000,
                'timestamp': datetime.now(),
                'source': 'AKShare'
            }
        ]
        
        monitor_widget.update_realtime_data(test_data)
        print("✓ 实时数据更新功能正常")
        
        # 检查表格数据
        if monitor_widget.realtime_table.rowCount() == 2:
            print("✓ 实时数据表格显示正常")
        else:
            print("✗ 实时数据表格显示异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 实时监控主组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_center_integration():
    """测试数据中心集成"""
    try:
        print("\n测试数据中心集成...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.data_center_widget import DataCenterWidget
        
        data_center = DataCenterWidget()
        print("✓ 数据中心组件创建成功")
        
        # 检查标签页数量
        tab_count = data_center.tab_widget.count()
        print(f"✓ 标签页数量: {tab_count}")
        
        # 检查实时监控标签页
        realtime_tab_index = -1
        for i in range(tab_count):
            if data_center.tab_widget.tabText(i) == "实时监控":
                realtime_tab_index = i
                break
        
        if realtime_tab_index >= 0:
            print("✓ 实时监控标签页集成成功")
            
            # 检查是否是实时监控组件
            realtime_widget = data_center.tab_widget.widget(realtime_tab_index)
            if hasattr(realtime_widget, 'realtime_table') and hasattr(realtime_widget, 'alert_manager'):
                print("✓ 实时监控功能集成成功")
            else:
                print("✗ 实时监控功能集成失败")
                return False
        else:
            print("✗ 实时监控标签页集成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据中心集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alert_functionality():
    """测试预警功能"""
    try:
        print("\n测试预警功能...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.realtime_monitor_widget import PriceAlertManager
        
        alert_manager = PriceAlertManager()
        
        # 添加多种类型的预警
        test_alerts = [
            {"symbol": "000001", "type": "价格上涨", "value": "15.0"},
            {"symbol": "000002", "type": "价格下跌", "value": "8.0"},
            {"symbol": "600000", "type": "涨幅超过", "value": "5.0"},
            {"symbol": "600036", "type": "跌幅超过", "value": "3.0"}
        ]
        
        for alert in test_alerts:
            alert_manager.alert_symbol_input.setText(alert["symbol"])
            alert_manager.alert_type_combo.setCurrentText(alert["type"])
            alert_manager.alert_value_input.setText(alert["value"])
            alert_manager.add_alert()
        
        print(f"✓ 添加了 {len(alert_manager.alerts)} 个预警规则")
        
        # 测试预警触发
        test_data = [
            {'symbol': '000001', 'price': 16.0, 'pct_change': 2.0},  # 应触发价格上涨预警
            {'symbol': '000002', 'price': 7.5, 'pct_change': -1.0},  # 应触发价格下跌预警
            {'symbol': '600000', 'price': 10.0, 'pct_change': 6.0},  # 应触发涨幅预警
            {'symbol': '600036', 'price': 12.0, 'pct_change': -4.0}  # 应触发跌幅预警
        ]
        
        alert_manager.check_alerts(test_data)
        print("✓ 预警触发检查完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 预警功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("实时监控功能测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("实时数据线程", test_realtime_data_thread),
        ("价格预警管理器", test_price_alert_manager),
        ("实时监控主组件", test_realtime_monitor_widget),
        ("预警功能", test_alert_functionality),
        ("数据中心集成", test_data_center_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！实时监控功能正常")
        print("\n新增功能特性:")
        print("✓ 实时数据推送 - 多股票实时行情监控")
        print("✓ 自定义监控列表 - 支持添加/删除监控股票")
        print("✓ 可配置刷新间隔 - 1-60秒自定义刷新频率")
        print("✓ 价格预警系统 - 支持价格和涨跌幅预警")
        print("✓ 多种预警类型 - 价格上涨/下跌、涨跌幅超过")
        print("✓ 预警日志记录 - 详细记录预警触发历史")
        print("✓ 实时数据表格 - 美观的实时行情显示")
        print("✓ 多数据源支持 - 自动切换最佳数据源")
        print("✓ 状态监控 - 实时显示监控状态和更新时间")
        print("✓ 完美集成 - 无缝集成到数据中心")
        return 0
    else:
        print("❌ 部分测试失败，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())

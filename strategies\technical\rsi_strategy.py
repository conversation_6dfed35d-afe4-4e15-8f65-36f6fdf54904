#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI策略
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from analysis.technical_indicators import TechnicalIndicators


class RSIStrategy(BaseStrategy):
    """RSI策略"""

    def __init__(self, name: str = "RSI策略", config: Dict[str, Any] = None):
        default_params = {
            'rsi_period': 14,       # RSI周期
            'oversold_level': 30,   # 超卖水平
            'overbought_level': 70, # 超买水平
            'extreme_oversold': 20, # 极度超卖
            'extreme_overbought': 80, # 极度超买
            'min_confidence': 0.6,  # 最小置信度
            'stop_loss': 0.05,      # 止损比例
            'take_profit': 0.12,    # 止盈比例
            'volume_filter': True,  # 成交量过滤
            'trend_filter': True,   # 趋势过滤
            'divergence_check': True # 背离检查
        }

        if config:
            default_params.update(config)

        super().__init__(name, default_params)
        self.tech_indicators = TechnicalIndicators()
        self.signal_count = 0

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成RSI交易信号"""
        signals = []

        if data is None or data.empty or len(data) < self.parameters['rsi_period'] + 10:
            self.logger.warning("数据不足，无法生成RSI信号")
            return signals

        try:
            # 计算RSI指标
            rsi = self.tech_indicators.rsi(data['close'], self.parameters['rsi_period'])

            # 计算辅助指标
            ma20 = data['close'].rolling(window=20).mean()
            volume_ma = data['volume'].rolling(window=20).mean()

            # 遍历数据生成信号
            for i in range(2, len(data)):
                current_date = data.index[i]
                current_price = data['close'].iloc[i]

                # 跳过NaN值
                if pd.isna(rsi.iloc[i]) or pd.isna(rsi.iloc[i-1]):
                    continue

                signal = None

                # RSI超卖买入信号
                if self._check_oversold_buy_signal(rsi, i):
                    if self._check_buy_conditions(data, i, ma20, volume_ma):
                        confidence = self._calculate_buy_confidence(data, i, rsi)

                        if confidence >= self.parameters['min_confidence']:
                            signal = {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'RSI',
                                'direction': 'buy',
                                'price': current_price,
                                'quantity': 0,
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'RSI超卖反弹，RSI:{rsi.iloc[i]:.2f}',
                                'stop_loss_price': current_price * (1 - self.parameters['stop_loss']),
                                'take_profit_price': current_price * (1 + self.parameters['take_profit'])
                            }

                # RSI超买卖出信号
                elif self._check_overbought_sell_signal(rsi, i):
                    if self._has_position(data.attrs.get('symbol', 'UNKNOWN')):
                        confidence = self._calculate_sell_confidence(data, i, rsi)

                        if confidence >= self.parameters['min_confidence']:
                            signal = {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'RSI',
                                'direction': 'sell',
                                'price': current_price,
                                'quantity': 0,
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'RSI超买回调，RSI:{rsi.iloc[i]:.2f}',
                                'stop_loss_price': 0,
                                'take_profit_price': 0
                            }

                # 检查背离信号
                elif self.parameters['divergence_check']:
                    divergence_signal = self._check_divergence(data, rsi, i)
                    if divergence_signal:
                        signal = divergence_signal

                if signal:
                    # 验证信号
                    if self.validate_signal(signal):
                        # 计算仓位大小
                        signal['quantity'] = self.calculate_position_size(
                            signal, current_price, 1000000
                        )

                        # 应用风险管理
                        signal = self.apply_risk_management(signal)

                        signals.append(signal)
                        self.save_signal(signal)
                        self.signal_count += 1

                        self.logger.info(f"生成{signal['signal_type']}信号: {signal['direction']} {signal['symbol']} @ {signal['price']:.2f}")

            return signals

        except Exception as e:
            self.logger.error(f"生成RSI信号失败: {e}")
            return signals

    def _check_oversold_buy_signal(self, rsi: pd.Series, index: int) -> bool:
        """检查超卖买入信号"""
        try:
            current_rsi = rsi.iloc[index]
            prev_rsi = rsi.iloc[index-1]

            # 基本超卖条件
            if current_rsi <= self.parameters['oversold_level']:
                # 极度超卖立即买入
                if current_rsi <= self.parameters['extreme_oversold']:
                    return True

                # 超卖反弹
                if prev_rsi < current_rsi:  # RSI开始上升
                    return True

            # RSI从超卖区域突破
            if (prev_rsi <= self.parameters['oversold_level'] and
                current_rsi > self.parameters['oversold_level']):
                return True

            return False

        except Exception:
            return False

    def _check_overbought_sell_signal(self, rsi: pd.Series, index: int) -> bool:
        """检查超买卖出信号"""
        try:
            current_rsi = rsi.iloc[index]
            prev_rsi = rsi.iloc[index-1]

            # 基本超买条件
            if current_rsi >= self.parameters['overbought_level']:
                # 极度超买立即卖出
                if current_rsi >= self.parameters['extreme_overbought']:
                    return True

                # 超买回调
                if prev_rsi > current_rsi:  # RSI开始下降
                    return True

            # RSI从超买区域跌破
            if (prev_rsi >= self.parameters['overbought_level'] and
                current_rsi < self.parameters['overbought_level']):
                return True

            return False

        except Exception:
            return False

    def _check_buy_conditions(self, data: pd.DataFrame, index: int,
                             ma20: pd.Series, volume_ma: pd.Series) -> bool:
        """检查买入附加条件"""
        try:
            # 趋势过滤：价格不能太远离均线
            if self.parameters['trend_filter']:
                if not pd.isna(ma20.iloc[index]):
                    price_ma_ratio = data['close'].iloc[index] / ma20.iloc[index]
                    if price_ma_ratio < 0.9:  # 价格低于均线10%以上
                        return False

            # 成交量过滤
            if self.parameters['volume_filter']:
                if (not pd.isna(volume_ma.iloc[index]) and
                    data['volume'].iloc[index] < volume_ma.iloc[index] * 0.8):
                    return False

            return True

        except Exception:
            return False

    def _calculate_buy_confidence(self, data: pd.DataFrame, index: int, rsi: pd.Series) -> float:
        """计算买入信号置信度"""
        try:
            confidence = 0.5

            current_rsi = rsi.iloc[index]

            # RSI位置越低，置信度越高
            if current_rsi <= self.parameters['extreme_oversold']:
                confidence += 0.3
            elif current_rsi <= self.parameters['oversold_level']:
                confidence += 0.2

            # RSI变化趋势
            if index >= 1:
                rsi_change = rsi.iloc[index] - rsi.iloc[index-1]
                if rsi_change > 0:  # RSI上升
                    confidence += min(0.15, rsi_change / 10)

            # 价格相对位置
            if len(data) >= index + 5:
                recent_low = data['close'].iloc[index-5:index+1].min()
                if data['close'].iloc[index] <= recent_low * 1.02:  # 接近近期低点
                    confidence += 0.1

            # 成交量确认
            if len(data) >= index + 5:
                avg_volume = data['volume'].iloc[index-5:index].mean()
                if data['volume'].iloc[index] > avg_volume * 1.2:
                    confidence += 0.1

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _calculate_sell_confidence(self, data: pd.DataFrame, index: int, rsi: pd.Series) -> float:
        """计算卖出信号置信度"""
        try:
            confidence = 0.5

            current_rsi = rsi.iloc[index]

            # RSI位置越高，置信度越高
            if current_rsi >= self.parameters['extreme_overbought']:
                confidence += 0.3
            elif current_rsi >= self.parameters['overbought_level']:
                confidence += 0.2

            # RSI变化趋势
            if index >= 1:
                rsi_change = rsi.iloc[index-1] - rsi.iloc[index]
                if rsi_change > 0:  # RSI下降
                    confidence += min(0.15, rsi_change / 10)

            # 价格相对位置
            if len(data) >= index + 5:
                recent_high = data['close'].iloc[index-5:index+1].max()
                if data['close'].iloc[index] >= recent_high * 0.98:  # 接近近期高点
                    confidence += 0.1

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _check_divergence(self, data: pd.DataFrame, rsi: pd.Series, index: int) -> Optional[Dict[str, Any]]:
        """检查RSI背离"""
        try:
            if index < 20:  # 需要足够的历史数据
                return None

            # 检查价格和RSI的背离
            price_window = data['close'].iloc[index-10:index+1]
            rsi_window = rsi.iloc[index-10:index+1]

            # 顶背离（价格新高，RSI不创新高）
            price_high_idx = price_window.idxmax()
            rsi_high_idx = rsi_window.idxmax()

            if (price_window.iloc[-1] >= price_window.max() * 0.99 and  # 价格接近最高
                rsi_window.iloc[-1] < rsi_window.max() * 0.95):  # RSI明显低于最高

                return {
                    'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                    'signal_type': 'RSI_DIVERGENCE',
                    'direction': 'sell',
                    'price': data['close'].iloc[index],
                    'quantity': 0,
                    'timestamp': data.index[index],
                    'confidence': 0.7,
                    'reason': f'RSI顶背离，价格:{data["close"].iloc[index]:.2f}, RSI:{rsi.iloc[index]:.2f}',
                    'stop_loss_price': 0,
                    'take_profit_price': 0
                }

            # 底背离（价格新低，RSI不创新低）
            price_low_idx = price_window.idxmin()
            rsi_low_idx = rsi_window.idxmin()

            if (price_window.iloc[-1] <= price_window.min() * 1.01 and  # 价格接近最低
                rsi_window.iloc[-1] > rsi_window.min() * 1.05):  # RSI明显高于最低

                return {
                    'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                    'signal_type': 'RSI_DIVERGENCE',
                    'direction': 'buy',
                    'price': data['close'].iloc[index],
                    'quantity': 0,
                    'timestamp': data.index[index],
                    'confidence': 0.7,
                    'reason': f'RSI底背离，价格:{data["close"].iloc[index]:.2f}, RSI:{rsi.iloc[index]:.2f}',
                    'stop_loss_price': data['close'].iloc[index] * (1 - self.parameters['stop_loss']),
                    'take_profit_price': data['close'].iloc[index] * (1 + self.parameters['take_profit'])
                }

            return None

        except Exception:
            return None

    def _has_position(self, symbol: str) -> bool:
        """检查是否有持仓"""
        return symbol in self.positions and self.positions[symbol]['quantity'] > 0

    def calculate_position_size(self, signal: Dict[str, Any], current_price: float,
                               available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于风险的仓位计算
            risk_per_trade = available_capital * 0.02  # 每笔交易风险2%
            stop_loss_price = signal.get('stop_loss_price', current_price * 0.95)
            risk_per_share = abs(current_price - stop_loss_price)

            if risk_per_share > 0:
                position_size = int(risk_per_trade / risk_per_share)
                # 确保是100的整数倍
                position_size = (position_size // 100) * 100
                return max(100, min(position_size, int(available_capital * 0.1 / current_price / 100) * 100))

            return 100

        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': 'RSI技术策略',
            'indicators': ['RSI', 'MA', 'Volume'],
            'signal_conditions': {
                'buy': f'RSI <= {self.parameters["oversold_level"]} 或底背离',
                'sell': f'RSI >= {self.parameters["overbought_level"]} 或顶背离'
            },
            'risk_management': {
                'stop_loss': f"{self.parameters['stop_loss']*100:.1f}%",
                'take_profit': f"{self.parameters['take_profit']*100:.1f}%"
            }
        })
        return info

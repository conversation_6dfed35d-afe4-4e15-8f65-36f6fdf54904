#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合功能验证测试
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_window_comprehensive():
    """测试主窗口综合功能"""
    print("🔍 测试主窗口综合功能...")
    
    try:
        app = QApplication([])
        
        from gui.main_window import MainWindow
        main_window = MainWindow()
        main_window.show()
        
        print("   ✅ 主窗口创建和显示成功")
        
        # 测试所有标签页
        if hasattr(main_window, 'tab_widget'):
            tab_count = main_window.tab_widget.count()
            print(f"   ✅ 主窗口标签页数量: {tab_count}")
            
            tab_names = []
            for i in range(tab_count):
                tab_name = main_window.tab_widget.tabText(i)
                tab_names.append(tab_name)
                
                # 切换到每个标签页
                main_window.tab_widget.setCurrentIndex(i)
                app.processEvents()
                print(f"   ✅ 标签页 '{tab_name}' 正常切换")
            
            print(f"   ✅ 所有标签页: {', '.join(tab_names)}")
        
        # 处理事件
        app.processEvents()
        
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 主窗口综合功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_workflow():
    """测试分析工作流程"""
    print("\n🔍 测试分析工作流程...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        test_symbol = "000001.SZ"
        
        # 测试技术分析
        print("   📊 测试技术分析流程...")
        analysis_widget.tab_widget.setCurrentIndex(0)  # 技术分析标签页
        app.processEvents()
        
        # 查找技术分析输入框和按钮
        from PyQt5.QtWidgets import QLineEdit, QPushButton
        line_edits = analysis_widget.findChildren(QLineEdit)
        if line_edits:
            line_edits[0].setText(test_symbol)
            print(f"   ✅ 输入技术分析股票代码: {test_symbol}")
        
        buttons = analysis_widget.findChildren(QPushButton)
        for button in buttons:
            if "分析" in button.text():
                button.click()
                app.processEvents()
                print("   ✅ 执行技术分析")
                break
        
        # 测试基本面分析
        print("   📈 测试基本面分析流程...")
        analysis_widget.tab_widget.setCurrentIndex(1)  # 基本面分析标签页
        app.processEvents()
        
        line_edits = analysis_widget.findChildren(QLineEdit)
        if line_edits:
            line_edits[0].setText(test_symbol)
            print(f"   ✅ 输入基本面分析股票代码: {test_symbol}")
        
        buttons = analysis_widget.findChildren(QPushButton)
        for button in buttons:
            if "分析" in button.text():
                button.click()
                app.processEvents()
                print("   ✅ 执行基本面分析")
                break
        
        # 测试量化分析
        print("   🔢 测试量化分析流程...")
        analysis_widget.tab_widget.setCurrentIndex(2)  # 量化分析标签页
        app.processEvents()
        
        line_edits = analysis_widget.findChildren(QLineEdit)
        if line_edits:
            line_edits[0].setText(f"{test_symbol},000002.SZ")
            print("   ✅ 输入量化分析股票池")
        
        buttons = analysis_widget.findChildren(QPushButton)
        for button in buttons:
            if "分析" in button.text():
                button.click()
                app.processEvents()
                print("   ✅ 执行量化分析")
                break
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_workflow():
    """测试策略工作流程"""
    print("\n🔍 测试策略工作流程...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.strategy_center_widget import StrategyCenterWidget
        strategy_widget = StrategyCenterWidget()
        strategy_widget.show()
        
        # 测试策略配置
        print("   ⚙️ 测试策略配置流程...")
        strategy_widget.tab_widget.setCurrentIndex(1)  # 策略配置标签页
        app.processEvents()
        
        from PyQt5.QtWidgets import QLineEdit, QPushButton
        line_edits = strategy_widget.findChildren(QLineEdit)
        if line_edits:
            line_edits[0].setText("综合测试策略")
            print("   ✅ 输入策略名称")
        
        buttons = strategy_widget.findChildren(QPushButton)
        for button in buttons:
            if "保存" in button.text():
                button.click()
                app.processEvents()
                print("   ✅ 保存策略配置")
                break
        
        # 测试策略回测
        print("   📊 测试策略回测流程...")
        strategy_widget.tab_widget.setCurrentIndex(2)  # 回测分析标签页
        app.processEvents()
        
        line_edits = strategy_widget.findChildren(QLineEdit)
        if line_edits:
            line_edits[0].setText("000001.SZ")
            print("   ✅ 输入回测股票代码")
        
        buttons = strategy_widget.findChildren(QPushButton)
        for button in buttons:
            if "回测" in button.text():
                button.click()
                app.processEvents()
                print("   ✅ 执行策略回测")
                break
        
        strategy_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 策略工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_flow():
    """测试数据流转"""
    print("\n🔍 测试数据流转...")
    
    try:
        # 测试技术指标计算
        from analysis.technical_indicators import TechnicalIndicators
        ti = TechnicalIndicators()
        
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        test_data = pd.DataFrame({
            'open': prices + np.random.randn(100) * 0.1,
            'high': prices + np.abs(np.random.randn(100) * 0.2),
            'low': prices - np.abs(np.random.randn(100) * 0.2),
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 100)
        }, index=dates)
        
        # 计算技术指标
        indicators = ti.calculate_all_indicators(test_data)
        print(f"   ✅ 技术指标计算成功，共 {len(indicators.columns) - len(test_data.columns)} 个指标")
        
        # 测试基本面分析
        from analysis.fundamental_analysis import FundamentalAnalysis
        fa = FundamentalAnalysis()
        
        analysis_result = fa.analyze_stock("000001.SZ")
        if analysis_result:
            print("   ✅ 基本面分析数据生成成功")
            print(f"   ✅ 综合评级: {analysis_result['evaluation'].get('综合评级', 'N/A')}")
        
        # 测试量化分析
        from analysis.quantitative_analysis import QuantitativeAnalysis
        qa = QuantitativeAnalysis()
        
        # 生成示例市场数据
        market_data = qa.generate_sample_market_data(["000001.SZ", "000002.SZ"], 252)
        print(f"   ✅ 量化分析数据生成成功，{len(market_data)} 个标的")
        
        # 进行风险分析
        risk_analysis = qa.perform_risk_analysis(market_data)
        print(f"   ✅ 风险分析完成，分析了 {len(risk_analysis)} 个标的")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据流转测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_stability():
    """测试系统稳定性"""
    print("\n🔍 测试系统稳定性...")
    
    try:
        app = QApplication([])
        
        # 创建多个组件实例
        from gui.main_window import MainWindow
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        from gui.widgets.strategy_center_widget import StrategyCenterWidget
        
        main_window = MainWindow()
        analysis_widget = AnalysisCenterWidget()
        strategy_widget = StrategyCenterWidget()
        
        print("   ✅ 多组件实例创建成功")
        
        # 显示所有组件
        main_window.show()
        analysis_widget.show()
        strategy_widget.show()
        
        print("   ✅ 多组件同时显示成功")
        
        # 处理大量事件
        for _ in range(50):
            app.processEvents()
        
        print("   ✅ 大量事件处理成功")
        
        # 关闭所有组件
        main_window.close()
        analysis_widget.close()
        strategy_widget.close()
        
        print("   ✅ 多组件关闭成功")
        
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 系统稳定性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("综合功能验证测试")
    print("=" * 50)
    
    # 测试1: 主窗口综合功能
    main_window_ok = test_main_window_comprehensive()
    
    # 测试2: 分析工作流程
    analysis_workflow_ok = test_analysis_workflow()
    
    # 测试3: 策略工作流程
    strategy_workflow_ok = test_strategy_workflow()
    
    # 测试4: 数据流转
    data_flow_ok = test_data_flow()
    
    # 测试5: 系统稳定性
    stability_ok = test_system_stability()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"主窗口综合功能: {'✅ 通过' if main_window_ok else '❌ 失败'}")
    print(f"分析工作流程: {'✅ 通过' if analysis_workflow_ok else '❌ 失败'}")
    print(f"策略工作流程: {'✅ 通过' if strategy_workflow_ok else '❌ 失败'}")
    print(f"数据流转: {'✅ 通过' if data_flow_ok else '❌ 失败'}")
    print(f"系统稳定性: {'✅ 通过' if stability_ok else '❌ 失败'}")
    
    all_passed = main_window_ok and analysis_workflow_ok and strategy_workflow_ok and data_flow_ok and stability_ok
    
    if all_passed:
        print("\n🎉 综合功能验证测试全部通过！")
        print("🎯 股票分析工具功能完善完成！")
        return True
    else:
        print("\n❌ 综合功能验证测试失败，需要进一步完善")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

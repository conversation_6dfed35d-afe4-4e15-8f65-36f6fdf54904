#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复layout错误脚本
修复所有widget文件中的layout引用错误
"""

from pathlib import Path

def fix_strategy_center_widget():
    """修复策略中心组件的layout错误"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "strategy_center_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复参数配置部分的layout错误
        layout_fixes = [
            ('layout.addWidget(长期均线_label, 1, 0)', 'params_layout.addWidget(长期均线_label, 1, 0)'),
            ('layout.addWidget(止损比例_label, 2, 0)', 'params_layout.addWidget(止损比例_label, 2, 0)'),
            ('layout.addWidget(止盈比例_label, 3, 0)', 'params_layout.addWidget(止盈比例_label, 3, 0)'),
            
            # 修复回测参数部分的layout错误
            ('layout.addWidget(开始日期_label, 0, 0)', 'backtest_layout.addWidget(开始日期_label, 0, 0)'),
            ('layout.addWidget(结束日期_label, 1, 0)', 'backtest_layout.addWidget(结束日期_label, 1, 0)'),
            ('layout.addWidget(初始资金_label, 2, 0)', 'backtest_layout.addWidget(初始资金_label, 2, 0)'),
        ]
        
        for old, new in layout_fixes:
            content = content.replace(old, new)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了 strategy_center_widget.py")
        return True
        
    except Exception as e:
        print(f"❌ 修复 strategy_center_widget.py 失败: {e}")
        return False

def fix_analysis_center_widget():
    """修复分析中心组件的layout错误"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "analysis_center_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修复可能的layout错误
        layout_fixes = [
            # 如果有类似的错误，在这里添加
        ]
        
        for old, new in layout_fixes:
            content = content.replace(old, new)
        
        # 检查是否有修改
        if layout_fixes:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 修复了 analysis_center_widget.py")
        else:
            print("ℹ️  analysis_center_widget.py 无需修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复 analysis_center_widget.py 失败: {e}")
        return False

def fix_all_layout_errors():
    """修复所有layout错误"""
    widgets_dir = Path(__file__).parent / "gui" / "widgets"
    
    # 需要检查的文件
    widget_files = [
        "strategy_center_widget.py",
        "analysis_center_widget.py",
        "trading_center_widget.py",
        "data_center_widget.py",
        "dashboard_widget.py",
    ]
    
    for widget_file in widget_files:
        file_path = widgets_dir / widget_file
        
        if not file_path.exists():
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 通用的layout错误修复
            # 查找 layout.addWidget(xxx_label, row, col) 模式
            import re
            
            # 查找所有可能的layout错误
            pattern = r'(\s+)layout\.addWidget\((\w+_label),\s*(\d+),\s*(\d+)\)'
            matches = re.findall(pattern, content)
            
            for match in matches:
                indent, label_name, row, col = match
                old_line = f'{indent}layout.addWidget({label_name}, {row}, {col})'
                
                # 尝试推断正确的layout名称
                # 根据上下文推断layout名称
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if old_line.strip() in line:
                        # 向上查找layout定义
                        for j in range(i-1, max(0, i-20), -1):
                            if '_layout = QGridLayout(' in lines[j]:
                                layout_name = lines[j].split('_layout')[0].split()[-1] + '_layout'
                                new_line = f'{indent}{layout_name}.addWidget({label_name}, {row}, {col})'
                                content = content.replace(old_line, new_line)
                                print(f"  修复: {old_line.strip()} -> {new_line.strip()}")
                                break
                        break
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 修复了 {widget_file}")
            else:
                print(f"ℹ️  {widget_file} 无需修复")
                
        except Exception as e:
            print(f"❌ 修复 {widget_file} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("批量修复layout错误")
    print("=" * 60)
    
    # 1. 修复策略中心组件
    fix_strategy_center_widget()
    
    # 2. 修复分析中心组件
    fix_analysis_center_widget()
    
    # 3. 修复所有其他组件
    print("\n修复其他组件...")
    fix_all_layout_errors()
    
    print("\n" + "=" * 60)
    print("修复完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()

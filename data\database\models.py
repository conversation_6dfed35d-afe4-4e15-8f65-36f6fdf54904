#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from config.settings import Settings

# 创建基类
Base = declarative_base()

# 创建数据库引擎
engine = create_engine(Settings.DATABASE_URL, echo=False)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


class StockInfo(Base):
    """股票基本信息表"""
    __tablename__ = "stock_info"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), unique=True, index=True, comment="股票代码")
    name = Column(String(100), comment="股票名称")
    market = Column(String(10), comment="市场代码")
    industry = Column(String(50), comment="所属行业")
    list_date = Column(String(20), comment="上市日期")
    is_active = Column(Boolean, default=True, comment="是否有效")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now(), comment="更新时间")


class StockDaily(Base):
    """股票日线数据表"""
    __tablename__ = "stock_daily"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, comment="股票代码")
    trade_date = Column(String(20), index=True, comment="交易日期")
    open = Column(Float, comment="开盘价")
    high = Column(Float, comment="最高价")
    low = Column(Float, comment="最低价")
    close = Column(Float, comment="收盘价")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    pct_change = Column(Float, comment="涨跌幅")
    change = Column(Float, comment="涨跌额")
    turnover = Column(Float, comment="换手率")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")


class StockMinute(Base):
    """股票分钟数据表"""
    __tablename__ = "stock_minute"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, comment="股票代码")
    datetime = Column(DateTime, index=True, comment="时间")
    open = Column(Float, comment="开盘价")
    high = Column(Float, comment="最高价")
    low = Column(Float, comment="最低价")
    close = Column(Float, comment="收盘价")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")


class IndexDaily(Base):
    """指数日线数据表"""
    __tablename__ = "index_daily"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, comment="指数代码")
    trade_date = Column(String(20), index=True, comment="交易日期")
    open = Column(Float, comment="开盘价")
    high = Column(Float, comment="最高价")
    low = Column(Float, comment="最低价")
    close = Column(Float, comment="收盘价")
    volume = Column(Float, comment="成交量")
    amount = Column(Float, comment="成交额")
    pct_change = Column(Float, comment="涨跌幅")
    change = Column(Float, comment="涨跌额")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")


class Strategy(Base):
    """策略信息表"""
    __tablename__ = "strategy"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, comment="策略名称")
    description = Column(Text, comment="策略描述")
    category = Column(String(50), comment="策略分类")
    parameters = Column(Text, comment="策略参数(JSON)")
    is_active = Column(Boolean, default=False, comment="是否激活")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now(), comment="更新时间")


class BacktestResult(Base):
    """回测结果表"""
    __tablename__ = "backtest_result"

    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, comment="策略ID")
    start_date = Column(String(20), comment="开始日期")
    end_date = Column(String(20), comment="结束日期")
    initial_capital = Column(Float, comment="初始资金")
    final_capital = Column(Float, comment="最终资金")
    total_return = Column(Float, comment="总收益率")
    annual_return = Column(Float, comment="年化收益率")
    max_drawdown = Column(Float, comment="最大回撤")
    sharpe_ratio = Column(Float, comment="夏普比率")
    win_rate = Column(Float, comment="胜率")
    profit_loss_ratio = Column(Float, comment="盈亏比")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")


class TradingRecord(Base):
    """交易记录表"""
    __tablename__ = "trading_record"

    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, comment="策略ID")
    symbol = Column(String(20), comment="股票代码")
    direction = Column(String(10), comment="买卖方向")
    quantity = Column(Integer, comment="数量")
    price = Column(Float, comment="价格")
    amount = Column(Float, comment="金额")
    commission = Column(Float, comment="手续费")
    trade_time = Column(DateTime, comment="交易时间")
    order_type = Column(String(20), comment="订单类型")
    status = Column(String(20), comment="状态")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")


class Portfolio(Base):
    """投资组合表"""
    __tablename__ = "portfolio"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), comment="组合名称")
    description = Column(Text, comment="组合描述")
    initial_capital = Column(Float, comment="初始资金")
    current_capital = Column(Float, comment="当前资金")
    total_return = Column(Float, comment="总收益率")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=lambda: datetime.now(), comment="创建时间")
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now(), comment="更新时间")


class Position(Base):
    """持仓表"""
    __tablename__ = "position"

    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(Integer, comment="组合ID")
    symbol = Column(String(20), comment="股票代码")
    quantity = Column(Integer, comment="持仓数量")
    avg_cost = Column(Float, comment="平均成本")
    current_price = Column(Float, comment="当前价格")
    market_value = Column(Float, comment="市值")
    unrealized_pnl = Column(Float, comment="浮动盈亏")
    updated_at = Column(DateTime, default=lambda: datetime.now(), onupdate=lambda: datetime.now(), comment="更新时间")


def create_tables():
    """创建所有表"""
    Base.metadata.create_all(bind=engine)


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主题修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QGroupBox, QGridLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from gui.styles.dark_theme import DarkTheme

class ThemeTestWindow(QMainWindow):
    """主题测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_theme()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("深色主题测试")
        self.setGeometry(200, 200, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("深色主题测试")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 测试组
        test_group = QGroupBox("测试组件")
        test_layout = QGridLayout(test_group)
        
        # 普通标签
        normal_label = QLabel("普通文字标签")
        test_layout.addWidget(QLabel("普通标签:"), 0, 0)
        test_layout.addWidget(normal_label, 0, 1)
        
        # 彩色标签
        green_label = QLabel("成功状态")
        green_label.setStyleSheet("color: #4CAF50; background-color: transparent;")
        test_layout.addWidget(QLabel("成功状态:"), 1, 0)
        test_layout.addWidget(green_label, 1, 1)
        
        red_label = QLabel("错误状态")
        red_label.setStyleSheet("color: #F44336; background-color: transparent;")
        test_layout.addWidget(QLabel("错误状态:"), 2, 0)
        test_layout.addWidget(red_label, 2, 1)
        
        # 按钮
        test_button = QPushButton("测试按钮")
        test_button.clicked.connect(self.show_message)
        test_layout.addWidget(QLabel("按钮:"), 3, 0)
        test_layout.addWidget(test_button, 3, 1)
        
        layout.addWidget(test_group)
        
        # 说明文字
        info_label = QLabel("如果您能清楚地看到所有文字，说明主题修复成功！")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #888888; background-color: transparent; padding: 20px;")
        layout.addWidget(info_label)
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)
    
    def apply_theme(self):
        """应用深色主题"""
        self.setStyleSheet(DarkTheme.get_stylesheet())
    
    def show_message(self):
        """显示消息"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "测试", "主题样式正常工作！")

def main():
    """主函数"""
    print("启动主题测试...")
    
    try:
        app = QApplication(sys.argv)
        
        window = ThemeTestWindow()
        window.show()
        
        print("主题测试窗口已显示")
        print("请检查文字是否清晰可见")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入脚本
"""

print("开始测试导入...")

try:
    print("1. 测试基础导入...")
    import sys
    from pathlib import Path
    print("   ✓ 基础模块导入成功")
    
    print("2. 测试PyQt5导入...")
    from PyQt5.QtWidgets import QApplication, QWidget
    from PyQt5.QtCore import Qt
    print("   ✓ PyQt5导入成功")
    
    print("3. 测试项目模块导入...")
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    from utils.logger import get_logger
    print("   ✓ logger模块导入成功")
    
    from config.settings import Settings
    print("   ✓ settings模块导入成功")
    
    print("4. 测试GUI组件导入...")
    from gui.widgets.analysis_center_widget import AnalysisCenterWidget
    print("   ✓ 分析中心组件导入成功")
    
    from gui.widgets.strategy_center_widget import StrategyCenterWidget
    print("   ✓ 策略中心组件导入成功")
    
    from gui.main_window import MainWindow
    print("   ✓ 主窗口导入成功")
    
    print("5. 测试创建应用程序...")
    app = QApplication([])
    print("   ✓ QApplication创建成功")
    
    print("6. 测试创建主窗口...")
    window = MainWindow()
    print("   ✓ 主窗口创建成功")
    
    print("\n✅ 所有测试通过！程序应该可以正常运行。")
    
except Exception as e:
    print(f"\n❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

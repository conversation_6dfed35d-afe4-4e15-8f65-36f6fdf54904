#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的技术指标模块
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from analysis.technical_indicators import TechnicalIndicators
from utils.logger import get_logger

def create_sample_data():
    """创建示例数据"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    # 生成模拟股价数据
    np.random.seed(42)
    base_price = 100
    returns = np.random.normal(0.001, 0.02, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成OHLCV数据
    data = pd.DataFrame({
        'date': dates,
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 100)
    })
    
    # 确保high >= close >= low
    data['high'] = data[['open', 'high', 'close']].max(axis=1)
    data['low'] = data[['open', 'low', 'close']].min(axis=1)
    
    data.set_index('date', inplace=True)
    return data

def test_basic_indicators():
    """测试基础指标"""
    logger = get_logger("TestTechnicalIndicators")
    logger.info("开始测试基础技术指标...")
    
    # 创建示例数据
    data = create_sample_data()
    logger.info(f"创建示例数据: {len(data)} 条记录")
    
    # 创建技术指标实例
    ti = TechnicalIndicators()
    
    # 测试移动平均线
    sma_5 = ti.sma(data['close'], 5)
    ema_5 = ti.ema(data['close'], 5)
    logger.info(f"SMA5 最新值: {sma_5.iloc[-1]:.2f}")
    logger.info(f"EMA5 最新值: {ema_5.iloc[-1]:.2f}")
    
    # 测试RSI
    rsi = ti.rsi(data['close'])
    logger.info(f"RSI 最新值: {rsi.iloc[-1]:.2f}")
    
    # 测试MACD
    macd, signal, histogram = ti.macd(data['close'])
    logger.info(f"MACD 最新值: {macd.iloc[-1]:.4f}")
    logger.info(f"MACD Signal 最新值: {signal.iloc[-1]:.4f}")
    logger.info(f"MACD Histogram 最新值: {histogram.iloc[-1]:.4f}")
    
    # 测试布林带
    bb_upper, bb_middle, bb_lower = ti.bollinger_bands(data['close'])
    logger.info(f"布林带上轨: {bb_upper.iloc[-1]:.2f}")
    logger.info(f"布林带中轨: {bb_middle.iloc[-1]:.2f}")
    logger.info(f"布林带下轨: {bb_lower.iloc[-1]:.2f}")
    
    logger.info("基础技术指标测试完成")

def test_new_indicators():
    """测试新增指标"""
    logger = get_logger("TestNewIndicators")
    logger.info("开始测试新增技术指标...")
    
    # 创建示例数据
    data = create_sample_data()
    
    # 创建技术指标实例
    ti = TechnicalIndicators()
    
    # 测试KDJ指标
    k, d, j = ti.kdj(data['high'], data['low'], data['close'])
    logger.info(f"KDJ K值: {k.iloc[-1]:.2f}")
    logger.info(f"KDJ D值: {d.iloc[-1]:.2f}")
    logger.info(f"KDJ J值: {j.iloc[-1]:.2f}")
    
    # 测试BRAR指标
    br, ar = ti.brar(data['open'], data['high'], data['low'], data['close'])
    logger.info(f"BR值: {br.iloc[-1]:.2f}")
    logger.info(f"AR值: {ar.iloc[-1]:.2f}")
    
    # 测试心理线指标
    psy = ti.psy(data['close'])
    logger.info(f"心理线指标: {psy.iloc[-1]:.2f}")
    
    # 测试乖离率
    bias = ti.bias(data['close'])
    logger.info(f"乖离率: {bias.iloc[-1]:.2f}%")
    
    # 测试威廉指标
    wr = ti.wr(data['high'], data['low'], data['close'])
    logger.info(f"威廉指标: {wr.iloc[-1]:.2f}")
    
    # 测试动量指标
    mtm = ti.mtm(data['close'])
    logger.info(f"动量指标: {mtm.iloc[-1]:.2f}")
    
    # 测试DMA指标
    dma, ama = ti.dma(data['close'])
    logger.info(f"DMA值: {dma.iloc[-1]:.2f}")
    logger.info(f"AMA值: {ama.iloc[-1]:.2f}")
    
    # 测试EXPMA指标
    expma = ti.expma(data['close'])
    logger.info(f"EXPMA值: {expma.iloc[-1]:.2f}")
    
    # 测试SAR指标
    sar = ti.sar(data['high'], data['low'])
    logger.info(f"SAR值: {sar.iloc[-1]:.2f}")
    
    logger.info("新增技术指标测试完成")

def test_all_indicators():
    """测试所有指标计算"""
    logger = get_logger("TestAllIndicators")
    logger.info("开始测试所有技术指标计算...")
    
    # 创建示例数据
    data = create_sample_data()
    logger.info(f"原始数据列数: {len(data.columns)}")
    
    # 创建技术指标实例
    ti = TechnicalIndicators()
    
    # 计算所有指标
    result = ti.calculate_all_indicators(data)
    logger.info(f"计算后数据列数: {len(result.columns)}")
    logger.info(f"新增指标数量: {len(result.columns) - len(data.columns)}")
    
    # 显示部分指标的最新值
    indicators_to_show = [
        'sma_5', 'sma_20', 'ema_5', 'ema_20', 'rsi_12', 'macd', 
        'bb_upper', 'bb_lower', 'kdj_k', 'kdj_d', 'kdj_j',
        'br', 'ar', 'psy', 'bias_6', 'wr', 'mtm', 'dma', 'expma', 'sar'
    ]
    
    logger.info("主要技术指标最新值:")
    for indicator in indicators_to_show:
        if indicator in result.columns:
            value = result[indicator].iloc[-1]
            if pd.notna(value):
                logger.info(f"  {indicator}: {value:.4f}")
            else:
                logger.info(f"  {indicator}: N/A")
    
    logger.info("所有技术指标计算测试完成")
    return result

def main():
    """主函数"""
    print("=" * 60)
    print("增强技术指标模块测试")
    print("=" * 60)
    
    try:
        # 测试基础指标
        test_basic_indicators()
        print()
        
        # 测试新增指标
        test_new_indicators()
        print()
        
        # 测试所有指标
        result = test_all_indicators()
        print()
        
        print("=" * 60)
        print("技术指标模块测试完成！")
        print(f"总共计算了 {len(result.columns) - 5} 个技术指标")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ 技术指标模块扩展成功！")
    else:
        print("❌ 技术指标模块测试失败！")

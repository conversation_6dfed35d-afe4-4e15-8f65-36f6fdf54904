#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易管理器
"""

import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import threading
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from trading.base_broker import BaseBroker, OrderSide, OrderType, OrderStatus
from trading.simulation_broker import SimulationBroker
from utils.logger import get_logger
from config.settings import Settings


class TradingManager:
    """交易管理器"""
    
    def __init__(self):
        self.logger = get_logger("TradingManager")
        self.broker = None
        self.is_running = False
        self.update_thread = None
        self.callbacks = {
            'order_update': [],
            'position_update': [],
            'account_update': []
        }
        
        # 风险控制参数
        self.risk_config = Settings.RISK_CONFIG
        self.trading_config = Settings.TRADING_CONFIG
        
    def initialize_broker(self, broker_type: str = "simulation", config: Dict[str, Any] = None) -> bool:
        """初始化券商接口"""
        try:
            if config is None:
                config = self.trading_config.copy()
            
            if broker_type == "simulation":
                self.broker = SimulationBroker(config)
            else:
                self.logger.error(f"不支持的券商类型: {broker_type}")
                return False
            
            # 连接到券商
            if self.broker.connect():
                self.logger.info(f"券商接口初始化成功: {broker_type}")
                return True
            else:
                self.logger.error("券商连接失败")
                return False
                
        except Exception as e:
            self.logger.error(f"初始化券商接口失败: {e}")
            return False
    
    def start_trading(self) -> bool:
        """开始交易"""
        if not self.broker or not self.broker.is_connected:
            self.logger.error("券商未连接，无法开始交易")
            return False
        
        if self.is_running:
            self.logger.warning("交易已在运行中")
            return True
        
        try:
            self.is_running = True
            
            # 启动数据更新线程
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            
            self.logger.info("交易系统启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动交易失败: {e}")
            self.is_running = False
            return False
    
    def stop_trading(self) -> bool:
        """停止交易"""
        try:
            self.is_running = False
            
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=5)
            
            self.logger.info("交易系统已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止交易失败: {e}")
            return False
    
    def place_order(self, symbol: str, side: str, order_type: str, 
                   quantity: int, price: float = None) -> Optional[str]:
        """下单"""
        if not self.broker or not self.broker.is_connected:
            self.logger.error("券商未连接")
            return None
        
        try:
            # 转换参数
            order_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
            order_type_enum = OrderType.MARKET if order_type.lower() == 'market' else OrderType.LIMIT
            
            # 风险检查
            if not self._risk_check(symbol, order_side, quantity, price):
                return None
            
            # 下单
            order_id = self.broker.place_order(symbol, order_side, order_type_enum, quantity, price)
            
            if order_id:
                self.logger.info(f"订单提交成功: {order_id}")
                self._notify_callbacks('order_update', {
                    'order_id': order_id,
                    'symbol': symbol,
                    'side': side,
                    'quantity': quantity,
                    'price': price,
                    'status': 'submitted'
                })
            
            return order_id
            
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """撤单"""
        if not self.broker:
            return False
        
        try:
            result = self.broker.cancel_order(order_id)
            if result:
                self.logger.info(f"撤单成功: {order_id}")
                self._notify_callbacks('order_update', {
                    'order_id': order_id,
                    'status': 'cancelled'
                })
            return result
            
        except Exception as e:
            self.logger.error(f"撤单失败: {e}")
            return False
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """获取账户信息"""
        if not self.broker:
            return None
        
        try:
            account_info = self.broker.get_account_info()
            return account_info.to_dict() if account_info else None
            
        except Exception as e:
            self.logger.error(f"获取账户信息失败: {e}")
            return None
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        if not self.broker:
            return []
        
        try:
            positions = self.broker.get_positions()
            return [pos.to_dict() for pos in positions.values()]
            
        except Exception as e:
            self.logger.error(f"获取持仓信息失败: {e}")
            return []
    
    def get_orders(self, symbol: str = None, status: str = None) -> List[Dict[str, Any]]:
        """获取订单列表"""
        if not self.broker:
            return []
        
        try:
            status_enum = None
            if status:
                status_map = {
                    'pending': OrderStatus.PENDING,
                    'partial': OrderStatus.PARTIAL,
                    'filled': OrderStatus.FILLED,
                    'cancelled': OrderStatus.CANCELLED,
                    'rejected': OrderStatus.REJECTED
                }
                status_enum = status_map.get(status.lower())
            
            orders = self.broker.get_orders(symbol, status_enum)
            return [order.to_dict() for order in orders]
            
        except Exception as e:
            self.logger.error(f"获取订单列表失败: {e}")
            return []
    
    def get_trades(self, symbol: str = None, start_date: datetime = None,
                  end_date: datetime = None) -> List[Dict[str, Any]]:
        """获取成交记录"""
        if not self.broker:
            return []
        
        try:
            return self.broker.get_trades(symbol, start_date, end_date)
            
        except Exception as e:
            self.logger.error(f"获取成交记录失败: {e}")
            return []
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def remove_callback(self, event_type: str, callback: Callable):
        """移除回调函数"""
        if event_type in self.callbacks and callback in self.callbacks[event_type]:
            self.callbacks[event_type].remove(callback)
    
    def _risk_check(self, symbol: str, side: OrderSide, quantity: int, price: float = None) -> bool:
        """风险检查"""
        try:
            # 检查市场开放时间
            if not self.broker.is_market_open():
                self.logger.warning("市场未开放")
                # 在模拟环境中允许非交易时间下单
                if not isinstance(self.broker, SimulationBroker):
                    return False
            
            # 检查单笔最大金额
            if price and quantity:
                order_amount = quantity * price
                max_order_amount = self.risk_config.get('max_single_order', 100000)
                if order_amount > max_order_amount:
                    self.logger.error(f"订单金额超过限制: {order_amount} > {max_order_amount}")
                    return False
            
            # 检查持仓集中度
            if side == OrderSide.BUY:
                account_info = self.broker.get_account_info()
                if account_info and price and quantity:
                    order_amount = quantity * price
                    position_ratio = order_amount / account_info.total_assets
                    max_position = self.risk_config.get('max_single_position', 0.1)
                    
                    if position_ratio > max_position:
                        self.logger.error(f"单只股票仓位超过限制: {position_ratio:.2%} > {max_position:.2%}")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"风险检查失败: {e}")
            return False
    
    def _update_loop(self):
        """数据更新循环"""
        while self.is_running:
            try:
                # 更新账户信息
                self.broker.update_account_info()
                account_info = self.broker.get_account_info()
                if account_info:
                    self._notify_callbacks('account_update', account_info.to_dict())
                
                # 更新持仓信息
                self.broker.update_positions()
                positions = self.broker.get_positions()
                self._notify_callbacks('position_update', [pos.to_dict() for pos in positions.values()])
                
                # 检查订单状态更新
                for order_id, order in self.broker.orders.items():
                    # 这里可以添加订单状态变化的检测逻辑
                    pass
                
                time.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                self.logger.error(f"数据更新失败: {e}")
                time.sleep(10)  # 出错时延长等待时间
    
    def _notify_callbacks(self, event_type: str, data: Any):
        """通知回调函数"""
        try:
            for callback in self.callbacks.get(event_type, []):
                callback(data)
        except Exception as e:
            self.logger.error(f"回调函数执行失败: {e}")
    
    def get_broker_status(self) -> Dict[str, Any]:
        """获取券商状态"""
        if not self.broker:
            return {'connected': False, 'broker_name': 'None'}
        
        return {
            'connected': self.broker.is_connected,
            'broker_name': self.broker.get_broker_name(),
            'is_running': self.is_running,
            'market_open': self.broker.is_market_open()
        }
    
    def disconnect(self):
        """断开连接"""
        try:
            self.stop_trading()
            if self.broker:
                self.broker.disconnect()
                self.broker = None
            self.logger.info("交易管理器已断开连接")
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")


# 全局交易管理器实例
trading_manager = TradingManager()

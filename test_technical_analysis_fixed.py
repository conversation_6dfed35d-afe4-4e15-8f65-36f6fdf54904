#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的技术分析功能
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ema12_indicator():
    """测试EMA12指标"""
    print("🔍 测试EMA12指标...")
    
    try:
        from analysis.technical_indicators import TechnicalIndicators
        import pandas as pd
        import numpy as np
        
        # 创建技术指标实例
        ti = TechnicalIndicators()
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        test_data = pd.DataFrame({
            'open': prices + np.random.randn(100) * 0.1,
            'high': prices + np.abs(np.random.randn(100) * 0.2),
            'low': prices - np.abs(np.random.randn(100) * 0.2),
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 100)
        }, index=dates)
        
        # 计算所有指标
        indicators = ti.calculate_all_indicators(test_data)
        
        # 检查EMA12是否存在
        if 'ema_12' in indicators.columns:
            ema12_value = indicators['ema_12'].iloc[-1]
            print(f"   ✅ EMA12指标计算成功，最新值: {ema12_value:.4f}")
            return True
        else:
            print("   ❌ EMA12指标缺失")
            return False
            
    except Exception as e:
        print(f"   ❌ EMA12指标测试失败: {e}")
        return False

def test_quantitative_analysis_table():
    """测试量化分析表格"""
    print("\n🔍 测试量化分析表格...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 切换到量化分析标签页
        if hasattr(analysis_widget, 'tab_widget'):
            for i in range(analysis_widget.tab_widget.count()):
                tab_name = analysis_widget.tab_widget.tabText(i)
                if "量化分析" in tab_name:
                    analysis_widget.tab_widget.setCurrentIndex(i)
                    print("   ✅ 切换到量化分析标签页")
                    break
        
        # 处理事件
        app.processEvents()
        
        # 查找股票池输入框
        from PyQt5.QtWidgets import QLineEdit
        line_edits = analysis_widget.findChildren(QLineEdit)
        
        stock_pool_input = None
        for edit in line_edits:
            if hasattr(edit, 'placeholderText'):
                placeholder = edit.placeholderText().lower()
                if '股票池' in placeholder or '代码' in placeholder:
                    stock_pool_input = edit
                    break
        
        if not stock_pool_input and line_edits:
            # 如果没找到特定的，就用第一个
            stock_pool_input = line_edits[0]
        
        if stock_pool_input:
            # 输入测试股票代码
            test_symbols = "000001.SZ,000002.SZ"
            stock_pool_input.setText(test_symbols)
            print(f"   ✅ 输入股票池: {test_symbols}")
            
            # 查找分析按钮
            from PyQt5.QtWidgets import QPushButton
            buttons = analysis_widget.findChildren(QPushButton)
            
            analyze_button = None
            for button in buttons:
                if "分析" in button.text():
                    analyze_button = button
                    break
            
            if analyze_button:
                print(f"   ✅ 找到分析按钮: {analyze_button.text()}")
                
                # 模拟点击分析按钮
                analyze_button.click()
                print("   ✅ 点击分析按钮")
                
                # 处理事件，等待分析完成
                for _ in range(10):
                    app.processEvents()
                
                print("   ✅ 量化分析操作完成")
                
                # 检查量化分析表格
                if hasattr(analysis_widget, 'quant_results_table'):
                    table = analysis_widget.quant_results_table
                    if table.rowCount() > 0:
                        print(f"   ✅ 量化分析表格有数据，行数: {table.rowCount()}")
                    else:
                        print("   ⚠️ 量化分析表格为空")
                else:
                    print("   ❌ 未找到量化分析表格")
            else:
                print("   ❌ 未找到分析按钮")
        else:
            print("   ❌ 未找到股票池输入框")
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 量化分析表格测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_technical_analysis_comprehensive():
    """测试技术分析综合功能"""
    print("\n🔍 测试技术分析综合功能...")
    
    try:
        app = QApplication([])
        
        from gui.widgets.analysis_center_widget import AnalysisCenterWidget
        analysis_widget = AnalysisCenterWidget()
        analysis_widget.show()
        
        # 测试所有标签页
        tab_names = []
        if hasattr(analysis_widget, 'tab_widget'):
            for i in range(analysis_widget.tab_widget.count()):
                tab_name = analysis_widget.tab_widget.tabText(i)
                tab_names.append(tab_name)
                analysis_widget.tab_widget.setCurrentIndex(i)
                app.processEvents()
                print(f"   ✅ 标签页 '{tab_name}' 正常显示")
        
        print(f"   ✅ 发现 {len(tab_names)} 个分析标签页: {', '.join(tab_names)}")
        
        # 测试技术指标计算
        if analysis_widget.ti:
            print("   ✅ 技术指标模块可用")
            
            # 创建测试数据
            import pandas as pd
            import numpy as np
            
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            np.random.seed(42)
            prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
            
            test_data = pd.DataFrame({
                'open': prices + np.random.randn(100) * 0.1,
                'high': prices + np.abs(np.random.randn(100) * 0.2),
                'low': prices - np.abs(np.random.randn(100) * 0.2),
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, 100)
            }, index=dates)
            
            # 计算指标
            indicators = analysis_widget.ti.calculate_all_indicators(test_data)
            
            # 检查关键指标
            key_indicators = ['sma_5', 'sma_20', 'ema_12', 'rsi_12', 'macd', 'kdj_k']
            available_indicators = []
            missing_indicators = []
            
            for indicator in key_indicators:
                if indicator in indicators.columns:
                    available_indicators.append(indicator)
                else:
                    missing_indicators.append(indicator)
            
            print(f"   ✅ 可用指标 ({len(available_indicators)}): {', '.join(available_indicators)}")
            if missing_indicators:
                print(f"   ⚠️ 缺失指标 ({len(missing_indicators)}): {', '.join(missing_indicators)}")
        
        analysis_widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 技术分析综合功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("修复后的技术分析功能测试")
    print("=" * 50)
    
    # 测试1: EMA12指标
    ema12_ok = test_ema12_indicator()
    
    # 测试2: 量化分析表格
    quant_table_ok = test_quantitative_analysis_table()
    
    # 测试3: 技术分析综合功能
    comprehensive_ok = test_technical_analysis_comprehensive()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"EMA12指标: {'✅ 通过' if ema12_ok else '❌ 失败'}")
    print(f"量化分析表格: {'✅ 通过' if quant_table_ok else '❌ 失败'}")
    print(f"技术分析综合功能: {'✅ 通过' if comprehensive_ok else '❌ 失败'}")
    
    all_passed = ema12_ok and quant_table_ok and comprehensive_ok
    
    if all_passed:
        print("\n🎉 修复后的技术分析功能测试通过！")
        return True
    else:
        print("\n❌ 技术分析功能测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

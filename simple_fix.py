#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复脚本 - 直接修复关键语法错误
"""

from pathlib import Path

def fix_settings_widget_imports():
    """修复设置组件的导入问题"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "settings_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复导入语句
        old_imports = """from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QGroupBox, QTabWidget,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QTextEdit, QFileDialog, QMessageBox,
    QSlider, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor"""

        new_imports = """from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QGroupBox, QTabWidget,
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QFileDialog, QMessageBox,
    QSlider
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont"""

        content = content.replace(old_imports, new_imports)
        
        # 移除Settings导入（如果不需要）
        content = content.replace("from config.settings import Settings", "# from config.settings import Settings")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 修复了导入语句")
        return True
        
    except Exception as e:
        print(f"❌ 修复导入失败: {e}")
        return False

def main():
    """主函数"""
    print("简单修复脚本")
    print("=" * 40)
    
    fix_settings_widget_imports()
    
    print("修复完成")

if __name__ == "__main__":
    main()

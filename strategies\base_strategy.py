#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略基类
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json

from utils.logger import get_logger
from utils.constants import DIRECTIONS, ORDER_TYPES, STRATEGY_STATUS


class BaseStrategy(ABC):
    """策略基类"""

    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        self.name = name
        self.parameters = parameters or {}
        self.logger = get_logger(f"Strategy.{name}")

        # 策略状态
        self.status = STRATEGY_STATUS['inactive']
        self.positions = {}  # 持仓信息
        self.orders = []     # 订单历史
        self.signals = []    # 信号历史

        # 绩效统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0

        # 风险控制参数
        self.max_position_size = self.parameters.get('max_position_size', 0.1)  # 最大仓位10%
        self.stop_loss = self.parameters.get('stop_loss', 0.05)  # 止损5%
        self.take_profit = self.parameters.get('take_profit', 0.15)  # 止盈15%

        self.logger.info(f"策略 {name} 初始化完成")

    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成交易信号"""
        pass

    @abstractmethod
    def calculate_position_size(self, signal: Dict[str, Any],
                              current_price: float,
                              available_capital: float) -> int:
        """计算仓位大小"""
        pass

    def validate_signal(self, signal: Dict[str, Any]) -> bool:
        """验证信号有效性"""
        required_fields = ['symbol', 'direction', 'timestamp', 'price']

        for field in required_fields:
            if field not in signal:
                self.logger.warning(f"信号缺少必要字段: {field}")
                return False

        if signal['direction'] not in DIRECTIONS:
            self.logger.warning(f"无效的交易方向: {signal['direction']}")
            return False

        if signal['price'] <= 0:
            self.logger.warning(f"无效的价格: {signal['price']}")
            return False

        return True

    def apply_risk_management(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """应用风险管理规则"""
        symbol = signal['symbol']
        direction = signal['direction']
        price = signal['price']

        # 添加止损止盈
        if direction == 'buy':
            signal['stop_loss_price'] = price * (1 - self.stop_loss)
            signal['take_profit_price'] = price * (1 + self.take_profit)
        else:  # sell
            signal['stop_loss_price'] = price * (1 + self.stop_loss)
            signal['take_profit_price'] = price * (1 - self.take_profit)

        # 检查仓位限制
        current_position = self.positions.get(symbol, 0)
        max_position = signal.get('quantity', 0)

        # 确保不超过最大仓位限制
        if abs(current_position + max_position) > self.max_position_size:
            adjusted_quantity = self.max_position_size - abs(current_position)
            signal['quantity'] = max(0, adjusted_quantity)
            self.logger.info(f"调整仓位大小以符合风险限制: {adjusted_quantity}")

        return signal

    def update_position(self, symbol: str, quantity: int, price: float, direction: str):
        """更新持仓"""
        if symbol not in self.positions:
            self.positions[symbol] = {
                'quantity': 0,
                'avg_cost': 0.0,
                'unrealized_pnl': 0.0,
                'realized_pnl': 0.0
            }

        position = self.positions[symbol]

        if direction == 'buy':
            # 买入
            total_cost = position['quantity'] * position['avg_cost'] + quantity * price
            total_quantity = position['quantity'] + quantity
            position['avg_cost'] = total_cost / total_quantity if total_quantity > 0 else 0
            position['quantity'] = total_quantity
        else:
            # 卖出
            if position['quantity'] >= quantity:
                # 部分或全部平仓
                realized_pnl = quantity * (price - position['avg_cost'])
                position['realized_pnl'] += realized_pnl
                position['quantity'] -= quantity
                self.total_pnl += realized_pnl

                if position['quantity'] == 0:
                    position['avg_cost'] = 0.0
            else:
                self.logger.warning(f"卖出数量 {quantity} 超过持仓 {position['quantity']}")

    def calculate_unrealized_pnl(self, current_prices: Dict[str, float]):
        """计算未实现盈亏"""
        total_unrealized = 0.0

        for symbol, position in self.positions.items():
            if position['quantity'] > 0 and symbol in current_prices:
                current_price = current_prices[symbol]
                unrealized_pnl = position['quantity'] * (current_price - position['avg_cost'])
                position['unrealized_pnl'] = unrealized_pnl
                total_unrealized += unrealized_pnl

        return total_unrealized

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取策略绩效指标"""
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0

        return {
            'name': self.name,
            'status': self.status,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'max_drawdown': self.max_drawdown,
            'positions': len([p for p in self.positions.values() if p['quantity'] > 0]),
            'parameters': self.parameters
        }

    def save_signal(self, signal: Dict[str, Any]):
        """保存信号"""
        signal['strategy'] = self.name
        signal['timestamp'] = datetime.now()
        self.signals.append(signal)

        # 限制信号历史长度
        if len(self.signals) > 1000:
            self.signals = self.signals[-1000:]

    def get_signals_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取信号历史"""
        return self.signals[-limit:]

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'description': f'{self.__class__.__name__}策略',
            'strategy_type': self.__class__.__name__,
            'indicators': [],
            'signal_conditions': {},
            'risk_management': {
                'stop_loss': f"{self.stop_loss*100:.1f}%",
                'take_profit': f"{self.take_profit*100:.1f}%",
                'max_position': f"{self.max_position_size*100:.1f}%"
            },
            'parameters': self.parameters,
            'status': self.status
        }

    def reset(self):
        """重置策略状态"""
        self.positions = {}
        self.orders = []
        self.signals = []
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.status = STRATEGY_STATUS['inactive']
        self.logger.info(f"策略 {self.name} 已重置")

    def start(self):
        """启动策略"""
        self.status = STRATEGY_STATUS['active']
        self.logger.info(f"策略 {self.name} 已启动")

    def stop(self):
        """停止策略"""
        self.status = STRATEGY_STATUS['stopped']
        self.logger.info(f"策略 {self.name} 已停止")

    def pause(self):
        """暂停策略"""
        self.status = STRATEGY_STATUS['paused']
        self.logger.info(f"策略 {self.name} 已暂停")

    def is_active(self) -> bool:
        """检查策略是否激活"""
        return self.status == STRATEGY_STATUS['active']

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'parameters': self.parameters,
            'status': self.status,
            'performance': self.get_performance_metrics()
        }

    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """从字典创建策略实例"""
        strategy = cls(data['name'], data.get('parameters', {}))
        strategy.status = data.get('status', STRATEGY_STATUS['inactive'])
        return strategy

    def __str__(self) -> str:
        """字符串表示"""
        return f"Strategy({self.name}, status={self.status}, positions={len(self.positions)})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.to_json()

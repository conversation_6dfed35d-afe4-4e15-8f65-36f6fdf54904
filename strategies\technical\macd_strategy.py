#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD策略
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from strategies.base_strategy import BaseStrategy
from analysis.technical_indicators import TechnicalIndicators


class MACDStrategy(BaseStrategy):
    """MACD策略"""

    def __init__(self, name: str = "MACD策略", config: Dict[str, Any] = None):
        default_params = {
            'fast_period': 12,      # 快线周期
            'slow_period': 26,      # 慢线周期
            'signal_period': 9,     # 信号线周期
            'min_confidence': 0.6,  # 最小置信度
            'stop_loss': 0.05,      # 止损比例
            'take_profit': 0.15,    # 止盈比例
            'volume_filter': True,  # 成交量过滤
            'trend_filter': True    # 趋势过滤
        }

        if config:
            default_params.update(config)

        super().__init__(name, default_params)
        self.tech_indicators = TechnicalIndicators()
        self.signal_count = 0

    def generate_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成MACD交易信号"""
        signals = []

        if data is None or data.empty or len(data) < self.parameters['slow_period'] + self.parameters['signal_period']:
            self.logger.warning("数据不足，无法生成MACD信号")
            return signals

        try:
            # 计算MACD指标
            macd, macd_signal, macd_hist = self.tech_indicators.macd(
                data['close'],
                self.parameters['fast_period'],
                self.parameters['slow_period'],
                self.parameters['signal_period']
            )

            # 计算辅助指标
            rsi = self.tech_indicators.rsi(data['close'], 14)
            volume_ma = data['volume'].rolling(window=20).mean()

            # 遍历数据生成信号
            for i in range(1, len(data)):
                current_date = data.index[i]
                current_price = data['close'].iloc[i]

                # 跳过NaN值
                if (pd.isna(macd.iloc[i]) or pd.isna(macd_signal.iloc[i]) or
                    pd.isna(macd_hist.iloc[i]) or pd.isna(macd_hist.iloc[i-1])):
                    continue

                signal = None

                # MACD金叉买入信号
                if (macd_hist.iloc[i] > 0 and macd_hist.iloc[i-1] <= 0 and
                    macd.iloc[i] > macd_signal.iloc[i]):

                    # 附加条件检查
                    if self._check_buy_conditions(data, i, rsi, volume_ma):
                        confidence = self._calculate_buy_confidence(data, i, macd, macd_signal, macd_hist, rsi)

                        if confidence >= self.parameters['min_confidence']:
                            signal = {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'MACD',
                                'direction': 'buy',
                                'price': current_price,
                                'quantity': 0,  # 将在风险管理中计算
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'MACD金叉，MACD:{macd.iloc[i]:.4f}, 信号线:{macd_signal.iloc[i]:.4f}',
                                'stop_loss_price': current_price * (1 - self.parameters['stop_loss']),
                                'take_profit_price': current_price * (1 + self.parameters['take_profit'])
                            }

                # MACD死叉卖出信号
                elif (macd_hist.iloc[i] < 0 and macd_hist.iloc[i-1] >= 0 and
                      macd.iloc[i] < macd_signal.iloc[i]):

                    # 检查是否有持仓
                    if self._has_position(data.attrs.get('symbol', 'UNKNOWN')):
                        confidence = self._calculate_sell_confidence(data, i, macd, macd_signal, macd_hist, rsi)

                        if confidence >= self.parameters['min_confidence']:
                            signal = {
                                'symbol': data.attrs.get('symbol', 'UNKNOWN'),
                                'signal_type': 'MACD',
                                'direction': 'sell',
                                'price': current_price,
                                'quantity': 0,  # 将在风险管理中计算
                                'timestamp': current_date,
                                'confidence': confidence,
                                'reason': f'MACD死叉，MACD:{macd.iloc[i]:.4f}, 信号线:{macd_signal.iloc[i]:.4f}',
                                'stop_loss_price': 0,
                                'take_profit_price': 0
                            }

                if signal:
                    # 验证信号
                    if self.validate_signal(signal):
                        # 计算仓位大小
                        signal['quantity'] = self.calculate_position_size(
                            signal, current_price, 1000000  # 假设100万资金
                        )

                        # 应用风险管理
                        signal = self.apply_risk_management(signal)

                        signals.append(signal)
                        self.save_signal(signal)
                        self.signal_count += 1

                        self.logger.info(f"生成{signal['signal_type']}信号: {signal['direction']} {signal['symbol']} @ {signal['price']:.2f}")

            return signals

        except Exception as e:
            self.logger.error(f"生成MACD信号失败: {e}")
            return signals

    def _check_buy_conditions(self, data: pd.DataFrame, index: int, rsi: pd.Series, volume_ma: pd.Series) -> bool:
        """检查买入附加条件"""
        try:
            # 趋势过滤：价格在20日均线之上
            if self.parameters['trend_filter']:
                ma20 = data['close'].rolling(window=20).mean()
                if pd.isna(ma20.iloc[index]) or data['close'].iloc[index] < ma20.iloc[index]:
                    return False

            # 成交量过滤：成交量大于20日平均
            if self.parameters['volume_filter']:
                if (pd.isna(volume_ma.iloc[index]) or
                    data['volume'].iloc[index] < volume_ma.iloc[index] * 1.2):
                    return False

            # RSI过滤：避免超买区域
            if not pd.isna(rsi.iloc[index]) and rsi.iloc[index] > 80:
                return False

            return True

        except Exception:
            return False

    def _calculate_buy_confidence(self, data: pd.DataFrame, index: int,
                                 macd: pd.Series, macd_signal: pd.Series,
                                 macd_hist: pd.Series, rsi: pd.Series) -> float:
        """计算买入信号置信度"""
        try:
            confidence = 0.5  # 基础置信度

            # MACD强度
            macd_strength = abs(macd.iloc[index] - macd_signal.iloc[index])
            confidence += min(0.2, macd_strength * 100)

            # 柱状图变化强度
            hist_change = macd_hist.iloc[index] - macd_hist.iloc[index-1]
            confidence += min(0.15, hist_change * 50)

            # RSI位置
            if not pd.isna(rsi.iloc[index]):
                if 30 <= rsi.iloc[index] <= 70:  # 中性区域
                    confidence += 0.1
                elif rsi.iloc[index] < 30:  # 超卖区域
                    confidence += 0.15

            # 价格动量
            if len(data) >= index + 3:
                price_momentum = (data['close'].iloc[index] - data['close'].iloc[index-3]) / data['close'].iloc[index-3]
                if price_momentum > 0:
                    confidence += min(0.1, price_momentum * 5)

            # MACD位置（零轴上方更好）
            if macd.iloc[index] > 0:
                confidence += 0.05

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _calculate_sell_confidence(self, data: pd.DataFrame, index: int,
                                  macd: pd.Series, macd_signal: pd.Series,
                                  macd_hist: pd.Series, rsi: pd.Series) -> float:
        """计算卖出信号置信度"""
        try:
            confidence = 0.5  # 基础置信度

            # MACD强度
            macd_strength = abs(macd.iloc[index] - macd_signal.iloc[index])
            confidence += min(0.2, macd_strength * 100)

            # 柱状图变化强度
            hist_change = macd_hist.iloc[index-1] - macd_hist.iloc[index]
            confidence += min(0.15, hist_change * 50)

            # RSI位置
            if not pd.isna(rsi.iloc[index]):
                if rsi.iloc[index] > 70:  # 超买区域
                    confidence += 0.15
                elif 30 <= rsi.iloc[index] <= 70:  # 中性区域
                    confidence += 0.1

            # 价格动量
            if len(data) >= index + 3:
                price_momentum = (data['close'].iloc[index-3] - data['close'].iloc[index]) / data['close'].iloc[index]
                if price_momentum > 0:
                    confidence += min(0.1, price_momentum * 5)

            # MACD位置（零轴下方更危险）
            if macd.iloc[index] < 0:
                confidence += 0.05

            return min(1.0, max(0.1, confidence))

        except Exception:
            return 0.5

    def _has_position(self, symbol: str) -> bool:
        """检查是否有持仓"""
        return symbol in self.positions and self.positions[symbol]['quantity'] > 0

    def calculate_position_size(self, signal: Dict[str, Any], current_price: float,
                               available_capital: float) -> int:
        """计算仓位大小"""
        try:
            # 基于风险的仓位计算
            risk_per_trade = available_capital * 0.02  # 每笔交易风险2%
            stop_loss_price = signal.get('stop_loss_price', current_price * 0.95)
            risk_per_share = abs(current_price - stop_loss_price)

            if risk_per_share > 0:
                position_size = int(risk_per_trade / risk_per_share)
                # 确保是100的整数倍
                position_size = (position_size // 100) * 100
                return max(100, min(position_size, int(available_capital * 0.1 / current_price / 100) * 100))

            return 100

        except Exception:
            return 100

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = super().get_strategy_info()
        info.update({
            'strategy_type': 'MACD技术策略',
            'indicators': ['MACD', 'RSI', 'MA'],
            'signal_conditions': {
                'buy': 'MACD金叉且满足辅助条件',
                'sell': 'MACD死叉且有持仓'
            },
            'risk_management': {
                'stop_loss': f"{self.parameters['stop_loss']*100:.1f}%",
                'take_profit': f"{self.parameters['take_profit']*100:.1f}%"
            }
        })
        return info

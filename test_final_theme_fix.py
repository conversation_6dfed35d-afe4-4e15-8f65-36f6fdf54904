#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终主题修复测试
验证所有文字显示问题是否已解决
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from gui.styles.dark_theme import DarkTheme

class ThemeTestMainWindow(QMainWindow):
    """主题测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_theme()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("主题修复最终测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 测试仪表盘组件
        try:
            from gui.widgets.dashboard_widget import DashboardWidget
            dashboard = DashboardWidget()
            tab_widget.addTab(dashboard, "仪表盘测试")
            print("✓ 仪表盘组件加载成功")
        except Exception as e:
            print(f"❌ 仪表盘组件加载失败: {e}")
        
        # 测试数据中心组件
        try:
            from gui.widgets.data_center_widget import DataCenterWidget
            data_center = DataCenterWidget()
            tab_widget.addTab(data_center, "数据中心测试")
            print("✓ 数据中心组件加载成功")
        except Exception as e:
            print(f"❌ 数据中心组件加载失败: {e}")
        
        layout.addWidget(tab_widget)
    
    def apply_theme(self):
        """应用深色主题"""
        self.setStyleSheet(DarkTheme.get_stylesheet())

def main():
    """主函数"""
    print("=" * 60)
    print("最终主题修复测试")
    print("=" * 60)
    
    print("修复内容总结:")
    print("1. ✓ 修复了数据中心组件中的统计标签颜色")
    print("2. ✓ 修复了仪表盘组件中的所有标签颜色")
    print("3. ✓ 修复了数据概览仪表板的标题和状态标签")
    print("4. ✓ 统一设置了所有标签的文字颜色为白色")
    print("5. ✓ 添加了透明背景确保样式正确应用")
    
    print("\n启动测试窗口...")
    
    try:
        app = QApplication(sys.argv)
        
        window = ThemeTestMainWindow()
        window.show()
        
        print("✅ 测试窗口已显示")
        print("请检查以下内容:")
        print("- 仪表盘标签页中的所有文字是否清晰可见")
        print("- 数据中心标签页中的所有文字是否清晰可见")
        print("- 统计数据、状态信息等是否都能正常显示")
        print("- 没有白色文字配白色背景的问题")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()

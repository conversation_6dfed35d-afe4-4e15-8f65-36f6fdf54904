#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import warnings

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger

warnings.filterwarnings('ignore')

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class ReportGenerator:
    """报告生成器类"""

    def __init__(self):
        self.logger = get_logger("ReportGenerator")
        self.reports_dir = Path(__file__).parent / "output"
        self.reports_dir.mkdir(exist_ok=True)

    def generate_technical_analysis_report(self, symbol: str, data: pd.DataFrame,
                                         indicators_data: pd.DataFrame,
                                         analysis_results: Dict) -> str:
        """生成技术分析报告"""
        try:
            report_content = []

            # 报告标题
            report_content.append(f"# {symbol} 技术分析报告")
            report_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append(f"**分析周期**: {data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")
            report_content.append("")

            # 基本信息
            report_content.append("## 基本信息")
            latest_price = data['close'].iloc[-1]
            price_change = data['close'].iloc[-1] - data['close'].iloc[-2]
            price_change_pct = (price_change / data['close'].iloc[-2]) * 100

            report_content.append(f"- **最新价格**: {latest_price:.2f}")
            report_content.append(f"- **涨跌幅**: {price_change:+.2f} ({price_change_pct:+.2f}%)")
            report_content.append(f"- **成交量**: {data['volume'].iloc[-1]:,.0f}")
            report_content.append(f"- **数据点数**: {len(data)}")
            report_content.append("")

            # 技术指标分析
            report_content.append("## 技术指标分析")

            # 移动平均线分析
            if 'sma_5' in indicators_data.columns and 'sma_20' in indicators_data.columns:
                sma5 = indicators_data['sma_5'].iloc[-1]
                sma20 = indicators_data['sma_20'].iloc[-1]

                report_content.append("### 移动平均线")
                report_content.append(f"- **MA5**: {sma5:.2f}")
                report_content.append(f"- **MA20**: {sma20:.2f}")

                if latest_price > sma5 > sma20:
                    ma_signal = "强烈看涨"
                elif latest_price > sma5:
                    ma_signal = "看涨"
                elif latest_price < sma5 < sma20:
                    ma_signal = "强烈看跌"
                else:
                    ma_signal = "看跌"

                report_content.append(f"- **MA信号**: {ma_signal}")
                report_content.append("")

            # RSI分析
            if 'rsi_12' in indicators_data.columns:
                rsi = indicators_data['rsi_12'].iloc[-1]
                report_content.append("### RSI相对强弱指标")
                report_content.append(f"- **RSI值**: {rsi:.2f}")

                if rsi > 70:
                    rsi_signal = "超买，建议卖出"
                elif rsi < 30:
                    rsi_signal = "超卖，建议买入"
                else:
                    rsi_signal = "中性"

                report_content.append(f"- **RSI信号**: {rsi_signal}")
                report_content.append("")

            # MACD分析
            if all(col in indicators_data.columns for col in ['macd', 'macd_signal', 'macd_histogram']):
                macd = indicators_data['macd'].iloc[-1]
                macd_signal = indicators_data['macd_signal'].iloc[-1]
                macd_hist = indicators_data['macd_histogram'].iloc[-1]

                report_content.append("### MACD指标")
                report_content.append(f"- **MACD**: {macd:.4f}")
                report_content.append(f"- **信号线**: {macd_signal:.4f}")
                report_content.append(f"- **柱状图**: {macd_hist:.4f}")

                if macd > macd_signal and macd_hist > 0:
                    macd_trend = "金叉向上，看涨"
                elif macd < macd_signal and macd_hist < 0:
                    macd_trend = "死叉向下，看跌"
                else:
                    macd_trend = "震荡整理"

                report_content.append(f"- **MACD信号**: {macd_trend}")
                report_content.append("")

            # KDJ分析
            if all(col in indicators_data.columns for col in ['kdj_k', 'kdj_d', 'kdj_j']):
                kdj_k = indicators_data['kdj_k'].iloc[-1]
                kdj_d = indicators_data['kdj_d'].iloc[-1]
                kdj_j = indicators_data['kdj_j'].iloc[-1]

                report_content.append("### KDJ随机指标")
                report_content.append(f"- **K值**: {kdj_k:.2f}")
                report_content.append(f"- **D值**: {kdj_d:.2f}")
                report_content.append(f"- **J值**: {kdj_j:.2f}")

                if kdj_k > 80 and kdj_d > 80:
                    kdj_signal = "超买区域，注意风险"
                elif kdj_k < 20 and kdj_d < 20:
                    kdj_signal = "超卖区域，关注机会"
                else:
                    kdj_signal = "正常区间"

                report_content.append(f"- **KDJ信号**: {kdj_signal}")
                report_content.append("")

            # 布林带分析
            if all(col in indicators_data.columns for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                bb_upper = indicators_data['bb_upper'].iloc[-1]
                bb_middle = indicators_data['bb_middle'].iloc[-1]
                bb_lower = indicators_data['bb_lower'].iloc[-1]

                report_content.append("### 布林带")
                report_content.append(f"- **上轨**: {bb_upper:.2f}")
                report_content.append(f"- **中轨**: {bb_middle:.2f}")
                report_content.append(f"- **下轨**: {bb_lower:.2f}")

                if latest_price > bb_upper:
                    bb_signal = "突破上轨，强势上涨"
                elif latest_price < bb_lower:
                    bb_signal = "跌破下轨，弱势下跌"
                elif latest_price > bb_middle:
                    bb_signal = "位于中轨上方，偏强"
                else:
                    bb_signal = "位于中轨下方，偏弱"

                report_content.append(f"- **布林带信号**: {bb_signal}")
                report_content.append("")

            # 综合评价
            report_content.append("## 综合技术分析")

            # 计算综合评分
            signals = []
            if 'sma_5' in indicators_data.columns:
                if latest_price > indicators_data['sma_5'].iloc[-1]:
                    signals.append(1)
                else:
                    signals.append(-1)

            if 'rsi_12' in indicators_data.columns:
                rsi = indicators_data['rsi_12'].iloc[-1]
                if rsi > 70:
                    signals.append(-1)
                elif rsi < 30:
                    signals.append(1)
                else:
                    signals.append(0)

            if 'macd_histogram' in indicators_data.columns:
                if indicators_data['macd_histogram'].iloc[-1] > 0:
                    signals.append(1)
                else:
                    signals.append(-1)

            total_score = sum(signals)
            signal_count = len(signals)

            if total_score > signal_count * 0.5:
                overall_signal = "看涨"
                recommendation = "建议买入或持有"
            elif total_score < -signal_count * 0.5:
                overall_signal = "看跌"
                recommendation = "建议卖出或观望"
            else:
                overall_signal = "中性"
                recommendation = "建议观望或轻仓操作"

            report_content.append(f"- **综合信号**: {overall_signal}")
            report_content.append(f"- **操作建议**: {recommendation}")
            report_content.append(f"- **信号强度**: {abs(total_score)}/{signal_count}")
            report_content.append("")

            # 风险提示
            report_content.append("## 风险提示")
            report_content.append("- 技术分析仅供参考，不构成投资建议")
            report_content.append("- 市场有风险，投资需谨慎")
            report_content.append("- 请结合基本面分析和市场环境综合判断")
            report_content.append("- 建议设置止损位，控制投资风险")

            # 保存报告
            report_text = "\n".join(report_content)
            filename = f"{symbol}_technical_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            filepath = self.reports_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_text)

            self.logger.info(f"技术分析报告生成完成: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"生成技术分析报告失败: {e}")
            return ""

    def generate_fundamental_analysis_report(self, symbol: str, analysis_result: Dict) -> str:
        """生成基本面分析报告"""
        try:
            report_content = []

            # 报告标题
            report_content.append(f"# {symbol} 基本面分析报告")
            report_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append(f"**分析日期**: {analysis_result.get('analysis_date', 'N/A')}")
            report_content.append("")

            # 基本信息
            financial_data = analysis_result.get('financial_data', {})
            ratios = analysis_result.get('ratios', {})
            evaluation = analysis_result.get('evaluation', {})
            industry_comparison = analysis_result.get('industry_comparison', {})

            report_content.append("## 公司基本信息")
            report_content.append(f"- **股票代码**: {symbol}")
            report_content.append(f"- **所属行业**: {analysis_result.get('industry', 'N/A')}")
            report_content.append(f"- **市值**: {financial_data.get('market_cap', 0)/10000:.1f} 亿元")
            report_content.append(f"- **股价**: {financial_data.get('stock_price', 0):.2f} 元")
            report_content.append("")

            # 财务数据
            report_content.append("## 主要财务数据")
            report_content.append(f"- **营业收入**: {financial_data.get('revenue', 0)/10000:.1f} 亿元")
            report_content.append(f"- **净利润**: {financial_data.get('net_income', 0)/10000:.1f} 亿元")
            report_content.append(f"- **总资产**: {financial_data.get('total_assets', 0)/10000:.1f} 亿元")
            report_content.append(f"- **股东权益**: {financial_data.get('total_equity', 0)/10000:.1f} 亿元")
            report_content.append("")

            # 财务比率分析
            report_content.append("## 财务比率分析")

            report_content.append("### 盈利能力")
            report_content.append(f"- **净资产收益率(ROE)**: {ratios.get('净资产收益率(ROE)', 0):.2f}%")
            report_content.append(f"- **总资产收益率(ROA)**: {ratios.get('总资产收益率(ROA)', 0):.2f}%")
            report_content.append(f"- **净利润率**: {ratios.get('净利润率', 0):.2f}%")
            report_content.append(f"- **毛利率**: {ratios.get('毛利率', 0):.2f}%")
            report_content.append("")

            report_content.append("### 偿债能力")
            report_content.append(f"- **资产负债率**: {ratios.get('资产负债率', 0):.2f}%")
            report_content.append(f"- **流动比率**: {ratios.get('流动比率', 0):.2f}")
            report_content.append(f"- **权益乘数**: {ratios.get('权益乘数', 0):.2f}")
            report_content.append("")

            report_content.append("### 估值指标")
            report_content.append(f"- **市盈率(PE)**: {ratios.get('市盈率(PE)', 0):.2f}")
            report_content.append(f"- **市净率(PB)**: {ratios.get('市净率(PB)', 0):.2f}")
            report_content.append(f"- **每股收益(EPS)**: {ratios.get('每股收益(EPS)', 0):.2f} 元")
            report_content.append(f"- **每股净资产**: {ratios.get('每股净资产', 0):.2f} 元")
            report_content.append("")

            report_content.append("### 成长性指标")
            report_content.append(f"- **营收增长率**: {ratios.get('营收增长率', 0):.2f}%")
            report_content.append(f"- **净利润增长率**: {ratios.get('净利润增长率', 0):.2f}%")
            report_content.append(f"- **总资产增长率**: {ratios.get('总资产增长率', 0):.2f}%")
            report_content.append("")

            # 财务健康评估
            report_content.append("## 财务健康评估")
            report_content.append(f"- **综合评级**: {evaluation.get('综合评级', 'N/A')}")
            report_content.append(f"- **综合得分**: {evaluation.get('综合得分', 'N/A')}")
            report_content.append(f"- **盈利能力**: {evaluation.get('盈利能力', 'N/A')}")
            report_content.append(f"- **偿债能力**: {evaluation.get('偿债能力', 'N/A')}")
            report_content.append(f"- **估值水平**: {evaluation.get('估值水平', 'N/A')}")
            report_content.append(f"- **成长性**: {evaluation.get('成长性', 'N/A')}")
            report_content.append("")

            # 行业比较
            report_content.append("## 行业比较分析")
            industry = analysis_result.get('industry', '制造业')
            report_content.append(f"**与{industry}行业平均水平比较**:")

            key_metrics = ['净资产收益率(ROE)', '市盈率(PE)', '市净率(PB)', '资产负债率']
            for metric in key_metrics:
                if metric in industry_comparison:
                    comparison = industry_comparison[metric]
                    report_content.append(f"- **{metric}**: {comparison}")
            report_content.append("")

            # 投资建议
            report_content.append("## 投资建议")

            overall_rating = evaluation.get('综合评级', 'C')
            if overall_rating in ['A+', 'A']:
                investment_advice = "强烈推荐"
                risk_level = "低风险"
            elif overall_rating in ['B+', 'B']:
                investment_advice = "推荐"
                risk_level = "中等风险"
            else:
                investment_advice = "谨慎投资"
                risk_level = "高风险"

            report_content.append(f"- **投资建议**: {investment_advice}")
            report_content.append(f"- **风险等级**: {risk_level}")
            report_content.append("")

            # 风险提示
            report_content.append("## 风险提示")
            report_content.append("- 基本面分析基于历史财务数据，不代表未来表现")
            report_content.append("- 请关注公司最新公告和行业动态")
            report_content.append("- 投资决策应综合考虑技术面、基本面和市场环境")
            report_content.append("- 建议分散投资，控制单一标的风险敞口")

            # 保存报告
            report_text = "\n".join(report_content)
            filename = f"{symbol}_fundamental_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            filepath = self.reports_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_text)

            self.logger.info(f"基本面分析报告生成完成: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"生成基本面分析报告失败: {e}")
            return ""

    def generate_quantitative_analysis_report(self, symbols: List[str], risk_analysis: Dict,
                                            correlation_matrix: pd.DataFrame,
                                            pca_results: Dict) -> str:
        """生成量化分析报告"""
        try:
            report_content = []

            # 报告标题
            report_content.append(f"# 投资组合量化分析报告")
            report_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append(f"**分析标的**: {', '.join(symbols)}")
            report_content.append(f"**标的数量**: {len(symbols)}")
            report_content.append("")

            # 风险收益概览
            report_content.append("## 风险收益概览")

            # 创建汇总表格
            summary_data = []
            for symbol, metrics in risk_analysis.items():
                summary_data.append([
                    symbol,
                    f"{metrics.get('annual_return', 0)*100:.2f}%",
                    f"{metrics.get('annual_volatility', 0)*100:.2f}%",
                    f"{metrics.get('sharpe_ratio', 0):.3f}",
                    f"{metrics.get('max_drawdown', 0)*100:.2f}%"
                ])

            # 表格标题
            report_content.append("| 标的 | 年化收益率 | 年化波动率 | 夏普比率 | 最大回撤 |")
            report_content.append("|------|------------|------------|----------|----------|")

            # 表格数据
            for row in summary_data:
                report_content.append(f"| {' | '.join(row)} |")
            report_content.append("")

            # 详细风险分析
            report_content.append("## 详细风险分析")

            for symbol, metrics in risk_analysis.items():
                report_content.append(f"### {symbol}")
                report_content.append(f"- **年化收益率**: {metrics.get('annual_return', 0)*100:.2f}%")
                report_content.append(f"- **年化波动率**: {metrics.get('annual_volatility', 0)*100:.2f}%")
                report_content.append(f"- **夏普比率**: {metrics.get('sharpe_ratio', 0):.3f}")
                report_content.append(f"- **索提诺比率**: {metrics.get('sortino_ratio', 0):.3f}")
                report_content.append(f"- **卡玛比率**: {metrics.get('calmar_ratio', 0):.3f}")
                report_content.append(f"- **最大回撤**: {metrics.get('max_drawdown', 0)*100:.2f}%")
                report_content.append(f"- **VaR (5%)**: {metrics.get('var_5', 0)*100:.2f}%")
                report_content.append(f"- **CVaR (5%)**: {metrics.get('cvar_5', 0)*100:.2f}%")
                report_content.append(f"- **偏度**: {metrics.get('skewness', 0):.3f}")
                report_content.append(f"- **峰度**: {metrics.get('kurtosis', 0):.3f}")

                # 风险评级
                sharpe = metrics.get('sharpe_ratio', 0)
                if sharpe > 1.5:
                    risk_rating = "优秀"
                elif sharpe > 1.0:
                    risk_rating = "良好"
                elif sharpe > 0.5:
                    risk_rating = "一般"
                else:
                    risk_rating = "较差"

                report_content.append(f"- **风险评级**: {risk_rating}")
                report_content.append("")

            # 相关性分析
            if not correlation_matrix.empty:
                report_content.append("## 相关性分析")

                # 平均相关性
                corr_values = correlation_matrix.values
                upper_triangle = corr_values[np.triu_indices_from(corr_values, k=1)]
                avg_correlation = np.mean(upper_triangle)

                report_content.append(f"- **平均相关系数**: {avg_correlation:.3f}")

                # 高相关性股票对
                high_corr_pairs = []
                for i in range(len(correlation_matrix.columns)):
                    for j in range(i+1, len(correlation_matrix.columns)):
                        corr_value = correlation_matrix.iloc[i, j]
                        if abs(corr_value) > 0.7:
                            high_corr_pairs.append((
                                correlation_matrix.columns[i],
                                correlation_matrix.columns[j],
                                corr_value
                            ))

                if high_corr_pairs:
                    report_content.append("- **高相关性股票对** (|相关系数| > 0.7):")
                    for stock1, stock2, corr in high_corr_pairs:
                        report_content.append(f"  - {stock1} - {stock2}: {corr:.3f}")
                else:
                    report_content.append("- **高相关性股票对**: 无")

                report_content.append("")

                # 相关性矩阵
                report_content.append("### 相关性矩阵")

                # 表格标题
                header = "| 标的 | " + " | ".join(correlation_matrix.columns) + " |"
                separator = "|" + "------|" * (len(correlation_matrix.columns) + 1)

                report_content.append(header)
                report_content.append(separator)

                # 表格数据
                for i, index in enumerate(correlation_matrix.index):
                    row_data = [index] + [f"{correlation_matrix.iloc[i, j]:.3f}" for j in range(len(correlation_matrix.columns))]
                    report_content.append(f"| {' | '.join(row_data)} |")

                report_content.append("")

            # 主成分分析
            if pca_results:
                report_content.append("## 主成分分析")

                n_components = pca_results.get('n_components', 0)
                explained_variance = pca_results.get('explained_variance_ratio', [])
                cumulative_variance = pca_results.get('cumulative_variance_ratio', [])

                report_content.append(f"- **主成分数量**: {n_components}")
                report_content.append("")

                report_content.append("### 方差解释比例")
                report_content.append("| 主成分 | 解释方差比例 | 累计解释比例 |")
                report_content.append("|--------|--------------|--------------|")

                for i, (exp_var, cum_var) in enumerate(zip(explained_variance, cumulative_variance)):
                    report_content.append(f"| PC{i+1} | {exp_var:.3f} ({exp_var*100:.1f}%) | {cum_var:.3f} ({cum_var*100:.1f}%) |")

                report_content.append("")

                # 主成分贡献度
                if 'components' in pca_results:
                    components_df = pca_results['components']
                    report_content.append("### 主成分构成")

                    for i in range(min(3, n_components)):  # 只显示前3个主成分
                        pc_name = f"PC{i+1}"
                        report_content.append(f"#### {pc_name} (解释方差: {explained_variance[i]*100:.1f}%)")

                        # 获取贡献度最大的因子
                        pc_weights = components_df[pc_name].abs().sort_values(ascending=False)

                        for factor, weight in pc_weights.head(5).items():
                            original_weight = components_df.loc[factor, pc_name]
                            report_content.append(f"- **{factor}**: {original_weight:.3f}")

                        report_content.append("")

            # 投资组合建议
            report_content.append("## 投资组合建议")

            # 按夏普比率排序
            sorted_stocks = sorted(risk_analysis.items(),
                                 key=lambda x: x[1].get('sharpe_ratio', 0),
                                 reverse=True)

            report_content.append("### 按风险调整收益排序")
            for i, (symbol, metrics) in enumerate(sorted_stocks[:5], 1):
                sharpe = metrics.get('sharpe_ratio', 0)
                annual_return = metrics.get('annual_return', 0) * 100
                report_content.append(f"{i}. **{symbol}** - 夏普比率: {sharpe:.3f}, 年化收益: {annual_return:.2f}%")

            report_content.append("")

            # 风险分散建议
            report_content.append("### 风险分散建议")
            if avg_correlation > 0.7:
                report_content.append("- ⚠️ **高相关性警告**: 投资组合内标的相关性较高，分散化效果有限")
                report_content.append("- 建议增加不同行业或资产类别的标的")
            elif avg_correlation > 0.3:
                report_content.append("- ✅ **适度相关性**: 投资组合具有一定的分散化效果")
                report_content.append("- 可考虑适当调整权重配置")
            else:
                report_content.append("- ✅ **良好分散性**: 投资组合分散化程度较好")
                report_content.append("- 当前配置有助于降低整体风险")

            report_content.append("")

            # 风险提示
            report_content.append("## 风险提示")
            report_content.append("- 量化分析基于历史数据，不保证未来表现")
            report_content.append("- 相关性在市场极端情况下可能发生变化")
            report_content.append("- 建议定期重新评估投资组合配置")
            report_content.append("- 请根据个人风险承受能力调整投资策略")

            # 保存报告
            report_text = "\n".join(report_content)
            filename = f"quantitative_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            filepath = self.reports_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_text)

            self.logger.info(f"量化分析报告生成完成: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"生成量化分析报告失败: {e}")
            return ""

    def generate_comprehensive_report(self, symbol: str, technical_data: Dict,
                                    fundamental_data: Dict, strategy_results: Dict = None) -> str:
        """生成综合分析报告"""
        try:
            report_content = []

            # 报告标题
            report_content.append(f"# {symbol} 综合投资分析报告")
            report_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append("")

            # 执行摘要
            report_content.append("## 执行摘要")

            # 获取关键指标
            tech_signal = self._get_technical_summary(technical_data)
            fund_rating = fundamental_data.get('evaluation', {}).get('综合评级', 'C')

            report_content.append(f"- **技术面信号**: {tech_signal}")
            report_content.append(f"- **基本面评级**: {fund_rating}")

            # 综合评分
            tech_score = self._calculate_technical_score(technical_data)
            fund_score = self._calculate_fundamental_score(fund_rating)
            overall_score = (tech_score + fund_score) / 2

            if overall_score >= 80:
                overall_rating = "强烈推荐"
            elif overall_score >= 60:
                overall_rating = "推荐"
            elif overall_score >= 40:
                overall_rating = "中性"
            else:
                overall_rating = "不推荐"

            report_content.append(f"- **综合评级**: {overall_rating}")
            report_content.append(f"- **综合得分**: {overall_score:.1f}/100")
            report_content.append("")

            # 技术面分析摘要
            report_content.append("## 技术面分析摘要")
            if 'indicators_data' in technical_data:
                indicators = technical_data['indicators_data']
                latest_price = technical_data.get('data', {}).get('close', pd.Series()).iloc[-1] if 'data' in technical_data else 0

                # 主要技术指标
                if 'rsi_12' in indicators.columns:
                    rsi = indicators['rsi_12'].iloc[-1]
                    report_content.append(f"- **RSI**: {rsi:.2f} ({'超买' if rsi > 70 else '超卖' if rsi < 30 else '正常'})")

                if 'macd_histogram' in indicators.columns:
                    macd_hist = indicators['macd_histogram'].iloc[-1]
                    report_content.append(f"- **MACD**: {'金叉向上' if macd_hist > 0 else '死叉向下'}")

                if all(col in indicators.columns for col in ['kdj_k', 'kdj_d']):
                    kdj_k = indicators['kdj_k'].iloc[-1]
                    kdj_d = indicators['kdj_d'].iloc[-1]
                    kdj_signal = "超买" if kdj_k > 80 and kdj_d > 80 else "超卖" if kdj_k < 20 and kdj_d < 20 else "正常"
                    report_content.append(f"- **KDJ**: K={kdj_k:.1f}, D={kdj_d:.1f} ({kdj_signal})")

            report_content.append("")

            # 基本面分析摘要
            report_content.append("## 基本面分析摘要")
            ratios = fundamental_data.get('ratios', {})
            evaluation = fundamental_data.get('evaluation', {})

            report_content.append(f"- **ROE**: {ratios.get('净资产收益率(ROE)', 0):.2f}% ({evaluation.get('盈利能力', 'N/A')})")
            report_content.append(f"- **PE**: {ratios.get('市盈率(PE)', 0):.2f} ({evaluation.get('估值水平', 'N/A')})")
            report_content.append(f"- **资产负债率**: {ratios.get('资产负债率', 0):.2f}% ({evaluation.get('偿债能力', 'N/A')})")
            report_content.append(f"- **成长性**: {evaluation.get('成长性', 'N/A')}")
            report_content.append("")

            # 投资建议
            report_content.append("## 投资建议")

            # 根据技术面和基本面给出建议
            if tech_signal == "看涨" and fund_rating in ['A+', 'A']:
                recommendation = "强烈买入"
                reason = "技术面和基本面均表现优秀"
            elif tech_signal == "看涨" and fund_rating in ['B+', 'B']:
                recommendation = "买入"
                reason = "技术面向好，基本面稳健"
            elif tech_signal == "看跌" and fund_rating in ['C+', 'C']:
                recommendation = "卖出"
                reason = "技术面和基本面均表现不佳"
            elif tech_signal == "中性":
                recommendation = "观望"
                reason = "技术面信号不明确，建议等待更好时机"
            else:
                recommendation = "谨慎操作"
                reason = "技术面和基本面信号存在分歧"

            report_content.append(f"- **操作建议**: {recommendation}")
            report_content.append(f"- **建议理由**: {reason}")
            report_content.append("")

            # 风险评估
            report_content.append("## 风险评估")

            # 技术风险
            tech_risks = []
            if 'indicators_data' in technical_data:
                indicators = technical_data['indicators_data']
                if 'rsi_12' in indicators.columns and indicators['rsi_12'].iloc[-1] > 80:
                    tech_risks.append("RSI超买，存在回调风险")
                if 'bb_upper' in indicators.columns and 'data' in technical_data:
                    latest_price = technical_data['data']['close'].iloc[-1]
                    bb_upper = indicators['bb_upper'].iloc[-1]
                    if latest_price > bb_upper:
                        tech_risks.append("价格突破布林带上轨，波动性增加")

            # 基本面风险
            fund_risks = []
            if ratios.get('资产负债率', 0) > 70:
                fund_risks.append("资产负债率较高，财务杠杆风险")
            if ratios.get('营收增长率', 0) < 0:
                fund_risks.append("营收负增长，经营压力较大")

            if tech_risks:
                report_content.append("### 技术面风险")
                for risk in tech_risks:
                    report_content.append(f"- {risk}")

            if fund_risks:
                report_content.append("### 基本面风险")
                for risk in fund_risks:
                    report_content.append(f"- {risk}")

            if not tech_risks and not fund_risks:
                report_content.append("- 当前未发现明显风险点")

            report_content.append("")

            # 免责声明
            report_content.append("## 免责声明")
            report_content.append("- 本报告仅供参考，不构成投资建议")
            report_content.append("- 投资有风险，入市需谨慎")
            report_content.append("- 请根据自身风险承受能力做出投资决策")
            report_content.append("- 建议咨询专业投资顾问")

            # 保存报告
            report_text = "\n".join(report_content)
            filename = f"{symbol}_comprehensive_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            filepath = self.reports_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_text)

            self.logger.info(f"综合分析报告生成完成: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"生成综合分析报告失败: {e}")
            return ""

    def _get_technical_summary(self, technical_data: Dict) -> str:
        """获取技术面总结"""
        try:
            if 'indicators_data' not in technical_data:
                return "中性"

            indicators = technical_data['indicators_data']
            signals = []

            # RSI信号
            if 'rsi_12' in indicators.columns:
                rsi = indicators['rsi_12'].iloc[-1]
                if rsi > 70:
                    signals.append(-1)
                elif rsi < 30:
                    signals.append(1)
                else:
                    signals.append(0)

            # MACD信号
            if 'macd_histogram' in indicators.columns:
                macd_hist = indicators['macd_histogram'].iloc[-1]
                signals.append(1 if macd_hist > 0 else -1)

            # MA信号
            if 'sma_5' in indicators.columns and 'data' in technical_data:
                latest_price = technical_data['data']['close'].iloc[-1]
                ma5 = indicators['sma_5'].iloc[-1]
                signals.append(1 if latest_price > ma5 else -1)

            total_signal = sum(signals)
            if total_signal > 0:
                return "看涨"
            elif total_signal < 0:
                return "看跌"
            else:
                return "中性"

        except Exception:
            return "中性"

    def _calculate_technical_score(self, technical_data: Dict) -> float:
        """计算技术面得分"""
        try:
            signal = self._get_technical_summary(technical_data)
            if signal == "看涨":
                return 75.0
            elif signal == "看跌":
                return 25.0
            else:
                return 50.0
        except Exception:
            return 50.0

    def _calculate_fundamental_score(self, rating: str) -> float:
        """计算基本面得分"""
        rating_scores = {
            'A+': 95, 'A': 85, 'B+': 75, 'B': 65,
            'C+': 55, 'C': 45
        }
        return rating_scores.get(rating, 50.0)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
"""

import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

from .models import (
    SessionLocal, create_tables,
    StockInfo, StockDaily, StockMinute, IndexDaily,
    Strategy, BacktestResult, TradingRecord, Portfolio, Position
)
from utils.logger import get_logger


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.logger = get_logger("DatabaseManager")
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            create_tables()
            self.logger.info("数据库初始化成功")
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise e
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return SessionLocal()
    
    # 股票信息相关操作
    def save_stock_info(self, stock_data: pd.DataFrame) -> bool:
        """保存股票基本信息"""
        try:
            session = self.get_session()
            
            for _, row in stock_data.iterrows():
                # 检查是否已存在
                existing = session.query(StockInfo).filter(
                    StockInfo.symbol == row['symbol']
                ).first()
                
                if existing:
                    # 更新现有记录
                    existing.name = row.get('name', existing.name)
                    existing.market = row.get('market', existing.market)
                    existing.industry = row.get('industry', existing.industry)
                    existing.updated_at = datetime.now()
                else:
                    # 创建新记录
                    stock_info = StockInfo(
                        symbol=row['symbol'],
                        name=row.get('name', ''),
                        market=row.get('market', ''),
                        industry=row.get('industry', ''),
                        list_date=row.get('list_date', '')
                    )
                    session.add(stock_info)
            
            session.commit()
            session.close()
            self.logger.info(f"保存股票信息成功，共 {len(stock_data)} 条记录")
            return True
            
        except Exception as e:
            session.rollback()
            session.close()
            self.logger.error(f"保存股票信息失败: {e}")
            return False
    
    def get_stock_list(self, market: str = None) -> pd.DataFrame:
        """获取股票列表"""
        try:
            session = self.get_session()
            
            query = session.query(StockInfo).filter(StockInfo.is_active == True)
            
            if market:
                query = query.filter(StockInfo.market == market)
            
            stocks = query.all()
            session.close()
            
            if not stocks:
                return pd.DataFrame()
            
            data = []
            for stock in stocks:
                data.append({
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'market': stock.market,
                    'industry': stock.industry,
                    'list_date': stock.list_date
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()
    
    # 股票日线数据相关操作
    def save_stock_daily(self, symbol: str, data: pd.DataFrame) -> bool:
        """保存股票日线数据"""
        try:
            session = self.get_session()
            
            for date, row in data.iterrows():
                trade_date = date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date)
                
                # 检查是否已存在
                existing = session.query(StockDaily).filter(
                    and_(StockDaily.symbol == symbol, StockDaily.trade_date == trade_date)
                ).first()
                
                if existing:
                    # 更新现有记录
                    existing.open = float(row.get('open', 0))
                    existing.high = float(row.get('high', 0))
                    existing.low = float(row.get('low', 0))
                    existing.close = float(row.get('close', 0))
                    existing.volume = float(row.get('volume', 0))
                    existing.amount = float(row.get('amount', 0))
                    existing.pct_change = float(row.get('pct_change', 0))
                    existing.change = float(row.get('change', 0))
                    existing.turnover = float(row.get('turnover', 0))
                else:
                    # 创建新记录
                    daily_data = StockDaily(
                        symbol=symbol,
                        trade_date=trade_date,
                        open=float(row.get('open', 0)),
                        high=float(row.get('high', 0)),
                        low=float(row.get('low', 0)),
                        close=float(row.get('close', 0)),
                        volume=float(row.get('volume', 0)),
                        amount=float(row.get('amount', 0)),
                        pct_change=float(row.get('pct_change', 0)),
                        change=float(row.get('change', 0)),
                        turnover=float(row.get('turnover', 0))
                    )
                    session.add(daily_data)
            
            session.commit()
            session.close()
            self.logger.info(f"保存 {symbol} 日线数据成功，共 {len(data)} 条记录")
            return True
            
        except Exception as e:
            session.rollback()
            session.close()
            self.logger.error(f"保存 {symbol} 日线数据失败: {e}")
            return False
    
    def get_stock_daily(self, symbol: str, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """获取股票日线数据"""
        try:
            session = self.get_session()
            
            query = session.query(StockDaily).filter(StockDaily.symbol == symbol)
            
            if start_date:
                query = query.filter(StockDaily.trade_date >= start_date)
            if end_date:
                query = query.filter(StockDaily.trade_date <= end_date)
            
            query = query.order_by(asc(StockDaily.trade_date))
            
            records = query.all()
            session.close()
            
            if not records:
                return pd.DataFrame()
            
            data = []
            for record in records:
                data.append({
                    'date': record.trade_date,
                    'open': record.open,
                    'high': record.high,
                    'low': record.low,
                    'close': record.close,
                    'volume': record.volume,
                    'amount': record.amount,
                    'pct_change': record.pct_change,
                    'change': record.change,
                    'turnover': record.turnover
                })
            
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_date(self, symbol: str) -> Optional[str]:
        """获取股票最新数据日期"""
        try:
            session = self.get_session()
            
            latest = session.query(StockDaily).filter(
                StockDaily.symbol == symbol
            ).order_by(desc(StockDaily.trade_date)).first()
            
            session.close()
            
            return latest.trade_date if latest else None
            
        except Exception as e:
            self.logger.error(f"获取 {symbol} 最新日期失败: {e}")
            return None
    
    # 策略相关操作
    def save_strategy(self, strategy_data: Dict[str, Any]) -> int:
        """保存策略"""
        try:
            session = self.get_session()
            
            strategy = Strategy(
                name=strategy_data['name'],
                description=strategy_data.get('description', ''),
                category=strategy_data.get('category', ''),
                parameters=strategy_data.get('parameters', '{}')
            )
            
            session.add(strategy)
            session.commit()
            
            strategy_id = strategy.id
            session.close()
            
            self.logger.info(f"保存策略成功: {strategy_data['name']}")
            return strategy_id
            
        except Exception as e:
            session.rollback()
            session.close()
            self.logger.error(f"保存策略失败: {e}")
            return -1
    
    def get_strategies(self) -> pd.DataFrame:
        """获取策略列表"""
        try:
            session = self.get_session()
            
            strategies = session.query(Strategy).all()
            session.close()
            
            if not strategies:
                return pd.DataFrame()
            
            data = []
            for strategy in strategies:
                data.append({
                    'id': strategy.id,
                    'name': strategy.name,
                    'description': strategy.description,
                    'category': strategy.category,
                    'is_active': strategy.is_active,
                    'created_at': strategy.created_at
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"获取策略列表失败: {e}")
            return pd.DataFrame()
    
    # 回测结果相关操作
    def save_backtest_result(self, result_data: Dict[str, Any]) -> bool:
        """保存回测结果"""
        try:
            session = self.get_session()
            
            result = BacktestResult(
                strategy_id=result_data['strategy_id'],
                start_date=result_data['start_date'],
                end_date=result_data['end_date'],
                initial_capital=result_data['initial_capital'],
                final_capital=result_data['final_capital'],
                total_return=result_data['total_return'],
                annual_return=result_data['annual_return'],
                max_drawdown=result_data['max_drawdown'],
                sharpe_ratio=result_data['sharpe_ratio'],
                win_rate=result_data['win_rate'],
                profit_loss_ratio=result_data['profit_loss_ratio']
            )
            
            session.add(result)
            session.commit()
            session.close()
            
            self.logger.info("保存回测结果成功")
            return True
            
        except Exception as e:
            session.rollback()
            session.close()
            self.logger.error(f"保存回测结果失败: {e}")
            return False
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        try:
            session = self.get_session()
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 清理旧的分钟数据
            deleted = session.query(StockMinute).filter(
                StockMinute.datetime < cutoff_date
            ).delete()
            
            session.commit()
            session.close()
            
            self.logger.info(f"清理了 {deleted} 条旧的分钟数据")
            
        except Exception as e:
            session.rollback()
            session.close()
            self.logger.error(f"清理旧数据失败: {e}")

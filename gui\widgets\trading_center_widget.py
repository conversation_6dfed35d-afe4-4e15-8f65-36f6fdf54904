#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易中心组件
"""

import sys
import numpy as np
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QGroupBox, QTableWidget, QTableWidgetItem, QComboBox,
    QLineEdit, QDateEdit, QTabWidget,
    QSpinBox, QDoubleSpinBox, QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from trading.trading_manager import trading_manager


class TradingCenterWidget(QWidget):
    """交易中心组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("TradingCenterWidget")
        self.init_ui()
        self.setup_timer()
        self.setup_trading_manager()
        self.logger.info("交易中心组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_label = QLabel("交易中心")
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 手动交易标签页
        self.tab_widget.addTab(self.create_manual_trading_tab(), "手动交易")

        # 持仓管理标签页
        self.tab_widget.addTab(self.create_position_tab(), "持仓管理")

        # 订单管理标签页
        self.tab_widget.addTab(self.create_order_tab(), "订单管理")

        # 交易记录标签页
        self.tab_widget.addTab(self.create_history_tab(), "交易记录")

        layout.addWidget(self.tab_widget)

    def create_manual_trading_tab(self):
        """创建手动交易标签页"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # 左侧交易面板
        left_layout = QVBoxLayout()

        # 交易账户信息
        account_group = QGroupBox("账户信息")
        account_layout = QGridLayout(account_group)

        self.total_assets_label = QLabel("总资产: ¥1,000,000.00")


        self.total_assets_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_assets_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.available_cash_label = QLabel("可用资金: ¥800,000.00")

        self.available_cash_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.available_cash_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.market_value_label = QLabel("持仓市值: ¥200,000.00")

        self.market_value_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.market_value_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.today_pnl_label = QLabel("今日盈亏: ¥+5,000.00")

        self.today_pnl_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.today_pnl_label.setStyleSheet("color: #4CAF50; background-color: transparent;")

        account_layout.addWidget(self.total_assets_label, 0, 0)
        account_layout.addWidget(self.available_cash_label, 0, 1)
        account_layout.addWidget(self.market_value_label, 1, 0)
        account_layout.addWidget(self.today_pnl_label, 1, 1)

        left_layout.addWidget(account_group)

        # 下单面板
        order_group = QGroupBox("下单面板")
        order_layout = QGridLayout(order_group)

        # 股票代码
        symbol_label = QLabel("股票代码:")
        symbol_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        order_layout.addWidget(symbol_label, 0, 0)
        self.symbol_input = QLineEdit()
        self.symbol_input.setPlaceholderText("例如: 000001.SZ")
        order_layout.addWidget(self.symbol_input, 0, 1)

        # 买卖方向
        direction_label = QLabel("买卖方向:")
        direction_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        order_layout.addWidget(direction_label, 1, 0)
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["买入", "卖出"])
        order_layout.addWidget(self.direction_combo, 1, 1)

        # 订单类型
        order_type_label = QLabel("订单类型:")
        order_type_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        order_layout.addWidget(order_type_label, 2, 0)
        self.order_type_combo = QComboBox()
        self.order_type_combo.addItems(["市价单", "限价单", "止损单"])
        order_layout.addWidget(self.order_type_combo, 2, 1)

        # 数量
        quantity_label = QLabel("数量:")
        quantity_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        order_layout.addWidget(quantity_label, 3, 0)
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(100, 1000000)
        self.quantity_spin.setValue(1000)
        self.quantity_spin.setSingleStep(100)
        order_layout.addWidget(self.quantity_spin, 3, 1)

        # 价格
        price_label = QLabel("价格:")
        price_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        order_layout.addWidget(price_label, 4, 0)
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 9999.99)
        self.price_spin.setValue(10.00)
        self.price_spin.setDecimals(2)
        order_layout.addWidget(self.price_spin, 4, 1)

        # 下单按钮
        button_layout = QHBoxLayout()

        buy_btn = QPushButton("买入")
        buy_btn.setStyleSheet("QPushButton { background-color: red; color: white; font-weight: bold; }")
        buy_btn.clicked.connect(self.place_buy_order)
        button_layout.addWidget(buy_btn)

        sell_btn = QPushButton("卖出")
        sell_btn.setStyleSheet("QPushButton { background-color: green; color: white; font-weight: bold; }")
        sell_btn.clicked.connect(self.place_sell_order)
        button_layout.addWidget(sell_btn)

        order_layout.addLayout(button_layout, 5, 0, 1, 2)

        left_layout.addWidget(order_group)

        # 风险控制
        risk_group = QGroupBox("风险控制")
        risk_layout = QGridLayout(risk_group)

        self.enable_stop_loss_check = QCheckBox("启用止损")
        self.stop_loss_spin = QDoubleSpinBox()
        self.stop_loss_spin.setRange(0.01, 0.5)
        self.stop_loss_spin.setValue(0.1)
        self.stop_loss_spin.setSuffix("%")

        risk_layout.addWidget(self.enable_stop_loss_check, 0, 0)
        risk_layout.addWidget(self.stop_loss_spin, 0, 1)

        self.enable_take_profit_check = QCheckBox("启用止盈")
        self.take_profit_spin = QDoubleSpinBox()
        self.take_profit_spin.setRange(0.01, 1.0)
        self.take_profit_spin.setValue(0.2)
        self.take_profit_spin.setSuffix("%")

        risk_layout.addWidget(self.enable_take_profit_check, 1, 0)
        risk_layout.addWidget(self.take_profit_spin, 1, 1)

        left_layout.addWidget(risk_group)
        left_layout.addStretch()

        # 右侧市场信息
        right_layout = QVBoxLayout()

        # 实时行情
        quote_group = QGroupBox("实时行情")
        quote_layout = QVBoxLayout(quote_group)

        self.quote_table = QTableWidget()
        self.quote_table.setColumnCount(4)
        self.quote_table.setHorizontalHeaderLabels(["股票", "现价", "涨跌", "涨跌幅"])
        self.quote_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例行情数据
        self.add_sample_quotes()

        # 设置行情表格背景为蓝色
        self.quote_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        quote_layout.addWidget(self.quote_table)
        right_layout.addWidget(quote_group)

        # 五档行情
        depth_group = QGroupBox("五档行情")
        depth_layout = QVBoxLayout(depth_group)

        self.depth_table = QTableWidget()
        self.depth_table.setColumnCount(3)
        self.depth_table.setHorizontalHeaderLabels(["档位", "价格", "数量"])
        self.depth_table.setRowCount(10)

        # 添加示例五档数据
        self.add_sample_depth()

        # 设置五档行情表格背景为蓝色
        self.depth_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        depth_layout.addWidget(self.depth_table)
        right_layout.addWidget(depth_group)

        # 添加到主布局
        layout.addLayout(left_layout, 1)
        layout.addLayout(right_layout, 1)

        return widget

    def create_position_tab(self):
        """创建持仓管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 持仓统计
        stats_group = QGroupBox("持仓统计")
        stats_layout = QGridLayout(stats_group)

        self.position_count_label = QLabel("持仓股票: 5只")


        self.position_count_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.position_count_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.total_market_value_label = QLabel("总市值: ¥200,000.00")

        self.total_market_value_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_market_value_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.total_cost_label = QLabel("总成本: ¥195,000.00")

        self.total_cost_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_cost_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        self.total_profit_label = QLabel("浮动盈亏: ¥+5,000.00")

        self.total_profit_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_profit_label.setStyleSheet("color: #4CAF50; background-color: transparent;")

        stats_layout.addWidget(self.position_count_label, 0, 0)
        stats_layout.addWidget(self.total_market_value_label, 0, 1)
        stats_layout.addWidget(self.total_cost_label, 1, 0)
        stats_layout.addWidget(self.total_profit_label, 1, 1)

        layout.addWidget(stats_group)

        # 持仓明细
        positions_group = QGroupBox("持仓明细")
        positions_layout = QVBoxLayout(positions_group)

        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(8)
        self.positions_table.setHorizontalHeaderLabels([
            "股票代码", "股票名称", "持仓数量", "成本价", "现价", "市值", "盈亏", "盈亏比例"
        ])
        self.positions_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例持仓数据
        self.add_sample_positions()

        # 设置持仓表格背景为蓝色
        self.positions_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        positions_layout.addWidget(self.positions_table)

        # 持仓操作按钮
        button_layout = QHBoxLayout()

        sell_all_btn = QPushButton("全部卖出")
        sell_all_btn.clicked.connect(self.sell_all_positions)
        button_layout.addWidget(sell_all_btn)

        sell_profit_btn = QPushButton("卖出盈利")
        sell_profit_btn.clicked.connect(self.sell_profit_positions)
        button_layout.addWidget(sell_profit_btn)

        sell_loss_btn = QPushButton("止损卖出")
        sell_loss_btn.clicked.connect(self.sell_loss_positions)
        button_layout.addWidget(sell_loss_btn)

        button_layout.addStretch()
        positions_layout.addLayout(button_layout)

        layout.addWidget(positions_group)

        return widget

    def create_order_tab(self):
        """创建订单管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 订单筛选
        filter_group = QGroupBox("订单筛选")
        filter_layout = QHBoxLayout(filter_group)

        status_label = QLabel("状态:")
        status_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        filter_layout.addWidget(status_label)
        status_combo = QComboBox()
        status_combo.addItems(["全部", "待成交", "部分成交", "已成交", "已撤销"])
        filter_layout.addWidget(status_combo)

        date_label = QLabel("日期:")
        date_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        filter_layout.addWidget(date_label)
        date_combo = QComboBox()
        date_combo.addItems(["今日", "近3天", "近一周", "近一月"])
        filter_layout.addWidget(date_combo)

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_orders)
        filter_layout.addWidget(refresh_btn)

        filter_layout.addStretch()
        layout.addWidget(filter_group)

        # 订单列表
        orders_group = QGroupBox("订单列表")
        orders_layout = QVBoxLayout(orders_group)

        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(9)
        self.orders_table.setHorizontalHeaderLabels([
            "订单号", "股票代码", "方向", "类型", "数量", "价格", "状态", "成交时间", "操作"
        ])
        self.orders_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例订单数据
        self.add_sample_orders()

        # 设置订单表格背景为蓝色
        self.orders_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        orders_layout.addWidget(self.orders_table)
        layout.addWidget(orders_group)

        return widget

    def create_history_tab(self):
        """创建交易记录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 查询条件
        query_group = QGroupBox("查询条件")
        query_layout = QGridLayout(query_group)

        start_date_label = QLabel("开始日期:")
        start_date_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        query_layout.addWidget(start_date_label, 0, 0)
        self.history_start_date = QDateEdit()
        self.history_start_date.setDate(QDate.currentDate().addDays(-30))
        self.history_start_date.setCalendarPopup(True)
        query_layout.addWidget(self.history_start_date, 0, 1)

        end_date_label = QLabel("结束日期:")
        end_date_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        query_layout.addWidget(end_date_label, 0, 2)
        self.history_end_date = QDateEdit()
        self.history_end_date.setDate(QDate.currentDate())
        self.history_end_date.setCalendarPopup(True)
        query_layout.addWidget(self.history_end_date, 0, 3)

        query_btn = QPushButton("查询")
        query_btn.clicked.connect(self.query_history)
        query_layout.addWidget(query_btn, 0, 4)

        layout.addWidget(query_group)

        # 交易记录
        history_group = QGroupBox("交易记录")
        history_layout = QVBoxLayout(history_group)

        self.history_table = QTableWidget()
        self.history_table.setColumnCount(8)
        self.history_table.setHorizontalHeaderLabels([
            "交易时间", "股票代码", "方向", "数量", "价格", "金额", "手续费", "盈亏"
        ])
        self.history_table.horizontalHeader().setStretchLastSection(True)

        # 添加示例交易记录
        self.add_sample_history()

        # 设置交易记录表格背景为蓝色
        self.history_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        history_layout.addWidget(self.history_table)
        layout.addWidget(history_group)

        return widget

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(5000)  # 每5秒更新一次

    def setup_trading_manager(self):
        """设置交易管理器"""
        try:
            # 初始化模拟交易
            if trading_manager.initialize_broker("simulation"):
                trading_manager.start_trading()

                # 添加回调函数
                trading_manager.add_callback('account_update', self.on_account_update)
                trading_manager.add_callback('position_update', self.on_position_update)
                trading_manager.add_callback('order_update', self.on_order_update)

                self.logger.info("交易管理器初始化成功")
            else:
                self.logger.error("交易管理器初始化失败")
        except Exception as e:
            self.logger.error(f"设置交易管理器失败: {e}")

    def update_data(self):
        """更新数据"""
        # TODO: 实现实时数据更新
        pass

    def add_sample_quotes(self):
        """添加示例行情数据"""
        quotes = [
            ("000001.SZ", "12.50", "+0.25", "+2.04%"),
            ("600036.SH", "35.20", "-0.80", "-2.22%"),
            ("300015.SZ", "28.80", "+1.20", "+4.35%"),
        ]

        self.quote_table.setRowCount(len(quotes))
        for row, quote in enumerate(quotes):
            for col, value in enumerate(quote):
                item = QTableWidgetItem(str(value))
                if col == 2 or col == 3:  # 涨跌和涨跌幅列
                    if value.startswith('+'):
                        item.setForeground(QColor("red"))
                    elif value.startswith('-'):
                        item.setForeground(QColor("green"))
                self.quote_table.setItem(row, col, item)

    def add_sample_depth(self):
        """添加示例五档数据"""
        depth_data = [
            ("卖五", "12.55", "1000"),
            ("卖四", "12.54", "1500"),
            ("卖三", "12.53", "2000"),
            ("卖二", "12.52", "2500"),
            ("卖一", "12.51", "3000"),
            ("买一", "12.50", "3500"),
            ("买二", "12.49", "3000"),
            ("买三", "12.48", "2500"),
            ("买四", "12.47", "2000"),
            ("买五", "12.46", "1500"),
        ]

        for row, (level, price, volume) in enumerate(depth_data):
            self.depth_table.setItem(row, 0, QTableWidgetItem(level))
            self.depth_table.setItem(row, 1, QTableWidgetItem(price))
            self.depth_table.setItem(row, 2, QTableWidgetItem(volume))

    def add_sample_positions(self):
        """添加示例持仓数据"""
        positions = [
            ("000001.SZ", "平安银行", "1000", "12.00", "12.50", "12500", "+500", "+4.17%"),
            ("600036.SH", "招商银行", "500", "36.00", "35.20", "17600", "-400", "-2.22%"),
            ("300015.SZ", "爱尔眼科", "800", "27.60", "28.80", "23040", "+960", "+4.35%"),
        ]

        self.positions_table.setRowCount(len(positions))
        for row, position in enumerate(positions):
            for col, value in enumerate(position):
                item = QTableWidgetItem(str(value))
                if col == 6 or col == 7:  # 盈亏列
                    if value.startswith('+'):
                        item.setForeground(QColor("red"))
                    elif value.startswith('-'):
                        item.setForeground(QColor("green"))
                self.positions_table.setItem(row, col, item)

    def add_sample_orders(self):
        """添加示例订单数据"""
        orders = [
            ("20231201001", "000001.SZ", "买入", "限价", "1000", "12.50", "已成交", "09:30:15", ""),
            ("20231201002", "600036.SH", "卖出", "市价", "500", "35.20", "已成交", "10:15:30", ""),
            ("20231201003", "300015.SZ", "买入", "限价", "800", "28.50", "待成交", "", "撤销"),
        ]

        self.orders_table.setRowCount(len(orders))
        for row, order in enumerate(orders):
            for col, value in enumerate(order):
                self.orders_table.setItem(row, col, QTableWidgetItem(str(value)))

    def add_sample_history(self):
        """添加示例交易记录"""
        history = [
            ("2023-12-01 09:30:15", "000001.SZ", "买入", "1000", "12.50", "12500", "3.75", ""),
            ("2023-12-01 10:15:30", "600036.SH", "卖出", "500", "35.20", "17600", "5.28", "+800"),
            ("2023-11-30 14:30:20", "300015.SZ", "买入", "800", "27.60", "22080", "6.62", ""),
        ]

        self.history_table.setRowCount(len(history))
        for row, record in enumerate(history):
            for col, value in enumerate(record):
                item = QTableWidgetItem(str(value))
                if col == 7 and value.startswith('+'):  # 盈亏列
                    item.setForeground(QColor("red"))
                self.history_table.setItem(row, col, item)

    def place_buy_order(self):
        """下买单"""
        self._place_order("buy")

    def place_sell_order(self):
        """下卖单"""
        self._place_order("sell")

    def _place_order(self, side: str):
        """下单通用方法"""
        try:
            symbol = self.symbol_input.text().strip()
            if not symbol:
                self.logger.warning("请输入股票代码")
                return

            quantity = self.quantity_spin.value()
            price = self.price_spin.value()
            order_type = "limit" if self.order_type_combo.currentText() == "限价单" else "market"

            order_id = trading_manager.place_order(symbol, side, order_type, quantity, price)

            if order_id:
                self.logger.info(f"订单提交成功: {order_id}")
                self.refresh_orders()
            else:
                self.logger.error("订单提交失败")

        except Exception as e:
            self.logger.error(f"下单失败: {e}")

    def sell_all_positions(self):
        """全部卖出"""
        try:
            # TODO: 实现全部卖出逻辑
            self.logger.info("执行全部卖出")
        except Exception as e:
            self.logger.error(f"全部卖出失败: {e}")

    def sell_profit_positions(self):
        """卖出盈利持仓"""
        try:
            # TODO: 实现卖出盈利持仓逻辑
            self.logger.info("执行卖出盈利持仓")
        except Exception as e:
            self.logger.error(f"卖出盈利持仓失败: {e}")

    def sell_loss_positions(self):
        """止损卖出"""
        try:
            # TODO: 实现止损卖出逻辑
            self.logger.info("执行止损卖出")
        except Exception as e:
            self.logger.error(f"止损卖出失败: {e}")

    def refresh_orders(self):
        """刷新订单"""
        try:
            # TODO: 从交易管理器获取最新订单
            self.logger.info("刷新订单列表")
            self.add_sample_orders()
        except Exception as e:
            self.logger.error(f"刷新订单失败: {e}")

    def query_history(self):
        """查询交易记录"""
        try:
            start_date = self.history_start_date.date().toString("yyyy-MM-dd")
            end_date = self.history_end_date.date().toString("yyyy-MM-dd")

            # TODO: 从数据库查询交易记录
            self.logger.info(f"查询交易记录: {start_date} 至 {end_date}")
            self.add_sample_history()
        except Exception as e:
            self.logger.error(f"查询交易记录失败: {e}")

    def on_account_update(self, account_info):
        """账户信息更新回调"""
        try:
            if account_info:
                self.total_assets_label.setText(f"总资产: ¥{account_info.total_assets:,.2f}")
                self.available_cash_label.setText(f"可用资金: ¥{account_info.available_cash:,.2f}")
                self.market_value_label.setText(f"持仓市值: ¥{account_info.market_value:,.2f}")

                # 计算今日盈亏
                today_pnl = account_info.total_assets - account_info.yesterday_assets
                pnl_color = "red" if today_pnl >= 0 else "green"
                pnl_sign = "+" if today_pnl >= 0 else ""

                self.today_pnl_label.setText(f"今日盈亏: ¥{pnl_sign}{today_pnl:,.2f}")
                self.today_pnl_label.setStyleSheet(f"color: {pnl_color};")

        except Exception as e:
            self.logger.error(f"更新账户信息失败: {e}")

    def on_position_update(self, positions):
        """持仓信息更新回调"""
        try:
            if positions:
                # 更新持仓统计
                position_count = len(positions)
                total_market_value = sum(pos.market_value for pos in positions.values())
                total_cost = sum(pos.cost for pos in positions.values())
                total_profit = total_market_value - total_cost

                self.position_count_label.setText(f"持仓股票: {position_count}只")
                self.total_market_value_label.setText(f"总市值: ¥{total_market_value:,.2f}")
                self.total_cost_label.setText(f"总成本: ¥{total_cost:,.2f}")

                profit_color = "red" if total_profit >= 0 else "green"
                profit_sign = "+" if total_profit >= 0 else ""
                self.total_profit_label.setText(f"浮动盈亏: ¥{profit_sign}{total_profit:,.2f}")
                self.total_profit_label.setStyleSheet(f"color: {profit_color};")

                # 更新持仓明细表格
                self.update_positions_table(positions)

        except Exception as e:
            self.logger.error(f"更新持仓信息失败: {e}")

    def on_order_update(self, orders):
        """订单信息更新回调"""
        try:
            if orders:
                # 更新订单表格
                self.update_orders_table(orders)
        except Exception as e:
            self.logger.error(f"更新订单信息失败: {e}")

    def update_positions_table(self, positions):
        """更新持仓表格"""
        try:
            self.positions_table.setRowCount(len(positions))

            for row, (symbol, position) in enumerate(positions.items()):
                # 股票代码
                self.positions_table.setItem(row, 0, QTableWidgetItem(symbol))

                # 股票名称 (TODO: 从数据库获取)
                self.positions_table.setItem(row, 1, QTableWidgetItem("股票名称"))

                # 持仓数量
                self.positions_table.setItem(row, 2, QTableWidgetItem(str(position.quantity)))

                # 成本价
                self.positions_table.setItem(row, 3, QTableWidgetItem(f"{position.avg_price:.2f}"))

                # 现价 (TODO: 从行情获取)
                current_price = position.avg_price * (1 + np.random.uniform(-0.05, 0.05))
                self.positions_table.setItem(row, 4, QTableWidgetItem(f"{current_price:.2f}"))

                # 市值
                market_value = position.quantity * current_price
                self.positions_table.setItem(row, 5, QTableWidgetItem(f"{market_value:.2f}"))

                # 盈亏
                profit = market_value - position.cost
                profit_item = QTableWidgetItem(f"{profit:+.2f}")
                profit_item.setForeground(QColor("red" if profit >= 0 else "green"))
                self.positions_table.setItem(row, 6, profit_item)

                # 盈亏比例
                profit_ratio = (profit / position.cost) * 100
                ratio_item = QTableWidgetItem(f"{profit_ratio:+.2f}%")
                ratio_item.setForeground(QColor("red" if profit_ratio >= 0 else "green"))
                self.positions_table.setItem(row, 7, ratio_item)

        except Exception as e:
            self.logger.error(f"更新持仓表格失败: {e}")

    def update_orders_table(self, orders):
        """更新订单表格"""
        try:
            self.orders_table.setRowCount(len(orders))

            for row, (order_id, order) in enumerate(orders.items()):
                # 订单号
                self.orders_table.setItem(row, 0, QTableWidgetItem(order_id))

                # 股票代码
                self.orders_table.setItem(row, 1, QTableWidgetItem(order.symbol))

                # 方向
                direction = "买入" if order.side == "buy" else "卖出"
                self.orders_table.setItem(row, 2, QTableWidgetItem(direction))

                # 类型
                order_type = "限价" if order.order_type == "limit" else "市价"
                self.orders_table.setItem(row, 3, QTableWidgetItem(order_type))

                # 数量
                self.orders_table.setItem(row, 4, QTableWidgetItem(str(order.quantity)))

                # 价格
                price_text = f"{order.price:.2f}" if order.price else "市价"
                self.orders_table.setItem(row, 5, QTableWidgetItem(price_text))

                # 状态
                status_map = {
                    "pending": "待成交",
                    "partial": "部分成交",
                    "filled": "已成交",
                    "cancelled": "已撤销"
                }
                status = status_map.get(order.status, order.status)
                self.orders_table.setItem(row, 6, QTableWidgetItem(status))

                # 成交时间
                fill_time = order.fill_time.strftime("%H:%M:%S") if order.fill_time else ""
                self.orders_table.setItem(row, 7, QTableWidgetItem(fill_time))

                # 操作按钮
                if order.status == "pending":
                    cancel_btn = QPushButton("撤销")
                    cancel_btn.clicked.connect(lambda: self.cancel_order(order_id))
                    self.orders_table.setCellWidget(row, 8, cancel_btn)

        except Exception as e:
            self.logger.error(f"更新订单表格失败: {e}")

    def cancel_order(self, order_id: str):
        """撤销订单"""
        try:
            if trading_manager.cancel_order(order_id):
                self.logger.info(f"订单撤销成功: {order_id}")
                self.refresh_orders()
            else:
                self.logger.error(f"订单撤销失败: {order_id}")
        except Exception as e:
            self.logger.error(f"撤销订单失败: {e}")

    def sell_all_positions(self):
        """全部卖出"""
        try:
            positions = trading_manager.get_positions()
            for position in positions:
                if position['quantity'] > 0:
                    trading_manager.place_order(
                        position['symbol'],
                        "sell",
                        "market",
                        position['quantity']
                    )
            self.logger.info("全部卖出指令已提交")
        except Exception as e:
            self.logger.error(f"全部卖出失败: {e}")

    def sell_profit_positions(self):
        """卖出盈利"""
        try:
            positions = trading_manager.get_positions()
            for position in positions:
                if position['quantity'] > 0 and position['unrealized_pnl'] > 0:
                    trading_manager.place_order(
                        position['symbol'],
                        "sell",
                        "market",
                        position['quantity']
                    )
            self.logger.info("盈利持仓卖出指令已提交")
        except Exception as e:
            self.logger.error(f"卖出盈利失败: {e}")

    def sell_loss_positions(self):
        """止损卖出"""
        try:
            positions = trading_manager.get_positions()
            for position in positions:
                if position['quantity'] > 0 and position['unrealized_pnl'] < 0:
                    # 检查是否达到止损条件（亏损超过10%）
                    loss_ratio = position['unrealized_pnl'] / (position['quantity'] * position['avg_price'])
                    if loss_ratio < -0.1:  # 亏损超过10%
                        trading_manager.place_order(
                            position['symbol'],
                            "sell",
                            "market",
                            position['quantity']
                        )
            self.logger.info("止损卖出指令已提交")
        except Exception as e:
            self.logger.error(f"止损卖出失败: {e}")

    def refresh_orders(self):
        """刷新订单"""
        try:
            orders = trading_manager.get_orders()
            self.update_orders_table(orders)
            self.logger.info("订单列表已刷新")
        except Exception as e:
            self.logger.error(f"刷新订单失败: {e}")

    def query_history(self):
        """查询历史"""
        try:
            start_date = self.history_start_date.date().toPyDate()
            end_date = self.history_end_date.date().toPyDate()

            trades = trading_manager.get_trades(
                start_date=datetime.combine(start_date, datetime.min.time()),
                end_date=datetime.combine(end_date, datetime.max.time())
            )

            self.update_history_table(trades)
            self.logger.info(f"查询到{len(trades)}条交易记录")
        except Exception as e:
            self.logger.error(f"查询历史失败: {e}")

    def on_account_update(self, account_data):
        """账户信息更新回调"""
        try:
            self.total_assets_label.setText(f"总资产: ¥{account_data['total_assets']:,.2f}")
            self.available_cash_label.setText(f"可用资金: ¥{account_data['available_cash']:,.2f}")
            self.market_value_label.setText(f"持仓市值: ¥{account_data['market_value']:,.2f}")
            self.today_pnl_label.setText(f"今日盈亏: ¥{account_data.get('today_pnl', 0):+,.2f}")
        except Exception as e:
            self.logger.error(f"更新账户信息显示失败: {e}")

    def on_position_update(self, positions_data):
        """持仓信息更新回调"""
        try:
            self.update_positions_table(positions_data)

            # 更新持仓统计
            total_count = len(positions_data)
            total_market_value = sum(pos['market_value'] for pos in positions_data)
            total_cost = sum(pos['quantity'] * pos['avg_price'] for pos in positions_data)
            total_profit = sum(pos['unrealized_pnl'] for pos in positions_data)

            self.position_count_label.setText(f"持仓股票: {total_count}只")
            self.total_market_value_label.setText(f"总市值: ¥{total_market_value:,.2f}")
            self.total_cost_label.setText(f"总成本: ¥{total_cost:,.2f}")
            self.total_profit_label.setText(f"浮动盈亏: ¥{total_profit:+,.2f}")

        except Exception as e:
            self.logger.error(f"更新持仓信息显示失败: {e}")

    def on_order_update(self, order_data):
        """订单信息更新回调"""
        try:
            self.refresh_orders()
        except Exception as e:
            self.logger.error(f"更新订单信息显示失败: {e}")

    def update_positions_table(self, positions_data):
        """更新持仓表格"""
        try:
            self.positions_table.setRowCount(len(positions_data))

            for row, position in enumerate(positions_data):
                items = [
                    position['symbol'],
                    position['symbol'],  # 股票名称，暂时用代码代替
                    str(position['quantity']),
                    f"{position['avg_price']:.2f}",
                    f"{position['market_value'] / position['quantity']:.2f}",  # 现价
                    f"{position['market_value']:.2f}",
                    f"{position['unrealized_pnl']:+.2f}",
                    f"{position['unrealized_pnl'] / (position['quantity'] * position['avg_price']) * 100:+.2f}%"
                ]

                for col, value in enumerate(items):
                    item = QTableWidgetItem(str(value))
                    if col == 6 or col == 7:  # 盈亏列
                        if position['unrealized_pnl'] > 0:
                            item.setForeground(QColor("red"))
                        elif position['unrealized_pnl'] < 0:
                            item.setForeground(QColor("green"))
                    self.positions_table.setItem(row, col, item)

        except Exception as e:
            self.logger.error(f"更新持仓表格失败: {e}")

    def update_orders_table(self, orders_data):
        """更新订单表格"""
        try:
            self.orders_table.setRowCount(len(orders_data))

            for row, order in enumerate(orders_data):
                items = [
                    order['order_id'],
                    order['symbol'],
                    "买入" if order['side'] == 'buy' else "卖出",
                    "市价单" if order['order_type'] == 'market' else "限价单",
                    str(order['quantity']),
                    f"{order.get('price', 0):.2f}",
                    order['status'],
                    order.get('update_time', '').strftime('%H:%M:%S') if order.get('update_time') else '',
                    ""
                ]

                for col, value in enumerate(items):
                    self.orders_table.setItem(row, col, QTableWidgetItem(str(value)))

        except Exception as e:
            self.logger.error(f"更新订单表格失败: {e}")

    def update_history_table(self, trades_data):
        """更新交易记录表格"""
        try:
            self.history_table.setRowCount(len(trades_data))

            for row, trade in enumerate(trades_data):
                items = [
                    trade.get('trade_time', '').strftime('%Y-%m-%d %H:%M:%S') if trade.get('trade_time') else '',
                    trade['symbol'],
                    "买入" if trade['side'] == 'buy' else "卖出",
                    str(trade['quantity']),
                    f"{trade['price']:.2f}",
                    f"{trade['amount']:.2f}",
                    f"{trade.get('commission', 0):.2f}",
                    ""  # 盈亏暂时为空
                ]

                for col, value in enumerate(items):
                    self.history_table.setItem(row, col, QTableWidgetItem(str(value)))

        except Exception as e:
            self.logger.error(f"更新交易记录表格失败: {e}")

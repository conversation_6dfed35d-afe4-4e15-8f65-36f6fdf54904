#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强功能
验证新增的8个核心功能模块
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import setup_logger, get_logger
from utils.report_generator import create_report_generator, create_sample_report_data
from data.realtime_feed import create_realtime_feed
from trading.real_broker import create_broker
from gui.widgets.chart_widget import ChartWidget
from gui.widgets.settings_widget import SettingsWidget


def test_gui_enhancements():
    """测试GUI功能增强"""
    print("\n" + "="*50)
    print("测试GUI功能增强")
    print("="*50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 测试图表组件
        print("✓ 测试图表组件...")
        chart_widget = ChartWidget()
        print("  - 图表组件创建成功")
        
        # 测试设置界面
        print("✓ 测试设置界面...")
        settings_widget = SettingsWidget()
        print("  - 设置界面创建成功")
        
        # 清理
        app.quit()
        
        print("✅ GUI功能增强测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI功能增强测试失败: {e}")
        return False


def test_real_trading_interface():
    """测试实盘交易接口"""
    print("\n" + "="*50)
    print("测试实盘交易接口")
    print("="*50)
    
    try:
        # 测试华泰证券接口（沙盒模式）
        print("✓ 测试华泰证券接口...")
        
        config = {
            'broker_name': 'huatai',
            'api_key': 'test_api_key',
            'secret_key': 'test_secret_key',
            'account_id': 'test_account',
            'base_url': 'https://api.huatai.com',
            'sandbox': True
        }
        
        broker = create_broker('huatai', config)
        if broker:
            print("  - 华泰证券接口创建成功")
            
            # 测试连接
            if broker.connect():
                print("  - 连接成功")
                
                # 测试获取账户信息
                account_info = broker.get_account_info()
                if account_info:
                    print(f"  - 账户信息: 总资产 {account_info.total_assets}")
                
                # 测试获取持仓
                positions = broker.get_positions()
                print(f"  - 持仓数量: {len(positions)}")
                
                # 测试下单
                from trading.base_broker import OrderSide, OrderType
                order_id = broker.place_order(
                    "000001.SZ", OrderSide.BUY, OrderType.LIMIT, 1000, 12.50
                )
                if order_id:
                    print(f"  - 下单成功: {order_id}")
                
                broker.disconnect()
            else:
                print("  - 连接失败")
        
        print("✅ 实盘交易接口测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 实盘交易接口测试失败: {e}")
        return False


def test_realtime_data_feed():
    """测试实时数据推送"""
    print("\n" + "="*50)
    print("测试实时数据推送")
    print("="*50)
    
    try:
        # 测试新浪实时数据源
        print("✓ 测试新浪实时数据源...")
        
        feed = create_realtime_feed('sina')
        if feed:
            print("  - 新浪数据源创建成功")
            
            # 添加回调函数
            def on_tick_data(tick_data):
                print(f"  - 收到Tick数据: {tick_data.symbol} {tick_data.price}")
            
            def on_error(error):
                print(f"  - 数据源错误: {error}")
            
            feed.add_callback('tick', on_tick_data)
            feed.add_callback('error', on_error)
            
            # 连接并订阅
            if feed.connect():
                print("  - 连接成功")
                
                # 订阅股票
                feed.subscribe(['000001.SZ', '600036.SH'])
                print("  - 订阅股票成功")
                
                # 等待一段时间接收数据
                print("  - 等待接收数据...")
                time.sleep(3)
                
                # 断开连接
                feed.disconnect()
                print("  - 断开连接")
            else:
                print("  - 连接失败")
        
        print("✅ 实时数据推送测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 实时数据推送测试失败: {e}")
        return False


def test_report_generation():
    """测试报告导出功能"""
    print("\n" + "="*50)
    print("测试报告导出功能")
    print("="*50)
    
    try:
        # 创建示例报告数据
        report_data = create_sample_report_data()
        print("✓ 创建示例报告数据成功")
        
        # 测试HTML报告生成
        print("✓ 测试HTML报告生成...")
        html_generator = create_report_generator('html')
        if html_generator:
            html_file = html_generator.generate_html_report(report_data)
            if html_file:
                print(f"  - HTML报告生成成功: {html_file}")
            else:
                print("  - HTML报告生成失败")
        
        # 测试Excel报告生成
        print("✓ 测试Excel报告生成...")
        excel_generator = create_report_generator('excel')
        if excel_generator:
            excel_file = excel_generator.generate_excel_report(report_data)
            if excel_file:
                print(f"  - Excel报告生成成功: {excel_file}")
            else:
                print("  - Excel报告生成失败")
        
        # 测试PDF报告生成
        print("✓ 测试PDF报告生成...")
        pdf_generator = create_report_generator('pdf')
        if pdf_generator:
            pdf_file = pdf_generator.generate_pdf_report(report_data)
            if pdf_file:
                print(f"  - PDF报告生成成功: {pdf_file}")
            else:
                print("  - PDF报告生成失败（可能缺少ReportLab库）")
        
        print("✅ 报告导出功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 报告导出功能测试失败: {e}")
        return False


def test_enhanced_strategies():
    """测试增强策略类型"""
    print("\n" + "="*50)
    print("测试增强策略类型")
    print("="*50)
    
    try:
        from strategies.strategy_factory import StrategyFactory
        
        # 测试策略工厂
        print("✓ 测试策略工厂...")
        factory = StrategyFactory()
        
        # 获取可用策略
        available_strategies = factory.get_available_strategies()
        print(f"  - 可用策略数量: {len(available_strategies)}")
        
        for strategy_name in available_strategies:
            print(f"    - {strategy_name}")
        
        # 测试创建策略
        if 'simple_ml' in available_strategies:
            print("✓ 测试创建ML策略...")
            strategy = factory.create_strategy('simple_ml', {
                'symbol': '000001.SZ',
                'lookback_periods': [5, 10, 20],
                'feature_selection': True
            })
            if strategy:
                print("  - ML策略创建成功")
        
        print("✅ 增强策略类型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 增强策略类型测试失败: {e}")
        return False


def test_ml_enhancements():
    """测试机器学习模块增强"""
    print("\n" + "="*50)
    print("测试机器学习模块增强")
    print("="*50)
    
    try:
        from ml.model_manager import ModelManager
        from ml.feature_engineering import FeatureEngineer
        
        # 测试特征工程
        print("✓ 测试特征工程...")
        feature_engineer = FeatureEngineer()
        
        # 创建示例数据
        import pandas as pd
        import numpy as np
        
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        data = pd.DataFrame({
            'open': np.random.uniform(10, 15, 100),
            'high': np.random.uniform(15, 20, 100),
            'low': np.random.uniform(8, 12, 100),
            'close': np.random.uniform(10, 15, 100),
            'volume': np.random.randint(1000000, 10000000, 100)
        }, index=dates)
        
        # 生成特征
        features = feature_engineer.create_features(data, [5, 10, 20])
        print(f"  - 生成特征数量: {len(features.columns)}")
        
        # 测试模型管理器
        print("✓ 测试模型管理器...")
        model_manager = ModelManager()
        
        # 获取可用模型
        available_models = model_manager.get_available_models()
        print(f"  - 可用模型数量: {len(available_models)}")
        
        print("✅ 机器学习模块增强测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 机器学习模块增强测试失败: {e}")
        return False


def test_backtest_enhancements():
    """测试回测引擎增强"""
    print("\n" + "="*50)
    print("测试回测引擎增强")
    print("="*50)
    
    try:
        from backtesting.backtest_engine import BacktestEngine
        
        # 测试回测引擎
        print("✓ 测试回测引擎...")
        engine = BacktestEngine()
        
        # 设置回测参数
        config = {
            'initial_capital': 1000000,
            'commission_rate': 0.0003,
            'slippage': 0.001,
            'start_date': '2024-01-01',
            'end_date': '2024-01-31'
        }
        
        engine.set_config(config)
        print("  - 回测配置设置成功")
        
        print("✅ 回测引擎增强测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 回测引擎增强测试失败: {e}")
        return False


def test_user_settings():
    """测试用户配置功能"""
    print("\n" + "="*50)
    print("测试用户配置功能")
    print("="*50)
    
    try:
        # 测试设置文件读写
        print("✓ 测试设置文件读写...")
        
        settings_file = project_root / "config" / "test_settings.json"
        
        # 创建测试设置
        test_settings = {
            'theme': 'dark',
            'auto_start': True,
            'log_level': 'INFO',
            'commission_rate': 0.0003
        }
        
        # 保存设置
        import json
        settings_file.parent.mkdir(exist_ok=True)
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2)
        
        print("  - 设置保存成功")
        
        # 读取设置
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        if loaded_settings == test_settings:
            print("  - 设置读取成功")
        
        # 清理测试文件
        settings_file.unlink()
        
        print("✅ 用户配置功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 用户配置功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("量化交易系统增强功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now()}")
    
    # 设置日志
    logger = setup_logger()
    
    # 测试结果统计
    test_results = []
    
    # 执行各项测试
    test_functions = [
        ("GUI功能增强", test_gui_enhancements),
        ("实盘交易接口", test_real_trading_interface),
        ("实时数据推送", test_realtime_data_feed),
        ("报告导出功能", test_report_generation),
        ("增强策略类型", test_enhanced_strategies),
        ("机器学习增强", test_ml_enhancements),
        ("回测引擎增强", test_backtest_enhancements),
        ("用户配置功能", test_user_settings),
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n总计: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("\n🎉 所有增强功能测试通过！")
        print("\n系统功能完整性总结:")
        print("✓ 完整的GUI功能面板 - 仪表盘、图表、设置界面")
        print("✓ 实盘交易接口 - 支持华泰证券等主流券商")
        print("✓ 更多策略类型 - 技术分析、机器学习、量化因子")
        print("✓ 机器学习模块 - 特征工程、模型管理、策略优化")
        print("✓ 完整回测引擎 - 滑点、手续费、风险控制")
        print("✓ 实时数据推送 - 新浪、腾讯等多数据源")
        print("✓ 用户配置界面 - 系统设置、交易参数、风险控制")
        print("✓ 报告导出功能 - PDF、Excel、HTML多格式")
        
        print("\n系统特色:")
        print("• 模块化设计，易于扩展和维护")
        print("• 完整的风险管理体系")
        print("• 多数据源支持，数据可靠性高")
        print("• 机器学习集成，策略智能化")
        print("• 美观的用户界面，操作便捷")
        print("• 完整的报告系统，分析全面")
        
    else:
        print(f"\n⚠️  {total_count - passed_count} 项测试失败，请检查相关模块")
    
    print(f"\n测试完成时间: {datetime.now()}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级策略功能
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from strategies.strategy_factory import strategy_factory
from utils.logger import get_logger


def create_sample_data():
    """创建示例数据"""
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    
    # 生成模拟股价数据
    np.random.seed(42)
    base_price = 50
    returns = np.random.normal(0.001, 0.02, 200)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成OHLCV数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        open_price = close if i == 0 else prices[i-1] * (1 + np.random.normal(0, 0.005))
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000000, 10000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    return df


def test_strategy_factory():
    """测试策略工厂"""
    logger = get_logger("TestAdvancedStrategies")
    
    print("=" * 60)
    print("高级策略功能测试")
    print("=" * 60)
    
    try:
        # 获取可用策略
        available_strategies = strategy_factory.get_available_strategies()
        
        print(f"\n可用策略数量: {len(available_strategies)}")
        print("策略列表:")
        for strategy_type, info in available_strategies.items():
            print(f"  - {strategy_type}: {info.get('description', 'N/A')}")
        
        print("\n✅ 策略工厂测试通过")
        return True
        
    except Exception as e:
        logger.error(f"策略工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_individual_strategies():
    """测试各个策略"""
    logger = get_logger("TestIndividualStrategies")
    
    print("\n" + "=" * 60)
    print("各策略功能测试")
    print("=" * 60)
    
    try:
        # 创建示例数据
        data = create_sample_data()
        print(f"创建示例数据: {len(data)} 条记录")
        
        # 测试的策略类型
        strategies_to_test = [
            ("KDJ", "KDJ随机指标策略", {}),
            ("MeanReversion", "均值回归策略", {}),
            ("Breakout", "突破策略", {}),
            ("TrendFollowing", "趋势跟踪策略", {}),
            ("AdvancedBollinger", "高级布林带策略", {})
        ]
        
        results = []
        
        for strategy_type, description, config in strategies_to_test:
            print(f"\n测试策略: {description}")
            
            try:
                # 创建策略实例
                strategy = strategy_factory.create_strategy(strategy_type, f"{strategy_type}测试", config)
                
                if strategy is None:
                    print(f"  ❌ 策略创建失败")
                    continue
                
                # 生成交易信号
                signals = strategy.generate_signals(data)
                
                if signals is not None and len(signals) > 0:
                    # 统计信号
                    buy_signals = (signals['signal'] == 1).sum() if 'signal' in signals.columns else 0
                    sell_signals = (signals['signal'] == -1).sum() if 'signal' in signals.columns else 0
                    
                    print(f"  ✅ 信号生成成功")
                    print(f"     买入信号: {buy_signals}")
                    print(f"     卖出信号: {sell_signals}")
                    print(f"     数据行数: {len(signals)}")
                    
                    # 检查关键列
                    key_columns = ['signal', 'position']
                    missing_columns = [col for col in key_columns if col not in signals.columns]
                    if missing_columns:
                        print(f"     ⚠️  缺少列: {missing_columns}")
                    else:
                        print(f"     ✅ 包含所有关键列")
                    
                    results.append({
                        'strategy': strategy_type,
                        'description': description,
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'success': True
                    })
                else:
                    print(f"  ❌ 信号生成失败或无数据")
                    results.append({
                        'strategy': strategy_type,
                        'description': description,
                        'success': False
                    })
                
            except Exception as e:
                print(f"  ❌ 策略测试失败: {e}")
                results.append({
                    'strategy': strategy_type,
                    'description': description,
                    'success': False,
                    'error': str(e)
                })
        
        # 汇总结果
        print(f"\n策略测试汇总:")
        print("-" * 50)
        
        successful_strategies = [r for r in results if r['success']]
        failed_strategies = [r for r in results if not r['success']]
        
        print(f"成功策略: {len(successful_strategies)}/{len(results)}")
        
        for result in successful_strategies:
            print(f"  ✅ {result['strategy']}: 买入{result['buy_signals']}次, 卖出{result['sell_signals']}次")
        
        if failed_strategies:
            print(f"\n失败策略: {len(failed_strategies)}")
            for result in failed_strategies:
                error_msg = result.get('error', '未知错误')
                print(f"  ❌ {result['strategy']}: {error_msg}")
        
        print("\n✅ 各策略功能测试完成")
        return len(successful_strategies) == len(results)
        
    except Exception as e:
        logger.error(f"各策略功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_combination():
    """测试策略组合"""
    logger = get_logger("TestStrategyCombination")
    
    print("\n" + "=" * 60)
    print("策略组合测试")
    print("=" * 60)
    
    try:
        # 创建示例数据
        data = create_sample_data()
        
        # 定义策略组合配置
        combination_config = [
            {
                'type': 'KDJ',
                'name': 'KDJ策略',
                'parameters': {},
                'weight': 0.3
            },
            {
                'type': 'MeanReversion',
                'name': '均值回归策略',
                'parameters': {},
                'weight': 0.4
            },
            {
                'type': 'TrendFollowing',
                'name': '趋势跟踪策略',
                'parameters': {},
                'weight': 0.3
            }
        ]
        
        # 创建策略组合
        combination = strategy_factory.create_strategy_combination(
            combination_config, "高级策略组合"
        )
        
        if combination is None:
            print("❌ 策略组合创建失败")
            return False
        
        print(f"✅ 策略组合创建成功: {combination.name}")
        print(f"   包含策略数量: {len(combination.strategies)}")
        
        # 获取组合信息
        info = combination.get_strategy_info()
        print(f"   策略类型: {info.get('strategy_type', 'N/A')}")
        print(f"   子策略:")
        for sub_strategy in info.get('sub_strategies', []):
            print(f"     - {sub_strategy['name']} (权重: {sub_strategy['weight']})")
        
        print("\n✅ 策略组合测试通过")
        return True
        
    except Exception as e:
        logger.error(f"策略组合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始高级策略功能测试...")
    
    success = True
    
    # 测试策略工厂
    if not test_strategy_factory():
        success = False
    
    # 测试各个策略
    if not test_individual_strategies():
        success = False
    
    # 测试策略组合
    if not test_strategy_combination():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 高级策略功能测试全部通过！")
        print("\n新增策略特点:")
        print("✅ KDJ随机指标策略 - 超买超卖区域交易")
        print("✅ 均值回归策略 - Z-Score均值回归")
        print("✅ 突破策略 - 支撑阻力位突破")
        print("✅ 趋势跟踪策略 - MACD+ADX趋势确认")
        print("✅ 高级布林带策略 - 布林带+RSI组合")
        print("✅ 策略组合功能 - 多策略权重组合")
        
        print("\n下一步可以:")
        print("1. 集成到策略中心GUI")
        print("2. 完善回测引擎")
        print("3. 添加风险管理模块")
        print("4. 实现策略优化功能")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 60)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

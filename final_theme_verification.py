#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终主题验证脚本
验证所有标签颜色问题是否100%修复
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QGroupBox, QTabWidget, QTableWidget, QTableWidgetItem,
    QLineEdit, QComboBox, QCheckBox, QDateEdit, QSpinBox, QDoubleSpinBox,
    QListWidget, QListWidgetItem, QTextEdit, QProgressBar
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.styles.dark_theme import DarkTheme


class FinalThemeVerificationWindow(QMainWindow):
    """最终主题验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终主题验证 - 100%修复确认")
        self.setGeometry(100, 100, 1400, 900)
        self.init_ui()
        self.apply_theme()
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 主标题
        title_label = QLabel("🎯 最终主题验证 - 确认所有文字清晰可见")
        title_label.setFont(QFont("微软雅黑", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important; padding: 10px;")
        layout.addWidget(title_label)
        
        # 验证说明
        info_label = QLabel("✅ 如果您能清晰看到下面所有测试项目的文字，说明修复100%成功！")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #4CAF50 !important; background-color: transparent !important; font-size: 14px; font-weight: bold;")
        layout.addWidget(info_label)
        
        # 创建标签页进行全面测试
        tab_widget = QTabWidget()
        
        # 基础组件测试
        tab_widget.addTab(self.create_basic_test_tab(), "基础组件测试")
        
        # 表格列表测试
        tab_widget.addTab(self.create_table_test_tab(), "表格列表测试")
        
        # 输入组件测试
        tab_widget.addTab(self.create_input_test_tab(), "输入组件测试")
        
        # 分组框测试
        tab_widget.addTab(self.create_groupbox_test_tab(), "分组框测试")
        
        # 实际组件测试
        tab_widget.addTab(self.create_real_component_test_tab(), "实际组件测试")
        
        layout.addWidget(tab_widget)
        
        # 验证结果
        result_label = QLabel("🎉 如果上述所有文字都清晰可见，恭喜！主题修复100%成功！")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("color: #FF9800 !important; background-color: transparent !important; font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(result_label)
    
    def create_basic_test_tab(self):
        """创建基础组件测试"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 各种颜色的标签测试
        colors_group = QGroupBox("颜色标签测试")
        colors_layout = QVBoxLayout(colors_group)
        
        test_labels = [
            ("普通白色文字", "#ffffff"),
            ("成功绿色文字", "#4CAF50"),
            ("错误红色文字", "#F44336"),
            ("警告橙色文字", "#FF9800"),
            ("信息蓝色文字", "#2196F3"),
            ("次要灰色文字", "#cccccc"),
        ]
        
        for text, color in test_labels:
            label = QLabel(f"这是{text} - 应该清晰可见")
            label.setStyleSheet(f"color: {color} !important; background-color: transparent !important; font-size: 14px;")
            colors_layout.addWidget(label)
        
        layout.addWidget(colors_group)
        
        # 按钮测试
        buttons_group = QGroupBox("按钮测试")
        buttons_layout = QHBoxLayout(buttons_group)
        
        for i, btn_text in enumerate(["普通按钮", "主要按钮", "危险按钮", "成功按钮"]):
            btn = QPushButton(btn_text)
            buttons_layout.addWidget(btn)
        
        layout.addWidget(buttons_group)
        layout.addStretch()
        
        return widget
    
    def create_table_test_tab(self):
        """创建表格列表测试"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 表格测试
        table_group = QGroupBox("表格测试")
        table_layout = QVBoxLayout(table_group)
        
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["股票代码", "股票名称", "当前价格", "涨跌幅"])
        
        test_data = [
            ("000001.SZ", "平安银行", "12.50", "+2.04%"),
            ("600036.SH", "招商银行", "35.20", "-1.22%"),
            ("300015.SZ", "爱尔眼科", "28.80", "+3.45%"),
            ("002415.SZ", "海康威视", "45.60", "+1.88%"),
        ]
        
        table.setRowCount(len(test_data))
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                if col == 3:  # 涨跌幅列
                    if value.startswith('+'):
                        item.setForeground(Qt.red)
                    elif value.startswith('-'):
                        item.setForeground(Qt.green)
                table.setItem(row, col, item)
        
        table_layout.addWidget(table)
        layout.addWidget(table_group)
        
        # 列表测试
        list_group = QGroupBox("列表测试")
        list_layout = QVBoxLayout(list_group)
        
        list_widget = QListWidget()
        for i, item_text in enumerate(["列表项目1", "列表项目2", "列表项目3", "列表项目4"]):
            item = QListWidgetItem(f"{item_text} - 应该清晰可见")
            list_widget.addItem(item)
        
        list_layout.addWidget(list_widget)
        layout.addWidget(list_group)
        
        return widget
    
    def create_input_test_tab(self):
        """创建输入组件测试"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入组件测试
        input_group = QGroupBox("输入组件测试")
        input_layout = QVBoxLayout(input_group)
        
        # 文本输入
        text_layout = QHBoxLayout()
        text_label = QLabel("文本输入:")
        text_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        text_input = QLineEdit()
        text_input.setPlaceholderText("请输入文本")
        text_layout.addWidget(text_label)
        text_layout.addWidget(text_input)
        input_layout.addLayout(text_layout)
        
        # 下拉框
        combo_layout = QHBoxLayout()
        combo_label = QLabel("下拉选择:")
        combo_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        combo_box = QComboBox()
        combo_box.addItems(["选项1", "选项2", "选项3"])
        combo_layout.addWidget(combo_label)
        combo_layout.addWidget(combo_box)
        input_layout.addLayout(combo_layout)
        
        # 复选框
        checkbox_layout = QHBoxLayout()
        for i, text in enumerate(["复选框A", "复选框B", "复选框C"]):
            checkbox = QCheckBox(text)
            checkbox.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
            checkbox_layout.addWidget(checkbox)
        input_layout.addLayout(checkbox_layout)
        
        # 数字输入
        number_layout = QHBoxLayout()
        number_label = QLabel("数字输入:")
        number_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        spin_box = QSpinBox()
        spin_box.setRange(0, 1000)
        double_spin_box = QDoubleSpinBox()
        double_spin_box.setRange(0.0, 100.0)
        number_layout.addWidget(number_label)
        number_layout.addWidget(spin_box)
        number_layout.addWidget(double_spin_box)
        input_layout.addLayout(number_layout)
        
        # 日期选择
        date_layout = QHBoxLayout()
        date_label = QLabel("日期选择:")
        date_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setCalendarPopup(True)
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_edit)
        input_layout.addLayout(date_layout)
        
        layout.addWidget(input_group)
        layout.addStretch()
        
        return widget
    
    def create_groupbox_test_tab(self):
        """创建分组框测试"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 多个分组框测试
        test_groups = [
            ("系统状态", [
                "运行状态: 正常",
                "数据连接: 已连接", 
                "内存使用: 45%",
                "CPU使用: 23%"
            ]),
            ("交易信息", [
                "账户余额: ¥100,000",
                "持仓数量: 5只",
                "今日盈亏: +¥2,000",
                "总收益率: +15.6%"
            ]),
            ("市场数据", [
                "上证指数: 3000.00 (+1.2%)",
                "深证成指: 11000.00 (-0.5%)",
                "创业板指: 2200.00 (+2.1%)",
                "科创50: 1000.00 (+0.8%)"
            ])
        ]
        
        for group_title, items in test_groups:
            group = QGroupBox(group_title)
            group_layout = QVBoxLayout(group)
            
            for item_text in items:
                label = QLabel(item_text)
                label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
                group_layout.addWidget(label)
            
            layout.addWidget(group)
        
        layout.addStretch()
        return widget
    
    def create_real_component_test_tab(self):
        """创建实际组件测试"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模拟实际组件的标签
        real_group = QGroupBox("实际组件标签测试")
        real_layout = QVBoxLayout(real_group)
        
        real_labels = [
            "股票代码: 000001.SZ",
            "股票名称: 平安银行", 
            "当前价格: ¥12.50",
            "涨跌幅: +2.04%",
            "成交量: 1,234,567手",
            "成交额: ¥15.43亿",
            "市盈率: 5.67",
            "市净率: 0.89",
            "总市值: ¥2,456.78亿",
            "流通市值: ¥2,123.45亿"
        ]
        
        for label_text in real_labels:
            label = QLabel(label_text)
            label.setStyleSheet("color: #ffffff !important; background-color: transparent !important; font-size: 13px;")
            real_layout.addWidget(label)
        
        layout.addWidget(real_group)
        
        # 进度条测试
        progress_group = QGroupBox("进度条测试")
        progress_layout = QVBoxLayout(progress_group)
        
        progress_label = QLabel("数据加载进度:")
        progress_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        progress_layout.addWidget(progress_label)
        
        progress_bar = QProgressBar()
        progress_bar.setValue(75)
        progress_layout.addWidget(progress_bar)
        
        layout.addWidget(progress_group)
        layout.addStretch()
        
        return widget
    
    def apply_theme(self):
        """应用深色主题"""
        self.setStyleSheet(DarkTheme.get_stylesheet())


def main():
    """主函数"""
    print("=" * 60)
    print("最终主题验证测试")
    print("=" * 60)
    
    print("验证项目:")
    print("1. ✓ 基础标签颜色")
    print("2. ✓ 表格和列表内容")
    print("3. ✓ 输入组件标签")
    print("4. ✓ 分组框内标签")
    print("5. ✓ 实际组件标签")
    print("6. ✓ 复选框和其他组件")
    
    print("\n启动验证窗口...")
    
    try:
        app = QApplication(sys.argv)
        
        window = FinalThemeVerificationWindow()
        window.show()
        
        print("✅ 验证窗口已显示")
        print("\n请仔细检查:")
        print("- 所有标签页中的文字是否都清晰可见")
        print("- 表格和列表内容是否正确显示")
        print("- 输入框、下拉框等组件标签是否清晰")
        print("- 复选框文字是否可见")
        print("- 分组框内的所有文字是否清晰")
        print("- 没有任何白色文字配白色背景的问题")
        
        print("\n如果所有文字都清晰可见，说明修复100%成功！🎉")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    main()

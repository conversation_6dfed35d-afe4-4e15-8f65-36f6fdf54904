#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证批量下载窗口背景颜色修复效果
检查"开始批量下载"窗口的背景颜色是否已正确设置为蓝色
"""

import sys
from pathlib import Path

def check_batch_download_fix():
    """检查批量下载窗口修复效果"""
    file_path = Path(__file__).parent / "gui" / "widgets" / "enhanced_download_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("=" * 60)
        print("验证批量下载窗口背景颜色修复效果")
        print("=" * 60)
        
        # 检查项目列表
        checks = [
            ("background-color: #0078d4", "任务表格蓝色背景"),
            ("QGroupBox { color: #ffffff !important; }", "组框标题白色文字"),
            ("color: #ffffff !important; background-color: transparent", "状态标签白色文字"),
            ("setBackground(QColor(\"#4CAF50\"))", "成功状态绿色背景"),
            ("setBackground(QColor(\"#F44336\"))", "失败状态红色背景"),
            ("setForeground(QColor(\"#ffffff\"))", "状态文字白色前景"),
        ]
        
        success_count = 0
        total_count = len(checks)
        
        for pattern, description in checks:
            if pattern in content:
                print(f"✅ {description}: 已正确设置")
                success_count += 1
            else:
                print(f"❌ {description}: 未找到设置")
        
        print("\n" + "=" * 60)
        print(f"验证结果: {success_count}/{total_count} 项修复验证通过")
        print("=" * 60)
        
        if success_count == total_count:
            print("🎉 批量下载窗口背景颜色修复验证通过！")
            print("现在所有白色文字都应该在蓝色背景上清晰可见。")
            return True
        else:
            print("⚠️  部分修复验证失败，可能需要进一步检查。")
            return False
            
    except Exception as e:
        print(f"❌ 检查批量下载窗口修复失败: {e}")
        return False

def create_fix_summary():
    """创建修复总结"""
    print("\n" + "=" * 60)
    print("批量下载窗口修复总结")
    print("=" * 60)
    print("已修复的组件:")
    print("1. 下载任务表格 - 表格背景改为蓝色 (#0078d4)")
    print("2. 下载任务组框 - 标题文字改为白色")
    print("3. 下载进度组框 - 标题文字改为白色")
    print("4. 批量状态标签 - 文字改为白色")
    print("5. 任务状态项 - 成功状态绿色背景，失败状态红色背景")
    print("6. 状态文字 - 所有状态文字改为白色前景")
    print("\n颜色方案:")
    print("- 表格背景: #0078d4 (蓝色)")
    print("- 表头背景: #005a9e (深蓝色)")
    print("- 选中背景: #106ebe (中蓝色)")
    print("- 成功状态: #4CAF50 (绿色背景)")
    print("- 失败状态: #F44336 (红色背景)")
    print("- 所有文字: #ffffff (白色)")

def test_batch_download_window():
    """测试批量下载窗口"""
    print("\n" + "=" * 60)
    print("测试批量下载窗口")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication
        from gui.widgets.enhanced_download_widget import EnhancedDownloadWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建批量下载窗口
        download_widget = EnhancedDownloadWidget()
        download_widget.show()
        
        print("✅ 批量下载窗口创建成功")
        print("请检查以下内容:")
        print("1. 切换到'批量下载'标签页")
        print("2. 检查'下载任务'表格是否为蓝色背景")
        print("3. 检查'下载进度'区域文字是否为白色")
        print("4. 检查所有标签和文字是否清晰可见")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试批量下载窗口失败: {e}")
        return False

def main():
    """主函数"""
    # 验证修复效果
    success = check_batch_download_fix()
    
    # 创建修复总结
    create_fix_summary()
    
    # 测试窗口
    if success:
        print("\n🚀 建议现在重新启动程序查看修复效果:")
        print("python main.py")
        print("\n然后导航到: 数据中心 → 数据下载 → 批量下载")
        print("检查'开始批量下载'按钮和相关表格的显示效果")
    else:
        print("\n⚠️  建议检查修复脚本是否正确执行。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设置界面代码修复（非GUI测试）
"""

import sys
from pathlib import Path
import ast
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_settings_code_fix():
    """测试设置界面代码修复"""
    print("=" * 60)
    print("测试设置界面代码修复")
    print("=" * 60)
    
    try:
        # 读取设置界面代码
        settings_file = project_root / "gui" / "widgets" / "settings_widget.py"
        
        if not settings_file.exists():
            print("❌ 设置界面文件不存在")
            return False
        
        with open(settings_file, 'r', encoding='utf-8') as f:
            code_content = f.read()
        
        print("✓ 设置界面文件读取成功")
        
        # 检查语法错误
        try:
            ast.parse(code_content)
            print("✓ Python语法检查通过")
        except SyntaxError as e:
            print(f"❌ Python语法错误: {e}")
            return False
        
        # 检查变量名问题
        print("\n检查变量名修复...")
        
        # 检查是否还有中文变量名
        chinese_var_pattern = r'[\u4e00-\u9fff]+_label'
        chinese_vars = re.findall(chinese_var_pattern, code_content)
        
        if chinese_vars:
            print(f"❌ 发现未修复的中文变量名: {chinese_vars}")
            return False
        else:
            print("✓ 中文变量名问题已修复")
        
        # 检查布局问题
        print("\n检查布局修复...")
        
        # 检查是否有错误的layout引用
        wrong_layout_pattern = r'layout\.addWidget\([^,]+, \d+, \d+\)'
        wrong_layouts = re.findall(wrong_layout_pattern, code_content)
        
        # 过滤掉正确的布局引用
        actual_wrong_layouts = []
        for layout_ref in wrong_layouts:
            if 'broker_layout' not in layout_ref and 'trading_layout' not in layout_ref and \
               'source_layout' not in layout_ref and 'update_layout' not in layout_ref and \
               'theme_layout' not in layout_ref and 'display_layout' not in layout_ref and \
               'risk_layout' not in layout_ref and 'alert_layout' not in layout_ref:
                actual_wrong_layouts.append(layout_ref)
        
        if actual_wrong_layouts:
            print(f"❌ 发现错误的布局引用: {actual_wrong_layouts}")
            return False
        else:
            print("✓ 布局引用问题已修复")
        
        # 检查样式设置
        print("\n检查样式设置...")
        
        # 检查是否有统一的样式设置
        style_pattern = r'setStyleSheet\("color: #ffffff !important; background-color: transparent !important;"\)'
        style_matches = re.findall(style_pattern, code_content)
        
        if len(style_matches) > 0:
            print(f"✓ 发现 {len(style_matches)} 个统一样式设置")
        else:
            print("⚠ 未发现统一样式设置")
        
        # 检查导入语句
        print("\n检查导入语句...")
        
        required_imports = [
            'from PyQt5.QtWidgets import',
            'from PyQt5.QtCore import',
            'from PyQt5.QtGui import',
            'import json'
        ]
        
        missing_imports = []
        for import_stmt in required_imports:
            if import_stmt not in code_content:
                missing_imports.append(import_stmt)
        
        if missing_imports:
            print(f"❌ 缺少必要的导入: {missing_imports}")
            return False
        else:
            print("✓ 导入语句检查通过")
        
        # 检查方法完整性
        print("\n检查方法完整性...")
        
        required_methods = [
            'def __init__',
            'def init_ui',
            'def create_general_tab',
            'def create_trading_tab',
            'def create_data_tab',
            'def create_ui_tab',
            'def create_risk_tab',
            'def save_settings',
            'def load_settings',
            'def reset_settings'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in code_content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少必要的方法: {missing_methods}")
            return False
        else:
            print("✓ 方法完整性检查通过")
        
        # 尝试导入模块
        print("\n测试模块导入...")
        
        try:
            from gui.widgets.settings_widget import SettingsWidget
            print("✓ 设置界面模块导入成功")
            
            # 检查类的基本属性
            if hasattr(SettingsWidget, '__init__'):
                print("✓ SettingsWidget类结构正常")
            else:
                print("❌ SettingsWidget类结构异常")
                return False
                
        except Exception as e:
            print(f"❌ 模块导入失败: {e}")
            return False
        
        print(f"\n{'='*60}")
        print("设置界面代码修复测试完成")
        print(f"{'='*60}")
        
        print("\n修复总结:")
        print("✅ 修复了中文变量名问题")
        print("✅ 修复了布局引用问题")
        print("✅ 统一了UI组件样式")
        print("✅ 确保了代码语法正确")
        print("✅ 验证了模块导入正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_settings_code_fix()
    if success:
        print("\n✅ 第三阶段设置界面代码修复测试通过")
    else:
        print("\n❌ 第三阶段设置界面代码修复测试失败")
    
    input("\n按回车键退出...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化机器学习模块测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ml.feature_engineering import FeatureEngineer
from ml.simple_ml import SimpleMLManager
from strategies.ml.simple_ml_strategy import SimpleMLStrategy
from strategies.strategy_factory import strategy_factory
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def test_simple_ml():
    """测试简化机器学习模块"""
    logger = setup_logger()
    logger.info("开始测试简化机器学习模块")

    try:
        # 获取测试数据
        collector = AKShareCollector()
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=300)).strftime('%Y-%m-%d')

        print("正在获取测试数据...")
        data = collector.get_stock_data('000001.SZ', start_date, end_date)

        if data is None or data.empty:
            print("无法获取测试数据，使用模拟数据")
            data = generate_mock_data()

        data.attrs['symbol'] = '000001.SZ'
        print(f"数据获取成功，共{len(data)}条记录")

        # 测试特征工程
        print(f"\n{'='*50}")
        print("测试特征工程模块")

        feature_engineer = FeatureEngineer()

        print("正在生成特征...")
        features_df = feature_engineer.create_features(data, [5, 10, 20])
        feature_names = feature_engineer.get_feature_names()

        print(f"✓ 特征生成完成")
        print(f"  原始数据维度: {data.shape}")
        print(f"  特征数据维度: {features_df.shape}")
        print(f"  生成特征数量: {len(feature_names)}")
        print(f"  前10个特征: {feature_names[:10]}")

        # 创建目标变量
        print("\n正在创建目标变量...")
        target = feature_engineer.create_target(data, 'direction', 5, 0.02)

        print(f"✓ 目标变量创建完成")
        print(f"  目标分布: {target.value_counts().to_dict()}")

        # 特征选择
        print("\n正在进行特征选择...")
        selected_features = feature_engineer.select_features(
            features_df, target, method='correlation', top_k=15
        )
        print(f"✓ 特征选择完成，选择了{len(selected_features)}个特征")
        print(f"  选择的特征: {selected_features[:10]}...")

        # 测试简化模型管理器
        print(f"\n{'='*50}")
        print("测试简化模型管理器")

        model_manager = SimpleMLManager()

        # 创建简化分类模型
        print("\n创建简化分类模型...")
        success = model_manager.create_model(
            'test_simple_classifier',
            'simple_classifier',
            {'method': 'linear'}
        )

        if success:
            print("✓ 简化模型创建成功")

            # 准备训练数据
            X = features_df[selected_features].dropna()
            y = target.loc[X.index].dropna()
            X = X.loc[y.index]

            if len(X) > 50:
                print(f"\n开始训练简化模型，训练样本: {len(X)}")
                metrics = model_manager.train_model('test_simple_classifier', X, y)

                if metrics:
                    print("✓ 简化模型训练完成")
                    print(f"  训练准确率: {metrics.get('train_accuracy', 0):.3f}")
                    print(f"  测试准确率: {metrics.get('test_accuracy', 0):.3f}")

                    # 测试预测
                    print("\n测试简化模型预测...")
                    test_X = X.tail(5)
                    predictions = model_manager.predict('test_simple_classifier', test_X)
                    probabilities = model_manager.predict_proba('test_simple_classifier', test_X)

                    if predictions is not None:
                        print("✓ 预测成功")
                        print(f"  预测结果: {predictions}")
                        if probabilities is not None:
                            print(f"  预测概率: {probabilities}")

                    # 保存和加载模型
                    print("\n测试简化模型保存和加载...")
                    if model_manager.save_model('test_simple_classifier'):
                        print("✓ 简化模型保存成功")

                        # 创建新的模型管理器测试加载
                        new_manager = SimpleMLManager()
                        if new_manager.load_model('test_simple_classifier'):
                            print("✓ 简化模型加载成功")
                        else:
                            print("❌ 简化模型加载失败")

                    # 显示特征重要性
                    if 'feature_importance' in metrics:
                        print("\n特征重要性 (Top 10):")
                        for item in metrics['feature_importance'][:10]:
                            print(f"  {item['feature']}: {item['importance']:.4f}")

                else:
                    print("❌ 简化模型训练失败")
            else:
                print("❌ 训练数据不足")
        else:
            print("❌ 简化模型创建失败")

        # 测试简化ML策略
        print(f"\n{'='*50}")
        print("测试简化ML策略")

        simple_ml_strategy = SimpleMLStrategy(
            "测试简化ML策略",
            {
                'prediction_threshold': 0.6,
                'confidence_threshold': 0.6,
                'min_training_samples': 50,
                'top_features': 15
            }
        )

        print("✓ 简化ML策略创建成功")

        # 生成信号
        print("\n正在生成简化ML信号...")
        signals = simple_ml_strategy.generate_signals(data)

        print(f"✓ 信号生成完成，共生成{len(signals)}个信号")

        if signals:
            print("\n简化ML信号详情:")
            for i, signal in enumerate(signals[-3:]):
                print(f"  {i+1}. {signal['direction'].upper()} @ {signal['price']:.2f}")
                print(f"     置信度: {signal['confidence']:.3f}")
                print(f"     原因: {signal['reason']}")
                if 'ml_probability' in signal and signal['ml_probability']:
                    print(f"     ML概率: {signal['ml_probability']:.3f}")

        # 获取模型性能
        performance = simple_ml_strategy.get_model_performance()
        print(f"\n简化ML策略性能:")
        print(f"  模型类型: {performance.get('model_type', 'N/A')}")
        print(f"  方法: {performance.get('method', 'N/A')}")
        print(f"  是否已训练: {performance.get('is_fitted', False)}")
        print(f"  特征数量: {performance.get('feature_count', 0)}")
        print(f"  选择特征数: {performance.get('selected_features_count', 0)}")
        print(f"  信号数量: {performance.get('signal_count', 0)}")

        # 测试策略工厂集成
        print(f"\n{'='*50}")
        print("测试策略工厂集成")

        # 获取可用策略
        available_strategies = strategy_factory.get_available_strategies()
        print(f"可用策略数量: {len(available_strategies)}")

        if 'SimpleML' in available_strategies:
            print("✓ 简化ML策略已注册到策略工厂")

            # 通过工厂创建策略
            factory_strategy = strategy_factory.create_strategy('SimpleML', '工厂创建的简化ML策略')
            if factory_strategy:
                print("✓ 通过工厂创建简化ML策略成功")

                # 生成信号
                factory_signals = factory_strategy.generate_signals(data)
                print(f"✓ 工厂策略生成{len(factory_signals)}个信号")
            else:
                print("❌ 通过工厂创建简化ML策略失败")
        else:
            print("❌ 简化ML策略未注册到策略工厂")

        print(f"\n{'='*50}")
        print("简化机器学习模块测试完成")

    except Exception as e:
        logger.error(f"简化机器学习模块测试失败: {e}")
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def generate_mock_data():
    """生成模拟数据"""
    dates = pd.date_range(start='2023-06-01', end='2024-05-01', freq='D')

    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 10.0
    prices = []

    for i in range(len(dates)):
        # 添加趋势和随机波动
        trend = 0.0001 * i  # 轻微上升趋势
        noise = np.random.normal(0, 0.02)  # 2%的随机波动

        # 添加一些周期性模式
        cycle = 0.001 * np.sin(2 * np.pi * i / 30)  # 30天周期

        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise + cycle)

        prices.append(max(0.1, price))  # 确保价格为正

    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = low + (high - low) * np.random.random()
        volume = int(np.random.normal(1000000, 200000))

        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': max(100000, volume)
        })

    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)

    return df


if __name__ == "__main__":
    test_simple_ml()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar,
    QAction, QMessageBox, QSplitter, QLabel, QFileDialog
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap

from config.settings import Settings
from utils.logger import get_logger
from gui.styles.dark_theme import DarkTheme
from gui.widgets.data_center_widget import DataCenterWidget
from gui.widgets.strategy_center_widget import StrategyCenterWidget
from gui.widgets.trading_center_widget import TradingCenterWidget
from gui.widgets.analysis_center_widget import AnalysisCenterWidget
from gui.widgets.dashboard_widget import DashboardWidget
from gui.widgets.settings_widget import SettingsWidget


class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    status_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.logger = get_logger("MainWindow")
        self.init_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        self.apply_theme()

        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次

        self.logger.info("主窗口初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(Settings.WINDOW_TITLE)
        self.setGeometry(100, 100, *Settings.WINDOW_SIZE)
        self.setMinimumSize(*Settings.WINDOW_MIN_SIZE)

        # 确保窗口在屏幕中央显示
        self.center_window()

        # 设置窗口状态
        self.setWindowState(Qt.WindowNoState)  # 确保不是最小化状态

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # 创建左侧面板（导航和工具）
        self.create_left_panel(main_splitter)

        # 创建右侧主要内容区域
        self.create_main_content(main_splitter)

        # 设置分割器比例
        main_splitter.setSizes([250, 1150])

    def center_window(self):
        """将窗口居中显示"""
        from PyQt5.QtWidgets import QDesktopWidget

        # 获取屏幕几何信息
        screen = QDesktopWidget().screenGeometry()

        # 获取窗口几何信息
        window = self.geometry()

        # 计算居中位置
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2

        # 移动窗口到居中位置
        self.move(x, y)

    def create_left_panel(self, parent):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 系统状态面板
        status_label = QLabel("系统状态")
        status_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        left_layout.addWidget(status_label)

        self.system_status = QLabel("系统正常运行")
        self.system_status.setStyleSheet("color: #4CAF50; padding: 5px; background-color: transparent;")
        left_layout.addWidget(self.system_status)

        # 快捷功能面板
        quick_label = QLabel("快捷功能")
        quick_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        left_layout.addWidget(quick_label)

        # 添加一些占位内容
        placeholder = QLabel("功能面板开发中...")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("color: #888888; padding: 20px; background-color: transparent;")
        left_layout.addWidget(placeholder)

        left_layout.addStretch()
        parent.addWidget(left_widget)

    def create_main_content(self, parent):
        """创建主要内容区域"""
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(True)

        # 添加主要标签页
        self.add_main_tabs()

        parent.addWidget(self.tab_widget)

    def add_main_tabs(self):
        """添加主要标签页"""
        # 仪表盘
        self.dashboard_widget = DashboardWidget()
        self.tab_widget.addTab(self.dashboard_widget, "仪表盘")

        # 数据中心
        self.data_center_widget = DataCenterWidget()
        self.tab_widget.addTab(self.data_center_widget, "数据中心")

        # 策略中心
        self.strategy_center_widget = StrategyCenterWidget()
        self.tab_widget.addTab(self.strategy_center_widget, "策略中心")

        # 交易中心
        self.trading_center_widget = TradingCenterWidget()
        self.tab_widget.addTab(self.trading_center_widget, "交易中心")

        # 分析中心
        self.analysis_center_widget = AnalysisCenterWidget()
        self.tab_widget.addTab(self.analysis_center_widget, "分析中心")

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入数据
        import_action = QAction("导入数据", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        # 导出数据
        export_action = QAction("导出数据", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)

        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # 开始/停止按钮
        self.start_action = QAction("开始", self)
        self.start_action.triggered.connect(self.toggle_system)
        toolbar.addAction(self.start_action)

    def setup_statusbar(self):
        """设置状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.statusbar.addWidget(self.status_label)

        # 时间标签
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.statusbar.addPermanentWidget(self.time_label)

    def apply_theme(self):
        """应用主题"""
        if Settings.THEME == "dark":
            self.setStyleSheet(DarkTheme.get_stylesheet())

    def update_status(self):
        """更新状态"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    # 菜单事件处理
    def import_data(self):
        """导入数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "导入数据文件",
                "",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json);;所有文件 (*.*)"
            )

            if file_path:
                self.status_label.setText("正在导入数据...")
                # TODO: 实现具体的数据导入逻辑
                # 这里可以调用数据中心的导入功能
                self.data_center_widget.import_data_file(file_path)
                self.status_label.setText("数据导入完成")
                QMessageBox.information(self, "成功", f"数据文件导入成功:\n{file_path}")
                self.logger.info(f"导入数据文件: {file_path}")

        except Exception as e:
            self.logger.error(f"导入数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入数据失败:\n{str(e)}")
            self.status_label.setText("导入失败")

    def export_data(self):
        """导出数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出数据文件",
                "",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json)"
            )

            if file_path:
                self.status_label.setText("正在导出数据...")
                # TODO: 实现具体的数据导出逻辑
                # 这里可以调用数据中心的导出功能
                self.data_center_widget.export_data_file(file_path)
                self.status_label.setText("数据导出完成")
                QMessageBox.information(self, "成功", f"数据文件导出成功:\n{file_path}")
                self.logger.info(f"导出数据文件: {file_path}")

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出数据失败:\n{str(e)}")
            self.status_label.setText("导出失败")

    def show_settings(self):
        """显示设置"""
        try:
            settings_dialog = SettingsWidget(self)
            settings_dialog.setWindowTitle("系统设置")
            settings_dialog.setModal(True)
            settings_dialog.resize(600, 500)
            settings_dialog.show()
            self.logger.info("打开设置界面")
        except Exception as e:
            self.logger.error(f"打开设置界面失败: {e}")
            QMessageBox.critical(self, "错误", f"打开设置界面失败:\n{str(e)}")

    def show_about(self):
        """显示关于"""
        QMessageBox.about(
            self,
            "关于",
            "量化交易系统 v1.0.0\n\n"
            "一个功能完整的量化交易平台\n"
            "支持多种数据源和交易策略\n\n"
            "开发者: AI助手"
        )

    def refresh_data(self):
        """刷新数据"""
        self.status_label.setText("正在刷新数据...")
        QMessageBox.information(self, "提示", "数据刷新功能开发中...")
        self.status_label.setText("就绪")

    def toggle_system(self):
        """切换系统状态"""
        if self.start_action.text() == "开始":
            self.start_action.setText("停止")
            self.status_label.setText("系统运行中...")
            self.system_status.setText("系统运行中")
            self.system_status.setStyleSheet("color: #4CAF50; padding: 5px; background-color: transparent;")
        else:
            self.start_action.setText("开始")
            self.status_label.setText("系统已停止")
            self.system_status.setText("系统已停止")
            self.system_status.setStyleSheet("color: #F44336; padding: 5px; background-color: transparent;")

    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出量化交易系统吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.logger.info("用户确认退出系统")
            event.accept()
        else:
            event.ignore()

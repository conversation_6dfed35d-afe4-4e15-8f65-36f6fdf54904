@echo off
chcp 65001 >nul
title 量化交易系统

echo.
echo ========================================
echo           量化交易系统 v1.0.0
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未检测到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python环境检测正常

:: 检查虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo ✓ 检测到虚拟环境，正在激活...
    call venv\Scripts\activate.bat
) else (
    echo ⚠ 未检测到虚拟环境
    echo 建议创建虚拟环境以避免依赖冲突
    echo.
    set /p choice="是否创建虚拟环境？(y/n): "
    if /i "%choice%"=="y" (
        echo 正在创建虚拟环境...
        python -m venv venv
        call venv\Scripts\activate.bat
        echo ✓ 虚拟环境创建成功
    )
)

:: 检查依赖包
echo.
echo 正在检查依赖包...
python -c "import PyQt5, pandas, numpy, akshare, sqlalchemy, loguru" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少必要的依赖包
    echo.
    set /p install="是否自动安装依赖包？(y/n): "
    if /i "%install%"=="y" (
        echo 正在安装依赖包，请稍候...
        pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
        if errorlevel 1 (
            echo ❌ 依赖包安装失败
            echo 请手动运行：pip install -r requirements.txt
            pause
            exit /b 1
        )
        echo ✓ 依赖包安装成功
    ) else (
        echo 请手动安装依赖包：pip install -r requirements.txt
        pause
        exit /b 1
    )
) else (
    echo ✓ 依赖包检查通过
)

:: 检查数据库
echo.
echo 正在初始化数据库...
python -c "from data.database.manager import DatabaseManager; DatabaseManager()" >nul 2>&1
if errorlevel 1 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
) else (
    echo ✓ 数据库初始化成功
)

:: 启动程序
echo.
echo 🚀 正在启动量化交易系统...
echo.
python main.py

:: 程序结束处理
echo.
if errorlevel 1 (
    echo ❌ 程序运行出现错误
    echo 请查看日志文件：logs/error.log
) else (
    echo ✓ 程序正常退出
)

echo.
echo 感谢使用量化交易系统！
pause

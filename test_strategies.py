#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略测试脚本
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from strategies.strategy_factory import strategy_factory
from data.collectors.akshare_collector import AKShareCollector
from utils.logger import setup_logger


def test_strategies():
    """测试所有策略"""
    logger = setup_logger()
    logger.info("开始测试策略模块")
    
    try:
        # 获取测试数据
        collector = AKShareCollector()
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        print("正在获取测试数据...")
        data = collector.get_stock_data('000001.SZ', start_date, end_date)
        
        if data is None or data.empty:
            print("无法获取测试数据，使用模拟数据")
            data = generate_mock_data()
        
        data.attrs['symbol'] = '000001.SZ'
        print(f"数据获取成功，共{len(data)}条记录")
        
        # 获取可用策略
        available_strategies = strategy_factory.get_available_strategies()
        print(f"\n可用策略数量: {len(available_strategies)}")
        
        # 测试每个策略
        for strategy_type, strategy_info in available_strategies.items():
            print(f"\n{'='*50}")
            print(f"测试策略: {strategy_type}")
            print(f"策略名称: {strategy_info['name']}")
            print(f"策略类型: {strategy_info.get('strategy_type', 'N/A')}")
            print(f"使用指标: {', '.join(strategy_info.get('indicators', []))}")
            
            try:
                # 创建策略实例
                strategy = strategy_factory.create_strategy(strategy_type)
                if strategy is None:
                    print(f"❌ 策略创建失败: {strategy_type}")
                    continue
                
                print(f"✓ 策略创建成功")
                
                # 生成信号
                signals = strategy.generate_signals(data)
                print(f"✓ 信号生成完成，共生成{len(signals)}个信号")
                
                # 显示信号详情
                if signals:
                    print("\n最近5个信号:")
                    for i, signal in enumerate(signals[-5:]):
                        print(f"  {i+1}. {signal['direction'].upper()} @ {signal['price']:.2f} "
                              f"(置信度: {signal['confidence']:.2f}, 原因: {signal['reason']})")
                
                # 获取策略绩效
                performance = strategy.get_performance_metrics()
                print(f"\n策略绩效:")
                print(f"  总交易次数: {performance['total_trades']}")
                print(f"  盈利交易: {performance['winning_trades']}")
                print(f"  胜率: {performance['win_rate']:.1f}%")
                print(f"  总盈亏: {performance['total_pnl']:.2f}")
                
            except Exception as e:
                print(f"❌ 策略测试失败: {e}")
                logger.error(f"策略测试失败 {strategy_type}: {e}")
        
        # 测试策略组合
        print(f"\n{'='*50}")
        print("测试策略组合")
        
        try:
            combination_config = [
                {'type': 'MA', 'name': 'MA策略', 'weight': 0.3},
                {'type': 'MACD', 'name': 'MACD策略', 'weight': 0.4},
                {'type': 'RSI', 'name': 'RSI策略', 'weight': 0.3}
            ]
            
            combination = strategy_factory.create_strategy_combination(
                combination_config, "测试组合策略"
            )
            
            if combination:
                print("✓ 策略组合创建成功")
                
                signals = combination.generate_signals(data)
                print(f"✓ 组合信号生成完成，共生成{len(signals)}个信号")
                
                if signals:
                    print("\n组合信号:")
                    for signal in signals[-3:]:
                        print(f"  {signal['direction'].upper()} @ {signal['price']:.2f} "
                              f"(置信度: {signal['confidence']:.2f}, 权重: {signal['weight']:.2f})")
                        print(f"    来源策略: {', '.join(signal['source_strategies'])}")
            else:
                print("❌ 策略组合创建失败")
                
        except Exception as e:
            print(f"❌ 策略组合测试失败: {e}")
            logger.error(f"策略组合测试失败: {e}")
        
        print(f"\n{'='*50}")
        print("策略测试完成")
        
    except Exception as e:
        logger.error(f"策略测试失败: {e}")
        print(f"❌ 策略测试失败: {e}")


def generate_mock_data():
    """生成模拟数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # 生成模拟价格数据
    np.random.seed(42)
    base_price = 10.0
    prices = []
    
    for i in range(len(dates)):
        # 添加趋势和随机波动
        trend = 0.0001 * i  # 轻微上升趋势
        noise = np.random.normal(0, 0.02)  # 2%的随机波动
        
        if i == 0:
            price = base_price
        else:
            price = prices[-1] * (1 + trend + noise)
        
        prices.append(max(0.1, price))  # 确保价格为正
    
    # 生成OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = low + (high - low) * np.random.random()
        volume = int(np.random.normal(1000000, 200000))
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': max(100000, volume)
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    
    return df


if __name__ == "__main__":
    test_strategies()

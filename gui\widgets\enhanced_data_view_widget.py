#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据查看组件
"""

import sys
import random
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QComboBox,
    QLineEdit, QDateEdit, QProgressBar, QTextEdit, QTabWidget,
    QTableWidget, QTableWidgetItem, QCheckBox, QSpinBox,
    QFileDialog, QMessageBox, QSplitter, QHeaderView,
    QAbstractItemView, QMenu, QAction
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate, QThread
from PyQt5.QtGui import QFont, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger

# 暂时禁用matplotlib以避免导入问题
MATPLOTLIB_AVAILABLE = False

# # 尝试导入matplotlib用于图表
# try:
#     import matplotlib.pyplot as plt
#     from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
#     from matplotlib.figure import Figure
#     import matplotlib.dates as mdates
#     MATPLOTLIB_AVAILABLE = True
# except ImportError:
#     MATPLOTLIB_AVAILABLE = False


class DataVisualizationWidget(QWidget):
    """数据可视化组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataVisualizationWidget")
        self.current_data = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 图表控制
        control_layout = QHBoxLayout()

        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["K线图", "收盘价线图", "成交量柱状图", "价格分布图"])
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        control_layout.addWidget(QLabel("图表类型:"))
        control_layout.addWidget(self.chart_type_combo)

        refresh_btn = QPushButton("刷新图表")
        refresh_btn.clicked.connect(self.update_chart)
        control_layout.addWidget(refresh_btn)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 图表区域
        if MATPLOTLIB_AVAILABLE:
            self.figure = Figure(figsize=(10, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            layout.addWidget(self.canvas)
        else:
            placeholder = QLabel("数据可视化\n(需要安装matplotlib)")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: #666666; font-size: 14px; padding: 50px;")
            layout.addWidget(placeholder)

    def set_data(self, data):
        """设置数据"""
        self.current_data = data
        self.update_chart()

    def update_chart(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE or self.current_data is None:
            return

        try:
            self.figure.clear()
            chart_type = self.chart_type_combo.currentText()

            if chart_type == "K线图":
                self.draw_candlestick_chart()
            elif chart_type == "收盘价线图":
                self.draw_line_chart()
            elif chart_type == "成交量柱状图":
                self.draw_volume_chart()
            elif chart_type == "价格分布图":
                self.draw_distribution_chart()

            self.canvas.draw()

        except Exception as e:
            self.logger.error(f"更新图表失败: {e}")

    def draw_candlestick_chart(self):
        """绘制K线图"""
        ax = self.figure.add_subplot(111)

        # 模拟K线数据
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        data = []
        base_price = 10.0

        for date in dates:
            open_price = base_price + random.uniform(-0.5, 0.5)
            close_price = open_price + random.uniform(-1.0, 1.0)
            high_price = max(open_price, close_price) + random.uniform(0, 0.5)
            low_price = min(open_price, close_price) - random.uniform(0, 0.5)

            data.append({
                'date': date,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
            base_price = close_price

        df = pd.DataFrame(data)

        # 绘制K线
        for i, row in df.iterrows():
            color = 'red' if row['close'] >= row['open'] else 'green'
            ax.plot([i, i], [row['low'], row['high']], color='black', linewidth=1)
            ax.plot([i, i], [row['open'], row['close']], color=color, linewidth=3)

        ax.set_title('K线图', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期')
        ax.set_ylabel('价格')
        ax.grid(True, alpha=0.3)

    def draw_line_chart(self):
        """绘制收盘价线图"""
        ax = self.figure.add_subplot(111)

        # 模拟价格数据
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        prices = [10.0 + i * 0.1 + random.uniform(-0.5, 0.5) for i in range(30)]

        ax.plot(range(len(dates)), prices, color='blue', linewidth=2, marker='o', markersize=4)
        ax.set_title('收盘价走势', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期')
        ax.set_ylabel('收盘价')
        ax.grid(True, alpha=0.3)

    def draw_volume_chart(self):
        """绘制成交量柱状图"""
        ax = self.figure.add_subplot(111)

        # 模拟成交量数据
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        volumes = [random.randint(1000000, 10000000) for _ in range(30)]

        bars = ax.bar(range(len(dates)), volumes, color='orange', alpha=0.7)
        ax.set_title('成交量分布', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期')
        ax.set_ylabel('成交量')
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for i, bar in enumerate(bars):
            if i % 5 == 0:  # 每5个显示一个标签
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height/1000000:.1f}M', ha='center', va='bottom', fontsize=8)

    def draw_distribution_chart(self):
        """绘制价格分布图"""
        ax = self.figure.add_subplot(111)

        # 模拟价格分布数据
        prices = [10.0 + random.normalvariate(0, 2) for _ in range(1000)]

        ax.hist(prices, bins=30, color='purple', alpha=0.7, edgecolor='black')
        ax.set_title('价格分布直方图', fontsize=14, fontweight='bold')
        ax.set_xlabel('价格')
        ax.set_ylabel('频次')
        ax.grid(True, alpha=0.3)


class AdvancedDataTable(QTableWidget):
    """高级数据表格"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("AdvancedDataTable")
        self.init_ui()
        self.setup_context_menu()

    def init_ui(self):
        """初始化界面"""
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSortingEnabled(True)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)

    def setup_context_menu(self):
        """设置右键菜单"""
        self.context_menu = QMenu(self)

        # 导出选中行
        export_selected_action = QAction("导出选中行", self)
        export_selected_action.triggered.connect(self.export_selected_rows)
        self.context_menu.addAction(export_selected_action)

        # 复制选中数据
        copy_action = QAction("复制数据", self)
        copy_action.triggered.connect(self.copy_selected_data)
        self.context_menu.addAction(copy_action)

        self.context_menu.addSeparator()

        # 排序选项
        sort_asc_action = QAction("升序排列", self)
        sort_asc_action.triggered.connect(lambda: self.sort_column_asc())
        self.context_menu.addAction(sort_asc_action)

        sort_desc_action = QAction("降序排列", self)
        sort_desc_action.triggered.connect(lambda: self.sort_column_desc())
        self.context_menu.addAction(sort_desc_action)

    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.itemAt(position) is not None:
            self.context_menu.exec_(self.mapToGlobal(position))

    def export_selected_rows(self):
        """导出选中行"""
        try:
            selected_rows = set()
            for item in self.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要导出的行")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据", "",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;所有文件 (*.*)"
            )

            if file_path:
                # 收集选中行的数据
                headers = []
                for col in range(self.columnCount()):
                    headers.append(self.horizontalHeaderItem(col).text())

                data = []
                for row in sorted(selected_rows):
                    row_data = []
                    for col in range(self.columnCount()):
                        item = self.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                # 创建DataFrame并导出
                df = pd.DataFrame(data, columns=headers)

                if file_path.endswith('.csv'):
                    df.to_csv(file_path, index=False, encoding='utf-8')
                elif file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)

                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
                self.logger.info(f"导出选中行数据: {file_path}")

        except Exception as e:
            self.logger.error(f"导出选中行失败: {e}")
            QMessageBox.critical(self, "错误", f"导出失败:\n{str(e)}")

    def copy_selected_data(self):
        """复制选中数据到剪贴板"""
        try:
            from PyQt5.QtWidgets import QApplication

            selected_items = self.selectedItems()
            if not selected_items:
                return

            # 按行列排序
            selected_items.sort(key=lambda item: (item.row(), item.column()))

            # 构建文本
            text_data = []
            current_row = -1
            row_data = []

            for item in selected_items:
                if item.row() != current_row:
                    if row_data:
                        text_data.append('\t'.join(row_data))
                    row_data = []
                    current_row = item.row()
                row_data.append(item.text())

            if row_data:
                text_data.append('\t'.join(row_data))

            clipboard_text = '\n'.join(text_data)
            QApplication.clipboard().setText(clipboard_text)

            self.logger.info("数据已复制到剪贴板")

        except Exception as e:
            self.logger.error(f"复制数据失败: {e}")

    def sort_column_asc(self):
        """升序排列当前列"""
        current_column = self.currentColumn()
        if current_column >= 0:
            self.sortItems(current_column, Qt.AscendingOrder)

    def sort_column_desc(self):
        """降序排列当前列"""
        current_column = self.currentColumn()
        if current_column >= 0:
            self.sortItems(current_column, Qt.DescendingOrder)


class EnhancedDataViewWidget(QWidget):
    """增强数据查看组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("EnhancedDataViewWidget")
        self.current_data = []
        self.init_ui()
        self.logger.info("增强数据查看组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 查询条件区域
        self.create_query_section(layout)

        # 主要内容区域（分割器）
        main_splitter = QSplitter(Qt.Horizontal)

        # 左侧：数据表格
        self.create_table_section(main_splitter)

        # 右侧：数据可视化
        self.create_visualization_section(main_splitter)

        # 设置分割器比例
        main_splitter.setSizes([600, 400])
        layout.addWidget(main_splitter)

        # 状态栏
        self.create_status_section(layout)

    def create_query_section(self, parent_layout):
        """创建查询条件区域"""
        query_group = QGroupBox("查询条件")
        query_layout = QGridLayout(query_group)

        # 股票代码
        股票代码_label = QLabel("股票代码:")
        股票代码_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        query_layout.addWidget(股票代码_label, 0, 0)
        self.symbol_input = QLineEdit()
        self.symbol_input.setPlaceholderText("例如: 000001")
        query_layout.addWidget(self.symbol_input, 0, 1)

        # 日期范围
        开始日期_label = QLabel("开始日期:")
        开始日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        query_layout.addWidget(开始日期_label, 0, 2)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        query_layout.addWidget(self.start_date, 0, 3)

        结束日期_label = QLabel("结束日期:")
        结束日期_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        query_layout.addWidget(结束日期_label, 0, 4)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        query_layout.addWidget(self.end_date, 0, 5)

        # 数据类型筛选
        数据类型_label = QLabel("数据类型:")
        数据类型_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        query_layout.addWidget(数据类型_label, 1, 0)
        self.data_type_filter = QComboBox()
        self.data_type_filter.addItems(["全部", "日线数据", "分钟数据", "基本信息"])
        query_layout.addWidget(self.data_type_filter, 1, 1)

        # 价格范围筛选
        价格范围_label = QLabel("价格范围:")
        价格范围_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        query_layout.addWidget(价格范围_label, 1, 2)
        price_layout = QHBoxLayout()
        self.min_price = QLineEdit()
        self.min_price.setPlaceholderText("最低价")
        self.max_price = QLineEdit()
        self.max_price.setPlaceholderText("最高价")
        price_layout.addWidget(self.min_price)
        price_layout.addWidget(QLabel("至"))
        price_layout.addWidget(self.max_price)
        query_layout.addLayout(price_layout, 1, 3, 1, 2)

        # 操作按钮
        button_layout = QHBoxLayout()

        query_btn = QPushButton("查询数据")
        query_btn.clicked.connect(self.query_data)
        button_layout.addWidget(query_btn)

        export_btn = QPushButton("导出全部")
        export_btn.clicked.connect(self.export_all_data)
        button_layout.addWidget(export_btn)

        clear_btn = QPushButton("清空结果")
        clear_btn.clicked.connect(self.clear_results)
        button_layout.addWidget(clear_btn)

        button_layout.addStretch()
        query_layout.addLayout(button_layout, 2, 0, 1, 6)

        parent_layout.addWidget(query_group)

    def create_table_section(self, parent_splitter):
        """创建数据表格区域"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # 表格标题
        table_title = QLabel("数据列表")
        table_title.setFont(QFont("微软雅黑", 12, QFont.Bold))
        table_layout.addWidget(table_title)

        # 高级数据表格
        self.data_table = AdvancedDataTable()
        table_layout.addWidget(self.data_table)

        parent_splitter.addWidget(table_widget)

    def create_visualization_section(self, parent_splitter):
        """创建数据可视化区域"""
        viz_widget = QWidget()
        viz_layout = QVBoxLayout(viz_widget)

        # 可视化标题
        viz_title = QLabel("数据可视化")
        viz_title.setFont(QFont("微软雅黑", 12, QFont.Bold))
        viz_layout.addWidget(viz_title)

        # 数据可视化组件
        self.visualization = DataVisualizationWidget()
        viz_layout.addWidget(self.visualization)

        parent_splitter.addWidget(viz_widget)

    def create_status_section(self, parent_layout):
        """创建状态区域"""
        status_layout = QHBoxLayout()

        self.status_label = QLabel("就绪")


        self.status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        self.record_count_label = QLabel("记录数: 0")


        self.record_count_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        status_layout.addWidget(self.record_count_label)

        parent_layout.addLayout(status_layout)

    def query_data(self):
        """查询数据"""
        try:
            # 获取查询参数
            symbol = self.symbol_input.text().strip()
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            if not symbol:
                QMessageBox.warning(self, "警告", "请输入股票代码")
                return

            self.status_label.setText("正在查询数据...")

            # 生成模拟数据
            self.current_data = self.generate_sample_data(symbol, start_date, end_date)

            # 更新表格
            self.update_table()

            # 更新可视化
            self.visualization.set_data(self.current_data)

            self.status_label.setText("查询完成")
            self.record_count_label.setText(f"记录数: {len(self.current_data)}")

            self.logger.info(f"查询数据完成: {symbol}, {len(self.current_data)} 条记录")

        except Exception as e:
            self.logger.error(f"查询数据失败: {e}")
            self.status_label.setText("查询失败")
            QMessageBox.critical(self, "错误", f"查询失败:\n{str(e)}")

    def generate_sample_data(self, symbol, start_date, end_date):
        """生成示例数据"""
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        data = []

        base_price = 10.0
        for date in date_range:
            # 跳过周末
            if date.weekday() >= 5:
                continue

            # 模拟价格波动
            change = random.uniform(-0.5, 0.5)
            base_price += change

            data.append({
                '日期': date.strftime('%Y-%m-%d'),
                '股票代码': symbol,
                '开盘价': round(base_price + random.uniform(-0.2, 0.2), 2),
                '最高价': round(base_price + random.uniform(0, 0.5), 2),
                '最低价': round(base_price - random.uniform(0, 0.5), 2),
                '收盘价': round(base_price, 2),
                '成交量': random.randint(1000000, 10000000),
                '成交额': round(base_price * random.randint(1000000, 10000000), 2),
                '涨跌幅': round(random.uniform(-10, 10), 2)
            })

        return data

    def update_table(self):
        """更新数据表格"""
        if not self.current_data:
            self.data_table.setRowCount(0)
            return

        # 设置表格行数和列数
        self.data_table.setRowCount(len(self.current_data))
        self.data_table.setColumnCount(len(self.current_data[0]))
        self.data_table.setHorizontalHeaderLabels(list(self.current_data[0].keys()))

        # 填充数据
        for row, record in enumerate(self.current_data):
            for col, (key, value) in enumerate(record.items()):
                item = QTableWidgetItem(str(value))

                # 根据数据类型设置颜色
                if key == '涨跌幅':
                    if float(value) > 0:
                        item.setBackground(QColor("#FFEBEE"))  # 红色背景
                    elif float(value) < 0:
                        item.setBackground(QColor("#E8F5E8"))  # 绿色背景

                self.data_table.setItem(row, col, item)

        # 调整列宽
        self.data_table.resizeColumnsToContents()

    def export_all_data(self):
        """导出全部数据"""
        try:
            if not self.current_data:
                QMessageBox.warning(self, "警告", "没有数据可导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据", "",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx);;JSON文件 (*.json);;所有文件 (*.*)"
            )

            if file_path:
                df = pd.DataFrame(self.current_data)

                if file_path.endswith('.csv'):
                    df.to_csv(file_path, index=False, encoding='utf-8')
                elif file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                elif file_path.endswith('.json'):
                    df.to_json(file_path, orient='records', date_format='iso')

                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
                self.logger.info(f"导出全部数据: {file_path}")

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出失败:\n{str(e)}")

    def clear_results(self):
        """清空查询结果"""
        self.current_data = []
        self.data_table.setRowCount(0)
        self.status_label.setText("就绪")
        self.record_count_label.setText("记录数: 0")
        self.logger.info("清空查询结果")

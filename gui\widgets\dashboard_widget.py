#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表盘组件
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea,
    QProgressBar, QGroupBox, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from config.settings import Settings


class DashboardWidget(QWidget):
    """仪表盘组件"""

    # 信号定义
    refresh_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DashboardWidget")
        self.init_ui()
        self.setup_timer()
        self.logger.info("仪表盘组件初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建标题
        title_label = QLabel("量化交易系统仪表盘")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建主内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # 添加各个面板
        content_layout.addWidget(self.create_system_status_panel())
        content_layout.addWidget(self.create_market_overview_panel())
        content_layout.addWidget(self.create_strategy_status_panel())
        content_layout.addWidget(self.create_recent_trades_panel())

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def create_system_status_panel(self):
        """创建系统状态面板"""
        group = QGroupBox("系统状态")
        group.setFont(QFont("微软雅黑", 10, QFont.Bold))
        layout = QGridLayout(group)

        # 系统运行时间
        self.uptime_label = QLabel("系统运行时间: 00:00:00")

        self.uptime_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.uptime_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        status_label = QLabel("运行状态:")
        status_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(status_label, 0, 0)
        layout.addWidget(self.uptime_label, 0, 1)

        # 数据连接状态
        self.data_status_label = QLabel("数据连接: 正常")

        self.data_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.data_status_label.setStyleSheet("color: #4CAF50; background-color: transparent;")

        data_label = QLabel("数据状态:")
        data_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(data_label, 1, 0)
        layout.addWidget(self.data_status_label, 1, 1)

        # 策略运行状态
        self.strategy_status_label = QLabel("策略状态: 0个运行中")

        self.strategy_status_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.strategy_status_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        strategy_label = QLabel("策略状态:")
        strategy_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(strategy_label, 2, 0)
        layout.addWidget(self.strategy_status_label, 2, 1)

        # 内存使用情况
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_progress.setValue(25)

        memory_label = QLabel("内存使用:")
        memory_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(memory_label, 3, 0)
        layout.addWidget(self.memory_progress, 3, 1)

        return group

    def create_market_overview_panel(self):
        """创建市场概览面板"""
        group = QGroupBox("市场概览")
        group.setFont(QFont("微软雅黑", 10, QFont.Bold))
        layout = QGridLayout(group)

        # 主要指数
        indices = [
            ("上证指数", "3000.00", "+1.2%", "#4CAF50"),
            ("深证成指", "11000.00", "-0.5%", "#F44336"),
            ("创业板指", "2200.00", "+2.1%", "#4CAF50"),
            ("科创50", "1000.00", "+0.8%", "#4CAF50")
        ]

        for i, (name, value, change, color) in enumerate(indices):
            name_label = QLabel(name)
            name_label.setStyleSheet("color: #ffffff; background-color: transparent;")

            value_label = QLabel(value)
            value_label.setStyleSheet("color: #ffffff; background-color: transparent;")

            change_label = QLabel(change)
            change_label.setStyleSheet(f"color: {color}; font-weight: bold; background-color: transparent;")

            layout.addWidget(name_label, i, 0)
            layout.addWidget(value_label, i, 1)
            layout.addWidget(change_label, i, 2)

        return group

    def create_strategy_status_panel(self):
        """创建策略状态面板"""
        group = QGroupBox("策略状态")
        group.setFont(QFont("微软雅黑", 10, QFont.Bold))
        layout = QVBoxLayout(group)

        # 策略统计
        stats_layout = QGridLayout()

        self.total_strategies_label = QLabel("总策略数: 0")


        self.total_strategies_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.total_strategies_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        self.active_strategies_label = QLabel("运行中: 0")


        self.active_strategies_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.active_strategies_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        self.paused_strategies_label = QLabel("已暂停: 0")


        self.paused_strategies_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.paused_strategies_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        self.profit_strategies_label = QLabel("盈利策略: 0")


        self.profit_strategies_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.profit_strategies_label.setStyleSheet("color: #ffffff; background-color: transparent;")

        stats_layout.addWidget(self.total_strategies_label, 0, 0)
        stats_layout.addWidget(self.active_strategies_label, 0, 1)
        stats_layout.addWidget(self.paused_strategies_label, 1, 0)
        stats_layout.addWidget(self.profit_strategies_label, 1, 1)

        layout.addLayout(stats_layout)

        # 快速操作按钮
        button_layout = QHBoxLayout()

        start_all_btn = QPushButton("启动所有策略")
        stop_all_btn = QPushButton("停止所有策略")
        refresh_btn = QPushButton("刷新状态")

        start_all_btn.clicked.connect(self.start_all_strategies)
        stop_all_btn.clicked.connect(self.stop_all_strategies)
        refresh_btn.clicked.connect(self.refresh_data)

        button_layout.addWidget(start_all_btn)
        button_layout.addWidget(stop_all_btn)
        button_layout.addWidget(refresh_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        return group

    def create_recent_trades_panel(self):
        """创建最近交易面板"""
        group = QGroupBox("最近交易")
        group.setFont(QFont("微软雅黑", 10, QFont.Bold))
        layout = QVBoxLayout(group)

        # 创建交易表格
        self.trades_table = QTableWidget()
        self.trades_table.setColumnCount(6)
        self.trades_table.setHorizontalHeaderLabels([
            "时间", "股票代码", "方向", "数量", "价格", "状态"
        ])

        # 设置表格属性
        self.trades_table.setAlternatingRowColors(True)
        self.trades_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.trades_table.horizontalHeader().setStretchLastSection(True)
        
        # 设置表格背景为蓝色，确保白色文字可见
        self.trades_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        # 添加示例数据
        self.add_sample_trades()

        layout.addWidget(self.trades_table)

        return group

    def add_sample_trades(self):
        """添加示例交易数据"""
        sample_trades = [
            ("09:30:15", "000001.SZ", "买入", "1000", "12.50", "已成交"),
            ("10:15:30", "600036.SH", "卖出", "500", "35.20", "已成交"),
            ("11:20:45", "300015.SZ", "买入", "800", "28.80", "部分成交"),
            ("14:30:20", "002415.SZ", "卖出", "1200", "15.60", "已成交"),
        ]

        self.trades_table.setRowCount(len(sample_trades))

        for row, trade in enumerate(sample_trades):
            for col, value in enumerate(trade):
                item = QTableWidgetItem(str(value))
                if col == 2:  # 方向列
                    if value == "买入":
                        item.setForeground(QColor("red"))
                    else:
                        item.setForeground(QColor("green"))
                self.trades_table.setItem(row, col, item)

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_dashboard)
        self.timer.start(1000)  # 每秒更新一次

        self.start_time = datetime.now()

    def update_dashboard(self):
        """更新仪表盘数据"""
        # 更新运行时间
        uptime = datetime.now() - self.start_time
        uptime_str = str(uptime).split('.')[0]  # 去掉微秒
        self.uptime_label.setText(f"系统运行时间: {uptime_str}")

        # 更新内存使用（模拟）
        import random
        memory_usage = random.randint(20, 80)
        self.memory_progress.setValue(memory_usage)

    def start_all_strategies(self):
        """启动所有策略"""
        self.logger.info("启动所有策略")
        # TODO: 实现启动所有策略的逻辑

    def stop_all_strategies(self):
        """停止所有策略"""
        self.logger.info("停止所有策略")
        # TODO: 实现停止所有策略的逻辑

    def refresh_data(self):
        """刷新数据"""
        self.logger.info("刷新仪表盘数据")
        self.refresh_requested.emit()

        # 更新市场数据
        self.update_market_data()

        # 更新策略状态
        self.update_strategy_status()

        # 更新交易记录
        self.update_recent_trades()

    def update_market_data(self):
        """更新市场数据"""
        try:
            # 这里可以接入真实的市场数据API
            import random

            # 模拟市场数据更新
            indices_data = [
                ("上证指数", f"{3000 + random.randint(-100, 100):.2f}",
                 f"{random.uniform(-3, 3):+.1f}%",
                 "green" if random.random() > 0.5 else "red"),
                ("深证成指", f"{11000 + random.randint(-500, 500):.2f}",
                 f"{random.uniform(-3, 3):+.1f}%",
                 "green" if random.random() > 0.5 else "red"),
                ("创业板指", f"{2200 + random.randint(-100, 100):.2f}",
                 f"{random.uniform(-3, 3):+.1f}%",
                 "green" if random.random() > 0.5 else "red"),
                ("科创50", f"{1000 + random.randint(-50, 50):.2f}",
                 f"{random.uniform(-3, 3):+.1f}%",
                 "green" if random.random() > 0.5 else "red")
            ]

            self.logger.info("市场数据已更新")

        except Exception as e:
            self.logger.error(f"更新市场数据失败: {e}")

    def update_strategy_status(self):
        """更新策略状态"""
        try:
            # 这里可以从策略管理器获取真实状态
            import random

            total = random.randint(5, 15)
            active = random.randint(0, total)
            paused = random.randint(0, total - active)
            profit = random.randint(0, active)

            self.total_strategies_label.setText(f"总策略数: {total}")
            self.active_strategies_label.setText(f"运行中: {active}")
            self.paused_strategies_label.setText(f"已暂停: {paused}")
            self.profit_strategies_label.setText(f"盈利策略: {profit}")

            self.strategy_status_label.setText(f"策略状态: {active}个运行中")

            self.logger.info("策略状态已更新")

        except Exception as e:
            self.logger.error(f"更新策略状态失败: {e}")

    def update_recent_trades(self):
        """更新最近交易"""
        try:
            # 这里可以从交易管理器获取真实交易记录
            import random
            from datetime import datetime, timedelta

            # 生成模拟交易数据
            symbols = ["000001.SZ", "600036.SH", "300015.SZ", "002415.SZ", "600519.SH"]
            directions = ["买入", "卖出"]
            statuses = ["已成交", "部分成交", "已撤单"]

            new_trades = []
            for i in range(5):
                time_str = (datetime.now() - timedelta(minutes=random.randint(1, 480))).strftime("%H:%M:%S")
                symbol = random.choice(symbols)
                direction = random.choice(directions)
                quantity = str(random.randint(100, 2000))
                price = f"{random.uniform(10, 50):.2f}"
                status = random.choice(statuses)

                new_trades.append((time_str, symbol, direction, quantity, price, status))

            # 更新表格
            self.trades_table.setRowCount(len(new_trades))

            for row, trade in enumerate(new_trades):
                for col, value in enumerate(trade):
                    item = QTableWidgetItem(str(value))
                    if col == 2:  # 方向列
                        if value == "买入":
                            item.setForeground(QColor("red"))
                        else:
                            item.setForeground(QColor("green"))
                    self.trades_table.setItem(row, col, item)

            self.logger.info("交易记录已更新")

        except Exception as e:
            self.logger.error(f"更新交易记录失败: {e}")

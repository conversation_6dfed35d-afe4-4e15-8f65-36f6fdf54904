#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置文件
"""

import os
from pathlib import Path
from typing import Dict, Any
import json


class Settings:
    """系统设置类"""
    
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent.parent
    
    # 数据目录
    DATA_DIR = PROJECT_ROOT / "data"
    DATABASE_DIR = DATA_DIR / "database"
    CACHE_DIR = DATA_DIR / "cache"
    
    # 日志配置
    LOG_DIR = PROJECT_ROOT / "logs"
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    # 数据库配置
    DATABASE_URL = f"sqlite:///{DATABASE_DIR}/trading_system.db"
    
    # 界面配置
    WINDOW_TITLE = "量化交易系统 v1.0.0"
    WINDOW_SIZE = (1400, 900)
    WINDOW_MIN_SIZE = (1000, 600)
    
    # 主题配置
    THEME = "dark"  # dark, light
    
    # 数据源配置
    DATA_SOURCES = {
        "tushare": {
            "enabled": True,
            "token": "",  # 需要用户配置
            "timeout": 30
        },
        "akshare": {
            "enabled": True,
            "timeout": 30
        },
        "eastmoney": {
            "enabled": True,
            "timeout": 30
        }
    }
    
    # 交易配置
    TRADING_CONFIG = {
        "initial_capital": 1000000,  # 初始资金100万
        "commission_rate": 0.0003,   # 手续费率0.03%
        "slippage": 0.001,          # 滑点0.1%
        "max_position": 0.1,        # 单只股票最大仓位10%
        "max_drawdown": 0.2,        # 最大回撤20%
    }
    
    # 策略配置
    STRATEGY_CONFIG = {
        "backtest_start": "2020-01-01",
        "backtest_end": "2023-12-31",
        "benchmark": "000300.SH",  # 沪深300作为基准
        "rebalance_freq": "monthly"  # 调仓频率
    }
    
    # 风险管理配置
    RISK_CONFIG = {
        "max_single_loss": 0.02,    # 单笔最大亏损2%
        "max_daily_loss": 0.05,     # 单日最大亏损5%
        "position_limit": 0.95,     # 最大仓位95%
        "stop_loss": 0.1,           # 止损线10%
    }
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.DATA_DIR,
            cls.DATABASE_DIR,
            cls.CACHE_DIR,
            cls.LOG_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def load_user_config(cls, config_file: str = "user_config.json") -> Dict[str, Any]:
        """加载用户配置"""
        config_path = cls.PROJECT_ROOT / config_file
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
                return {}
        
        return {}
    
    @classmethod
    def save_user_config(cls, config: Dict[str, Any], config_file: str = "user_config.json"):
        """保存用户配置"""
        config_path = cls.PROJECT_ROOT / config_file
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
    
    @classmethod
    def get_tushare_token(cls) -> str:
        """获取Tushare Token"""
        user_config = cls.load_user_config()
        return user_config.get("tushare_token", "")
    
    @classmethod
    def set_tushare_token(cls, token: str):
        """设置Tushare Token"""
        user_config = cls.load_user_config()
        user_config["tushare_token"] = token
        cls.save_user_config(user_config)


# 初始化目录
Settings.create_directories()

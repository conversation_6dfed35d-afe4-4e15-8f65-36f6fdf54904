#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础组件类
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from utils.logger import get_logger


class BaseWidget(QWidget):
    """基础组件类，提供通用功能"""
    
    # 信号定义
    status_changed = pyqtSignal(str)  # 状态变化信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(self.__class__.__name__)
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """设置UI界面"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(10)
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: "Microsoft YaHei", Arial, sans-serif;
                font-size: 12px;
            }
            
            QLabel {
                color: #ffffff;
                background-color: transparent;
            }
            
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 4px;
                padding: 8px 16px;
                color: #ffffff;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #505050;
                border-color: #707070;
            }
            
            QPushButton:pressed {
                background-color: #353535;
            }
            
            QPushButton:disabled {
                background-color: #2b2b2b;
                color: #666666;
                border-color: #404040;
            }
        """)
        
    def add_title(self, title: str, subtitle: str = None):
        """添加标题"""
        title_layout = QVBoxLayout()
        
        # 主标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #ffffff; margin: 10px 0px;")
        title_layout.addWidget(title_label)
        
        # 副标题
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setFont(QFont("Microsoft YaHei", 10))
            subtitle_label.setStyleSheet("color: #cccccc; margin-bottom: 10px;")
            title_layout.addWidget(subtitle_label)
            
        self.layout.addLayout(title_layout)
        
    def add_separator(self):
        """添加分隔线"""
        separator = QLabel()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #606060; margin: 10px 0px;")
        self.layout.addWidget(separator)
        
    def create_button_layout(self, buttons: list):
        """创建按钮布局"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        for button_text, callback in buttons:
            button = QPushButton(button_text)
            if callback:
                button.clicked.connect(callback)
            button_layout.addWidget(button)
            
        button_layout.addStretch()
        return button_layout
        
    def show_status(self, message: str):
        """显示状态信息"""
        self.logger.info(message)
        self.status_changed.emit(message)
        
    def show_error(self, error: str):
        """显示错误信息"""
        self.logger.error(error)
        self.error_occurred.emit(error)
        
    def set_loading(self, loading: bool):
        """设置加载状态"""
        self.setEnabled(not loading)
        if loading:
            self.show_status("正在加载...")
        else:
            self.show_status("加载完成")
            
    def clear_layout(self, layout):
        """清空布局"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                self.clear_layout(child.layout())
                
    def get_safe_text(self, widget, default=""):
        """安全获取组件文本"""
        try:
            if hasattr(widget, 'text'):
                return widget.text().strip()
            elif hasattr(widget, 'currentText'):
                return widget.currentText().strip()
            else:
                return default
        except:
            return default
            
    def set_safe_text(self, widget, text):
        """安全设置组件文本"""
        try:
            if hasattr(widget, 'setText'):
                widget.setText(str(text))
            elif hasattr(widget, 'setCurrentText'):
                widget.setCurrentText(str(text))
        except Exception as e:
            self.logger.warning(f"设置文本失败: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序启动脚本
检查程序是否能正常启动并显示窗口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        print("✓ PyQt5.QtWidgets 导入成功")
    except Exception as e:
        print(f"❌ PyQt5.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtCore import Qt
        print("✓ PyQt5.QtCore 导入成功")
    except Exception as e:
        print(f"❌ PyQt5.QtCore 导入失败: {e}")
        return False
    
    try:
        from PyQt5.QtGui import QIcon
        print("✓ PyQt5.QtGui 导入成功")
    except Exception as e:
        print(f"❌ PyQt5.QtGui 导入失败: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        print("✓ utils.logger 导入成功")
    except Exception as e:
        print(f"❌ utils.logger 导入失败: {e}")
        return False
    
    try:
        from config.settings import Settings
        print("✓ config.settings 导入成功")
    except Exception as e:
        print(f"❌ config.settings 导入失败: {e}")
        return False
    
    try:
        from gui.main_window import MainWindow
        print("✓ gui.main_window 导入成功")
    except Exception as e:
        print(f"❌ gui.main_window 导入失败: {e}")
        return False
    
    return True

def test_simple_window():
    """测试简单窗口"""
    print("\n测试简单窗口...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        app = QApplication(sys.argv)
        
        # 创建简单测试窗口
        window = QMainWindow()
        window.setWindowTitle("测试窗口 - 如果您看到这个窗口说明程序可以正常启动")
        window.setGeometry(200, 200, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加测试标签
        title_label = QLabel("🎉 程序启动成功！")
        title_label.setFont(QFont("微软雅黑", 16))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #000000; background-color: #ffffff; padding: 20px; border: 2px solid #0078d4;")
        layout.addWidget(title_label)
        
        info_label = QLabel("如果您能看到这个窗口，说明PyQt5和基本功能正常工作。\n请关闭此窗口，然后我们尝试启动主程序。")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #333333; font-size: 14px; padding: 10px;")
        layout.addWidget(info_label)
        
        # 显示窗口
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✓ 测试窗口已显示")
        print("如果您看到了测试窗口，请关闭它，然后我们继续测试主程序")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window():
    """测试主窗口"""
    print("\n测试主窗口...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 确保窗口显示
        main_window.show()
        main_window.raise_()
        main_window.activateWindow()
        
        # 强制刷新
        app.processEvents()
        
        print("✓ 主窗口创建成功")
        print("主窗口应该已经显示，请检查任务栏或桌面")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 主窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("程序启动测试")
    print("=" * 60)
    
    # 1. 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖包是否安装")
        input("按回车键退出...")
        return 1
    
    print("\n✓ 所有导入测试通过")
    
    # 2. 询问用户要测试什么
    print("\n请选择测试类型:")
    print("1. 测试简单窗口（验证PyQt5基本功能）")
    print("2. 测试主程序窗口（完整功能）")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            return test_simple_window()
        elif choice == "2":
            return test_main_window()
        else:
            print("无效选择，默认测试简单窗口")
            return test_simple_window()
            
    except KeyboardInterrupt:
        print("\n用户取消")
        return 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

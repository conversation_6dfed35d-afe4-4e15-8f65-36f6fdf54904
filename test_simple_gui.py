#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单GUI测试脚本
用于验证PyQt5是否正常工作
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleTestWindow(QMainWindow):
    """简单测试窗口"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("PyQt5 GUI 测试")
        self.setGeometry(200, 200, 400, 300)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建布局
        layout = QVBoxLayout(central_widget)

        # 添加标题
        title_label = QLabel("PyQt5 GUI 测试")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 添加说明文字
        info_label = QLabel("如果您能看到这个窗口，说明PyQt5工作正常！")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        # 添加测试按钮
        test_button = QPushButton("点击测试")
        test_button.clicked.connect(self.show_message)
        layout.addWidget(test_button)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

        # 确保窗口显示在前台
        self.show()
        self.raise_()
        self.activateWindow()

    def show_message(self):
        """显示测试消息"""
        QMessageBox.information(self, "测试", "PyQt5 GUI 功能正常！")

def main():
    """主函数"""
    print("启动简单GUI测试...")

    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        print("QApplication 创建成功")

        # 创建测试窗口
        window = SimpleTestWindow()
        print("测试窗口已创建")

        print("窗口几何信息:", window.geometry())
        print("窗口是否可见:", window.isVisible())
        print("请检查任务栏或屏幕上是否有测试窗口显示")

        # 运行应用程序
        result = app.exec_()
        print("应用程序退出，返回码:", result)
        sys.exit(result)

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动量化交易系统
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 量化交易系统启动器")
    print("=" * 60)
    
    # 设置工作目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    print(f"📁 工作目录: {project_root}")
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查关键依赖
    print("\n🔍 检查关键依赖...")
    try:
        import PyQt5
        print("  ✅ PyQt5")
    except ImportError as e:
        print(f"  ❌ PyQt5: {e}")
        return 1
    
    try:
        import pandas
        print("  ✅ pandas")
    except ImportError as e:
        print(f"  ❌ pandas: {e}")
        return 1
    
    try:
        import numpy
        print("  ✅ numpy")
    except ImportError as e:
        print(f"  ❌ numpy: {e}")
        return 1
    
    # 检查主程序文件
    main_file = project_root / "main.py"
    if not main_file.exists():
        print("❌ main.py 文件不存在")
        return 1
    
    print("✅ main.py 文件存在")
    
    # 启动主程序
    print("\n🚀 启动主程序...")
    print("=" * 60)
    
    try:
        # 导入并运行主程序
        sys.path.insert(0, str(project_root))
        
        # 强制刷新输出
        sys.stdout.flush()
        
        # 运行主程序
        from main import main as main_func
        exit_code = main_func()
        
        print(f"\n🏁 程序结束，退出码: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        return 0
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
    except Exception as e:
        print(f"启动器失败: {e}")
        exit_code = 1
    
    print(f"\n按回车键退出... (退出码: {exit_code})")
    input()
    sys.exit(exit_code)

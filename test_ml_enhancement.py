#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试机器学习模块完善
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ml.model_manager import ModelManager
from ml.feature_engineering import FeatureEngineer
from utils.logger import get_logger

def test_ml_enhancement():
    """测试机器学习模块完善"""
    logger = get_logger("TestMLEnhancement")
    
    print("=" * 60)
    print("测试机器学习模块完善")
    print("=" * 60)
    
    try:
        # 测试模型管理器
        model_manager = ModelManager()
        
        # 获取可用模型
        available_models = model_manager.get_available_models()
        print(f"\n✓ 可用模型类型: {len(available_models)} 种")
        
        for model_type, info in available_models.items():
            print(f"\n模型类型: {model_type}")
            print(f"  类名: {info['class_name']}")
            print(f"  类型: {info['type']}")
            print(f"  描述: {info['description']}")
            print(f"  默认参数: {list(info['default_params'].keys())}")
        
        # 生成测试数据
        print(f"\n{'='*40}")
        print("生成测试数据")
        print(f"{'='*40}")
        
        np.random.seed(42)
        n_samples = 1000
        
        # 创建模拟股票数据
        dates = pd.date_range('2020-01-01', periods=n_samples, freq='D')
        data = pd.DataFrame({
            'open': 100 + np.cumsum(np.random.randn(n_samples) * 0.5),
            'high': 100 + np.cumsum(np.random.randn(n_samples) * 0.5) + np.random.rand(n_samples) * 2,
            'low': 100 + np.cumsum(np.random.randn(n_samples) * 0.5) - np.random.rand(n_samples) * 2,
            'close': 100 + np.cumsum(np.random.randn(n_samples) * 0.5),
            'volume': np.random.randint(1000000, 10000000, n_samples)
        }, index=dates)
        
        # 确保high >= low
        data['high'] = np.maximum(data['high'], data[['open', 'close']].max(axis=1))
        data['low'] = np.minimum(data['low'], data[['open', 'close']].min(axis=1))
        
        print(f"✓ 生成测试数据: {len(data)} 条记录")
        
        # 特征工程
        print(f"\n{'='*40}")
        print("测试特征工程")
        print(f"{'='*40}")
        
        feature_engineer = FeatureEngineer()
        features_df = feature_engineer.create_features(data)
        
        print(f"✓ 特征工程完成，生成 {features_df.shape[1]} 个特征")
        print(f"  特征名称: {features_df.columns.tolist()[:10]}...")
        
        # 创建目标变量
        target = (data['close'].shift(-1) > data['close']).astype(int).dropna()
        
        # 对齐特征和目标
        common_index = features_df.index.intersection(target.index)
        features_df = features_df.loc[common_index]
        target = target.loc[common_index]
        
        print(f"✓ 目标变量创建完成，样本数: {len(target)}")
        print(f"  目标分布: {target.value_counts().to_dict()}")
        
        # 测试新增模型类型
        print(f"\n{'='*40}")
        print("测试新增模型类型")
        print(f"{'='*40}")
        
        test_models = [
            ('gradient_boosting_classifier', '梯度提升分类器'),
            ('mlp_classifier', '神经网络分类器'),
            ('random_forest_classifier', '随机森林分类器')
        ]
        
        for model_type, model_name in test_models:
            try:
                print(f"\n测试 {model_name}...")
                
                # 创建模型
                success = model_manager.create_model(f'test_{model_type}', model_type)
                if success:
                    print(f"  ✓ 模型创建成功")
                    
                    # 训练模型
                    print(f"  开始训练...")
                    metrics = model_manager.train_model(
                        f'test_{model_type}', 
                        features_df.iloc[:800], 
                        target.iloc[:800],
                        test_size=0.2
                    )
                    
                    if metrics:
                        print(f"  ✓ 训练完成")
                        print(f"    训练准确率: {metrics.get('train_accuracy', 'N/A'):.3f}")
                        print(f"    测试准确率: {metrics.get('test_accuracy', 'N/A'):.3f}")
                        print(f"    训练样本: {metrics.get('training_samples', 'N/A')}")
                        print(f"    测试样本: {metrics.get('test_samples', 'N/A')}")
                        
                        # 测试预测
                        predictions = model_manager.predict(
                            f'test_{model_type}', 
                            features_df.iloc[800:850]
                        )
                        
                        if predictions is not None:
                            print(f"    ✓ 预测成功，预测样本: {len(predictions)}")
                        
                        # 测试概率预测
                        probabilities = model_manager.predict_proba(
                            f'test_{model_type}', 
                            features_df.iloc[800:850]
                        )
                        
                        if probabilities is not None:
                            print(f"    ✓ 概率预测成功，形状: {probabilities.shape}")
                        
                        # 获取模型信息
                        model_info = model_manager.get_model_info(f'test_{model_type}')
                        print(f"    模型类型: {model_info.get('model_type', 'N/A')}")
                        print(f"    特征数量: {model_info.get('feature_count', 'N/A')}")
                        print(f"    是否已训练: {model_info.get('is_fitted', 'N/A')}")
                        
                    else:
                        print(f"  ❌ 训练失败")
                else:
                    print(f"  ❌ 模型创建失败")
                    
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
        
        print(f"\n{'='*60}")
        print("机器学习模块完善测试完成")
        print(f"{'='*60}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_ml_enhancement()
    if success:
        print("\n✅ 第二阶段机器学习模块完善测试通过")
    else:
        print("\n❌ 第二阶段机器学习模块完善测试失败")
    
    input("\n按回车键退出...")

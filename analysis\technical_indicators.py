#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算模块
"""

import pandas as pd
import numpy as np
from typing import Union, Tuple, Optional
import warnings

from utils.logger import get_logger

warnings.filterwarnings('ignore')


class TechnicalIndicators:
    """技术指标计算类"""

    def __init__(self):
        self.logger = get_logger("TechnicalIndicators")

    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """简单移动平均线 (Simple Moving Average)"""
        return data.rolling(window=period).mean()

    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """指数移动平均线 (Exponential Moving Average)"""
        return data.ewm(span=period).mean()

    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """相对强弱指数 (Relative Strength Index)"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指标 (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带 (Bollinger Bands)"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, sma, lower_band

    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series,
                   k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """随机指标 (Stochastic Oscillator)"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent

    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均真实波幅 (Average True Range)"""
        high_low = high - low
        high_close_prev = np.abs(high - close.shift(1))
        low_close_prev = np.abs(low - close.shift(1))
        true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        return atr

    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """威廉指标 (Williams %R)"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r

    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """商品通道指数 (Commodity Channel Index)"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.abs(x - x.mean()).mean()
        )
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        return cci

    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """能量潮 (On-Balance Volume)"""
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    @staticmethod
    def vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """成交量加权平均价 (Volume Weighted Average Price)"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap

    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """平均趋向指数 (Average Directional Index)"""
        # 计算真实波幅
        tr = TechnicalIndicators.atr(high, low, close, 1)

        # 计算方向性移动
        plus_dm = high.diff()
        minus_dm = -low.diff()

        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0

        # 平滑处理
        tr_smooth = tr.rolling(window=period).mean()
        plus_dm_smooth = plus_dm.rolling(window=period).mean()
        minus_dm_smooth = minus_dm.rolling(window=period).mean()

        # 计算方向性指标
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)

        # 计算ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()

        return adx

    @staticmethod
    def momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """动量指标 (Momentum)"""
        return data.diff(period)

    @staticmethod
    def roc(data: pd.Series, period: int = 10) -> pd.Series:
        """变化率 (Rate of Change)"""
        return ((data / data.shift(period)) - 1) * 100

    @staticmethod
    def trix(data: pd.Series, period: int = 14) -> pd.Series:
        """三重指数平滑移动平均 (TRIX)"""
        ema1 = TechnicalIndicators.ema(data, period)
        ema2 = TechnicalIndicators.ema(ema1, period)
        ema3 = TechnicalIndicators.ema(ema2, period)
        trix = ema3.pct_change() * 10000
        return trix

    @staticmethod
    def dmi(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """趋向指标 (Directional Movement Index)"""
        # 计算真实波幅
        tr = TechnicalIndicators.atr(high, low, close, 1)

        # 计算方向性移动
        plus_dm = high.diff()
        minus_dm = -low.diff()

        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm < 0] = 0

        # 平滑处理
        tr_smooth = tr.rolling(window=period).mean()
        plus_dm_smooth = plus_dm.rolling(window=period).mean()
        minus_dm_smooth = minus_dm.rolling(window=period).mean()

        # 计算方向性指标
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)

        # 计算ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()

        return plus_di, minus_di, adx

    @staticmethod
    def kdj(high: pd.Series, low: pd.Series, close: pd.Series,
            k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """KDJ随机指标"""
        # 计算RSV
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        rsv = 100 * ((close - lowest_low) / (highest_high - lowest_low))

        # 计算K值
        k = pd.Series(index=close.index, dtype=float)
        k.iloc[0] = 50  # 初始值
        for i in range(1, len(rsv)):
            if pd.notna(rsv.iloc[i]):
                k.iloc[i] = (2/3) * k.iloc[i-1] + (1/3) * rsv.iloc[i]
            else:
                k.iloc[i] = k.iloc[i-1]

        # 计算D值
        d = pd.Series(index=close.index, dtype=float)
        d.iloc[0] = 50  # 初始值
        for i in range(1, len(k)):
            if pd.notna(k.iloc[i]):
                d.iloc[i] = (2/3) * d.iloc[i-1] + (1/3) * k.iloc[i]
            else:
                d.iloc[i] = d.iloc[i-1]

        # 计算J值
        j = 3 * k - 2 * d

        return k, d, j

    @staticmethod
    def brar(open_price: pd.Series, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 26) -> Tuple[pd.Series, pd.Series]:
        """BRAR人气意愿指标"""
        # 计算BR
        hc = high - close.shift(1)
        oc = open_price - close.shift(1)
        lc = low - close.shift(1)

        hc[hc < 0] = 0
        lc[lc > 0] = 0
        lc = abs(lc)

        br = (hc.rolling(window=period).sum() / lc.rolling(window=period).sum()) * 100

        # 计算AR
        ho = high - open_price
        oo = open_price - low
        ar = (ho.rolling(window=period).sum() / oo.rolling(window=period).sum()) * 100

        return br, ar

    @staticmethod
    def psy(close: pd.Series, period: int = 12) -> pd.Series:
        """心理线指标 (Psychological Line)"""
        price_change = close.diff()
        up_days = (price_change > 0).astype(int)
        psy = up_days.rolling(window=period).sum() / period * 100
        return psy

    @staticmethod
    def bias(close: pd.Series, period: int = 6) -> pd.Series:
        """乖离率 (Bias)"""
        ma = close.rolling(window=period).mean()
        bias = ((close - ma) / ma) * 100
        return bias

    @staticmethod
    def wr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """威廉指标 (Williams %R) - 改进版"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        wr = ((highest_high - close) / (highest_high - lowest_low)) * 100
        return wr

    @staticmethod
    def mtm(close: pd.Series, period: int = 12) -> pd.Series:
        """动量指标 (Momentum)"""
        mtm = close - close.shift(period)
        return mtm

    @staticmethod
    def dma(close: pd.Series, short_period: int = 10, long_period: int = 50) -> Tuple[pd.Series, pd.Series]:
        """平行线差指标 (Difference of Moving Average)"""
        short_ma = close.rolling(window=short_period).mean()
        long_ma = close.rolling(window=long_period).mean()
        dma = short_ma - long_ma
        ama = dma.rolling(window=10).mean()  # DMA的移动平均
        return dma, ama

    @staticmethod
    def expma(close: pd.Series, period: int = 12) -> pd.Series:
        """指数平均数 (Exponential Moving Average)"""
        return close.ewm(span=period).mean()

    @staticmethod
    def sar(high: pd.Series, low: pd.Series, acceleration: float = 0.02, maximum: float = 0.2) -> pd.Series:
        """抛物线转向指标 (Parabolic SAR)"""
        sar = pd.Series(index=high.index, dtype=float)
        trend = pd.Series(index=high.index, dtype=int)
        af = pd.Series(index=high.index, dtype=float)
        ep = pd.Series(index=high.index, dtype=float)

        # 初始化
        sar.iloc[0] = low.iloc[0]
        trend.iloc[0] = 1  # 1为上升趋势，-1为下降趋势
        af.iloc[0] = acceleration
        ep.iloc[0] = high.iloc[0]

        for i in range(1, len(high)):
            if trend.iloc[i-1] == 1:  # 上升趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])

                if low.iloc[i] <= sar.iloc[i]:  # 转为下降趋势
                    trend.iloc[i] = -1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = acceleration
                    ep.iloc[i] = low.iloc[i]
                else:
                    trend.iloc[i] = 1
                    if high.iloc[i] > ep.iloc[i-1]:
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + acceleration, maximum)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
            else:  # 下降趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])

                if high.iloc[i] >= sar.iloc[i]:  # 转为上升趋势
                    trend.iloc[i] = 1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = acceleration
                    ep.iloc[i] = high.iloc[i]
                else:
                    trend.iloc[i] = -1
                    if low.iloc[i] < ep.iloc[i-1]:
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + acceleration, maximum)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]

        return sar

    @staticmethod
    def mike(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 12) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series, pd.Series, pd.Series]:
        """麦克指标 (MIKE)"""
        typ = (high + low + close) / 3
        ll = low.rolling(window=period).min()
        hh = high.rolling(window=period).max()

        wr = (typ - ll) / (hh - ll) * 100
        mr = (typ - ll) / (hh - ll) * 100
        sr = 2 * ll - hh

        ws = typ + (typ - ll)
        ms = typ + (typ - ll)
        ss = typ - (hh - typ)

        return wr, mr, sr, ws, ms, ss

    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            result = data.copy()

            # 移动平均线系列
            result['sma_5'] = self.sma(data['close'], 5)
            result['sma_10'] = self.sma(data['close'], 10)
            result['sma_20'] = self.sma(data['close'], 20)
            result['sma_30'] = self.sma(data['close'], 30)
            result['sma_60'] = self.sma(data['close'], 60)
            result['sma_120'] = self.sma(data['close'], 120)
            result['sma_250'] = self.sma(data['close'], 250)

            result['ema_5'] = self.ema(data['close'], 5)
            result['ema_10'] = self.ema(data['close'], 10)
            result['ema_12'] = self.ema(data['close'], 12)
            result['ema_20'] = self.ema(data['close'], 20)
            result['ema_30'] = self.ema(data['close'], 30)
            result['ema_60'] = self.ema(data['close'], 60)

            # 趋势指标
            result['rsi_6'] = self.rsi(data['close'], 6)
            result['rsi_12'] = self.rsi(data['close'], 12)
            result['rsi_24'] = self.rsi(data['close'], 24)

            macd, signal, histogram = self.macd(data['close'])
            result['macd'] = macd
            result['macd_signal'] = signal
            result['macd_histogram'] = histogram

            # 布林带
            bb_upper, bb_middle, bb_lower = self.bollinger_bands(data['close'])
            result['bb_upper'] = bb_upper
            result['bb_middle'] = bb_middle
            result['bb_lower'] = bb_lower

            # KDJ指标
            k, d, j = self.kdj(data['high'], data['low'], data['close'])
            result['kdj_k'] = k
            result['kdj_d'] = d
            result['kdj_j'] = j

            # 随机指标
            stoch_k, stoch_d = self.stochastic(data['high'], data['low'], data['close'])
            result['stoch_k'] = stoch_k
            result['stoch_d'] = stoch_d

            # 威廉指标
            result['williams_r'] = self.williams_r(data['high'], data['low'], data['close'])
            result['wr'] = self.wr(data['high'], data['low'], data['close'])

            # 其他常用指标
            result['atr'] = self.atr(data['high'], data['low'], data['close'])
            result['cci'] = self.cci(data['high'], data['low'], data['close'])
            result['obv'] = self.obv(data['close'], data['volume'])
            result['vwap'] = self.vwap(data['high'], data['low'], data['close'], data['volume'])
            result['momentum'] = self.momentum(data['close'])
            result['roc'] = self.roc(data['close'])
            result['trix'] = self.trix(data['close'])

            # 趋向指标
            plus_di, minus_di, adx = self.dmi(data['high'], data['low'], data['close'])
            result['plus_di'] = plus_di
            result['minus_di'] = minus_di
            result['adx'] = adx

            # 新增指标
            br, ar = self.brar(data['open'], data['high'], data['low'], data['close'])
            result['br'] = br
            result['ar'] = ar

            result['psy'] = self.psy(data['close'])
            result['bias_6'] = self.bias(data['close'], 6)
            result['bias_12'] = self.bias(data['close'], 12)
            result['bias_24'] = self.bias(data['close'], 24)

            result['mtm'] = self.mtm(data['close'])

            dma, ama = self.dma(data['close'])
            result['dma'] = dma
            result['ama'] = ama

            result['expma'] = self.expma(data['close'])
            result['sar'] = self.sar(data['high'], data['low'])

            self.logger.info(f"计算技术指标完成，共 {len(result.columns) - len(data.columns)} 个指标")
            return result

        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return data

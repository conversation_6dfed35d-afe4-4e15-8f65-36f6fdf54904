#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序启动
"""

import sys
from pathlib import Path
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_startup():
    """测试主程序启动"""
    print("🔍 测试主程序启动...")
    
    try:
        # 创建QApplication
        app = QApplication([])
        
        # 导入主窗口
        from gui.main_window import MainWindow
        print("   ✅ 主窗口类导入成功")
        
        # 创建主窗口
        main_window = MainWindow()
        print("   ✅ 主窗口创建成功")
        
        # 显示窗口（但不进入事件循环）
        main_window.show()
        print("   ✅ 主窗口显示成功")
        
        # 处理一些事件
        app.processEvents()
        print("   ✅ 事件处理成功")
        
        # 关闭窗口
        main_window.close()
        print("   ✅ 主窗口关闭成功")
        
        # 退出应用
        app.quit()
        print("   ✅ 应用退出成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 主程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("主程序启动测试")
    print("=" * 50)
    
    # 测试主程序启动
    test_result = test_main_startup()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"主程序启动测试: {'✅ 通过' if test_result else '❌ 失败'}")
    
    if test_result:
        print("\n🎉 主程序启动测试通过！GUI组件导入问题已修复！")
        return True
    else:
        print("\n❌ 主程序启动测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

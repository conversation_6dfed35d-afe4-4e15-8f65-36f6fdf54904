#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源管理器
"""

import sys
import time
import random
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger
from data.collectors.base_collector import BaseCollector
from data.collectors.akshare_collector import AKShareCollector


class DataSourceType(Enum):
    """数据源类型枚举"""
    AKSHARE = "AKShare"
    TUSHARE = "Tushare"
    WIND = "Wind"
    YAHOO = "Yahoo Finance"
    SINA = "新浪财经"
    EASTMONEY = "东方财富"


class DataSourceStatus(Enum):
    """数据源状态枚举"""
    ONLINE = "在线"
    OFFLINE = "离线"
    ERROR = "错误"
    MAINTENANCE = "维护中"


class MockDataCollector(BaseCollector):
    """模拟数据收集器（用于测试）"""
    
    def __init__(self, name: str, reliability: float = 0.9):
        super().__init__(name)
        self.reliability = reliability  # 可靠性（0-1）
        self.latency_range = (50, 300)  # 延迟范围（毫秒）
    
    def connect(self) -> bool:
        """连接数据源"""
        # 模拟连接过程
        time.sleep(random.uniform(0.1, 0.5))
        success = random.random() < self.reliability
        self.is_connected = success
        if success:
            self.logger.info(f"{self.name} 连接成功")
        else:
            self.logger.warning(f"{self.name} 连接失败")
        return success
    
    def disconnect(self):
        """断开连接"""
        self.is_connected = False
        self.logger.info(f"{self.name} 连接已断开")
    
    def get_stock_list(self, market: str = None):
        """获取股票列表"""
        if not self.is_connected:
            self.connect()
        
        # 模拟获取股票列表
        import pandas as pd
        sample_stocks = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ"},
            {"symbol": "000002", "name": "万科A", "market": "SZ"},
            {"symbol": "600000", "name": "浦发银行", "market": "SH"},
            {"symbol": "600036", "name": "招商银行", "market": "SH"},
            {"symbol": "600519", "name": "贵州茅台", "market": "SH"}
        ]
        
        df = pd.DataFrame(sample_stocks)
        if market:
            df = df[df['market'] == market]
        
        return df
    
    def get_stock_data(self, symbol: str, start_date: str = None, end_date: str = None, period: str = "daily"):
        """获取股票数据"""
        if not self.is_connected:
            self.connect()
        
        # 模拟网络延迟
        latency = random.randint(*self.latency_range)
        time.sleep(latency / 1000.0)
        
        # 模拟数据获取成功率
        if random.random() > self.reliability:
            self.logger.warning(f"{self.name} 获取 {symbol} 数据失败")
            return None
        
        # 生成模拟数据
        import pandas as pd
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        data = []
        base_price = 10.0 + random.uniform(-5, 5)
        
        for date in date_range:
            if date.weekday() >= 5:  # 跳过周末
                continue
            
            change = random.uniform(-0.5, 0.5)
            base_price += change
            
            data.append({
                'date': date,
                'symbol': symbol,
                'open': round(base_price + random.uniform(-0.2, 0.2), 2),
                'high': round(base_price + random.uniform(0, 0.5), 2),
                'low': round(base_price - random.uniform(0, 0.5), 2),
                'close': round(base_price, 2),
                'volume': random.randint(1000000, 10000000),
                'amount': round(base_price * random.randint(1000000, 10000000), 2)
            })
        
        df = pd.DataFrame(data)
        df.set_index('date', inplace=True)
        
        self.logger.info(f"{self.name} 成功获取 {symbol} 数据，共 {len(df)} 条记录")
        return df
    
    def get_realtime_data(self, symbols: List[str]):
        """获取实时数据"""
        if not self.is_connected:
            self.connect()
        
        # 模拟实时数据
        import pandas as pd
        data = []
        
        for symbol in symbols:
            if random.random() < self.reliability:
                data.append({
                    'symbol': symbol,
                    'price': round(10.0 + random.uniform(-5, 5), 2),
                    'change': round(random.uniform(-1, 1), 2),
                    'pct_change': round(random.uniform(-10, 10), 2),
                    'volume': random.randint(1000000, 10000000),
                    'timestamp': datetime.now()
                })
        
        return pd.DataFrame(data)


class MultiSourceManager:
    """多数据源管理器"""
    
    def __init__(self):
        self.logger = get_logger("MultiSourceManager")
        self.data_sources: Dict[str, BaseCollector] = {}
        self.source_status: Dict[str, Dict] = {}
        self.primary_source = None
        self.fallback_sources = []
        self.init_data_sources()
    
    def init_data_sources(self):
        """初始化数据源"""
        try:
            # 添加真实的AKShare数据源
            akshare_collector = AKShareCollector()
            self.add_data_source("AKShare", akshare_collector)
            
            # 添加模拟数据源
            tushare_collector = MockDataCollector("Tushare", reliability=0.85)
            self.add_data_source("Tushare", tushare_collector)
            
            wind_collector = MockDataCollector("Wind", reliability=0.92)
            self.add_data_source("Wind", wind_collector)
            
            yahoo_collector = MockDataCollector("Yahoo Finance", reliability=0.88)
            self.add_data_source("Yahoo Finance", yahoo_collector)
            
            sina_collector = MockDataCollector("新浪财经", reliability=0.80)
            self.add_data_source("新浪财经", sina_collector)
            
            # 设置主要数据源和备用数据源
            self.set_primary_source("AKShare")
            self.set_fallback_sources(["Tushare", "Wind", "Yahoo Finance"])
            
            self.logger.info("多数据源管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化数据源失败: {e}")
    
    def add_data_source(self, name: str, collector: BaseCollector):
        """添加数据源"""
        self.data_sources[name] = collector
        self.source_status[name] = {
            'status': DataSourceStatus.OFFLINE,
            'last_check': None,
            'latency': 0,
            'success_rate': 0.0,
            'total_requests': 0,
            'successful_requests': 0,
            'error_count': 0
        }
        self.logger.info(f"添加数据源: {name}")
    
    def remove_data_source(self, name: str):
        """移除数据源"""
        if name in self.data_sources:
            self.data_sources[name].disconnect()
            del self.data_sources[name]
            del self.source_status[name]
            self.logger.info(f"移除数据源: {name}")
    
    def set_primary_source(self, name: str):
        """设置主要数据源"""
        if name in self.data_sources:
            self.primary_source = name
            self.logger.info(f"设置主要数据源: {name}")
        else:
            self.logger.warning(f"数据源 {name} 不存在")
    
    def set_fallback_sources(self, names: List[str]):
        """设置备用数据源"""
        valid_sources = [name for name in names if name in self.data_sources]
        self.fallback_sources = valid_sources
        self.logger.info(f"设置备用数据源: {valid_sources}")
    
    def check_source_status(self, name: str) -> DataSourceStatus:
        """检查数据源状态"""
        if name not in self.data_sources:
            return DataSourceStatus.ERROR
        
        try:
            start_time = time.time()
            collector = self.data_sources[name]
            
            # 尝试连接
            success = collector.connect()
            latency = int((time.time() - start_time) * 1000)
            
            # 更新状态
            status_info = self.source_status[name]
            status_info['last_check'] = datetime.now()
            status_info['latency'] = latency
            
            if success:
                status_info['status'] = DataSourceStatus.ONLINE
                status_info['successful_requests'] += 1
            else:
                status_info['status'] = DataSourceStatus.OFFLINE
                status_info['error_count'] += 1
            
            status_info['total_requests'] += 1
            if status_info['total_requests'] > 0:
                status_info['success_rate'] = status_info['successful_requests'] / status_info['total_requests']
            
            return status_info['status']
            
        except Exception as e:
            self.logger.error(f"检查数据源 {name} 状态失败: {e}")
            self.source_status[name]['status'] = DataSourceStatus.ERROR
            self.source_status[name]['error_count'] += 1
            return DataSourceStatus.ERROR
    
    def check_all_sources(self):
        """检查所有数据源状态"""
        for name in self.data_sources:
            self.check_source_status(name)
    
    def get_source_status(self, name: str) -> Dict:
        """获取数据源状态信息"""
        return self.source_status.get(name, {})
    
    def get_all_source_status(self) -> Dict:
        """获取所有数据源状态"""
        return self.source_status.copy()
    
    def get_best_source(self, exclude: List[str] = None) -> Optional[str]:
        """获取最佳数据源"""
        exclude = exclude or []
        
        # 首先尝试主要数据源
        if self.primary_source and self.primary_source not in exclude:
            if self.check_source_status(self.primary_source) == DataSourceStatus.ONLINE:
                return self.primary_source
        
        # 尝试备用数据源
        for source_name in self.fallback_sources:
            if source_name not in exclude:
                if self.check_source_status(source_name) == DataSourceStatus.ONLINE:
                    return source_name
        
        # 如果都不可用，返回成功率最高的
        available_sources = [name for name in self.data_sources if name not in exclude]
        if available_sources:
            best_source = max(available_sources, 
                            key=lambda x: self.source_status[x]['success_rate'])
            return best_source
        
        return None
    
    def get_stock_data_with_fallback(self, symbol: str, start_date: str = None, 
                                   end_date: str = None, period: str = "daily"):
        """使用备用机制获取股票数据"""
        tried_sources = []
        
        while len(tried_sources) < len(self.data_sources):
            best_source = self.get_best_source(exclude=tried_sources)
            if not best_source:
                break
            
            try:
                self.logger.info(f"尝试从 {best_source} 获取 {symbol} 数据")
                collector = self.data_sources[best_source]
                data = collector.get_stock_data(symbol, start_date, end_date, period)
                
                if data is not None and not data.empty:
                    self.logger.info(f"成功从 {best_source} 获取数据")
                    return data, best_source
                else:
                    self.logger.warning(f"{best_source} 返回空数据")
                    
            except Exception as e:
                self.logger.error(f"从 {best_source} 获取数据失败: {e}")
            
            tried_sources.append(best_source)
        
        self.logger.error(f"所有数据源都无法获取 {symbol} 数据")
        return None, None
    
    def get_realtime_data_with_fallback(self, symbols: List[str]):
        """使用备用机制获取实时数据"""
        tried_sources = []
        
        while len(tried_sources) < len(self.data_sources):
            best_source = self.get_best_source(exclude=tried_sources)
            if not best_source:
                break
            
            try:
                self.logger.info(f"尝试从 {best_source} 获取实时数据")
                collector = self.data_sources[best_source]
                data = collector.get_realtime_data(symbols)
                
                if data is not None and not data.empty:
                    self.logger.info(f"成功从 {best_source} 获取实时数据")
                    return data, best_source
                else:
                    self.logger.warning(f"{best_source} 返回空实时数据")
                    
            except Exception as e:
                self.logger.error(f"从 {best_source} 获取实时数据失败: {e}")
            
            tried_sources.append(best_source)
        
        self.logger.error("所有数据源都无法获取实时数据")
        return None, None
    
    def get_source_performance_report(self) -> Dict:
        """获取数据源性能报告"""
        report = {}
        
        for name, status in self.source_status.items():
            report[name] = {
                'status': status['status'].value if isinstance(status['status'], DataSourceStatus) else str(status['status']),
                'success_rate': f"{status['success_rate']:.2%}",
                'average_latency': f"{status['latency']}ms",
                'total_requests': status['total_requests'],
                'successful_requests': status['successful_requests'],
                'error_count': status['error_count'],
                'last_check': status['last_check'].strftime('%Y-%m-%d %H:%M:%S') if status['last_check'] else '未检查'
            }
        
        return report

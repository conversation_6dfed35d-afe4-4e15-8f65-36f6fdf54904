#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强数据查看功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    try:
        print("测试导入模块...")
        
        from gui.widgets.enhanced_data_view_widget import (
            EnhancedDataViewWidget, DataVisualizationWidget, AdvancedDataTable
        )
        print("✓ 增强数据查看组件导入成功")
        
        from gui.widgets.data_center_widget import DataCenterWidget
        print("✓ 数据中心组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_creation():
    """测试组件创建"""
    try:
        print("\n测试组件创建...")
        
        # 需要QApplication才能创建Qt组件
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_data_view_widget import (
            EnhancedDataViewWidget, DataVisualizationWidget, AdvancedDataTable
        )
        
        # 测试数据可视化组件
        viz_widget = DataVisualizationWidget()
        print("✓ 数据可视化组件创建成功")
        
        # 测试高级数据表格
        table_widget = AdvancedDataTable()
        print("✓ 高级数据表格创建成功")
        
        # 测试增强数据查看组件
        view_widget = EnhancedDataViewWidget()
        print("✓ 增强数据查看组件创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 组件创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_table_functionality():
    """测试数据表格功能"""
    try:
        print("\n测试数据表格功能...")
        
        from PyQt5.QtWidgets import QApplication, QTableWidgetItem
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_data_view_widget import AdvancedDataTable
        
        table = AdvancedDataTable()
        
        # 测试设置表格数据
        table.setRowCount(3)
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["列1", "列2", "列3"])
        
        # 填充测试数据
        for row in range(3):
            for col in range(3):
                item = QTableWidgetItem(f"数据{row}-{col}")
                table.setItem(row, col, item)
        
        print("✓ 数据表格数据设置成功")
        
        # 测试排序功能
        table.setSortingEnabled(True)
        print("✓ 数据表格排序功能启用成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据表格功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_functionality():
    """测试数据可视化功能"""
    try:
        print("\n测试数据可视化功能...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_data_view_widget import DataVisualizationWidget
        
        viz_widget = DataVisualizationWidget()
        
        # 测试设置数据
        test_data = [
            {'date': '2024-01-01', 'close': 10.0, 'volume': 1000000},
            {'date': '2024-01-02', 'close': 10.5, 'volume': 1200000},
            {'date': '2024-01-03', 'close': 10.2, 'volume': 900000}
        ]
        
        viz_widget.set_data(test_data)
        print("✓ 数据可视化数据设置成功")
        
        # 测试图表类型切换
        chart_types = ["K线图", "收盘价线图", "成交量柱状图", "价格分布图"]
        for chart_type in chart_types:
            viz_widget.chart_type_combo.setCurrentText(chart_type)
            print(f"✓ {chart_type} 切换成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据可视化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_functionality():
    """测试查询功能"""
    try:
        print("\n测试查询功能...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QDate
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.enhanced_data_view_widget import EnhancedDataViewWidget
        
        view_widget = EnhancedDataViewWidget()
        
        # 设置查询参数
        view_widget.symbol_input.setText("000001")
        view_widget.start_date.setDate(QDate.currentDate().addDays(-30))
        view_widget.end_date.setDate(QDate.currentDate())
        
        print("✓ 查询参数设置成功")
        
        # 执行查询
        view_widget.query_data()
        print("✓ 数据查询执行成功")
        
        # 检查查询结果
        if len(view_widget.current_data) > 0:
            print(f"✓ 查询到 {len(view_widget.current_data)} 条数据")
        else:
            print("✗ 查询结果为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 查询功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_center_integration():
    """测试数据中心集成"""
    try:
        print("\n测试数据中心集成...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.data_center_widget import DataCenterWidget
        
        data_center = DataCenterWidget()
        print("✓ 数据中心组件创建成功")
        
        # 检查标签页数量
        tab_count = data_center.tab_widget.count()
        print(f"✓ 标签页数量: {tab_count}")
        
        # 检查数据查看标签页
        view_tab_index = -1
        for i in range(tab_count):
            if data_center.tab_widget.tabText(i) == "数据查看":
                view_tab_index = i
                break
        
        if view_tab_index >= 0:
            print("✓ 数据查看标签页集成成功")
            
            # 检查是否是增强版组件
            view_widget = data_center.tab_widget.widget(view_tab_index)
            if hasattr(view_widget, 'visualization') and hasattr(view_widget, 'data_table'):
                print("✓ 增强数据查看功能集成成功")
            else:
                print("✗ 增强数据查看功能集成失败")
                return False
        else:
            print("✗ 数据查看标签页集成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据中心集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("增强数据查看功能测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("组件创建", test_component_creation),
        ("数据表格功能", test_data_table_functionality),
        ("数据可视化功能", test_visualization_functionality),
        ("查询功能", test_query_functionality),
        ("数据中心集成", test_data_center_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强数据查看功能正常")
        print("\n新增功能特性:")
        print("✓ 数据可视化 - K线图、线图、柱状图、分布图")
        print("✓ 高级表格 - 排序、筛选、右键菜单")
        print("✓ 多种导出 - CSV、Excel、JSON格式")
        print("✓ 智能筛选 - 价格范围、日期范围、数据类型")
        print("✓ 数据复制 - 支持复制到剪贴板")
        print("✓ 分割视图 - 表格和图表并排显示")
        print("✓ 状态监控 - 实时显示查询状态和记录数")
        print("✓ 右键操作 - 导出选中行、排序等")
        print("✓ 完美集成 - 无缝集成到数据中心")
        return 0
    else:
        print("❌ 部分测试失败，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())

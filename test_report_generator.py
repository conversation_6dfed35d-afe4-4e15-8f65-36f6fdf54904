#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试报告生成功能
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from reports.report_generator import ReportGenerator
from analysis.technical_indicators import TechnicalIndicators
from analysis.fundamental_analysis import FundamentalAnalysis
from analysis.quantitative_analysis import QuantitativeAnalysis
from utils.logger import get_logger


def create_sample_data():
    """创建示例数据"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    # 生成模拟股价数据
    np.random.seed(42)
    base_price = 50
    returns = np.random.normal(0.001, 0.02, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # 生成OHLCV数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        open_price = close if i == 0 else prices[i-1] * (1 + np.random.normal(0, 0.005))
        high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000000, 10000000)
        
        data.append({
            'date': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    return df


def test_technical_analysis_report():
    """测试技术分析报告生成"""
    logger = get_logger("TestReportGenerator")
    
    print("=" * 60)
    print("技术分析报告生成测试")
    print("=" * 60)
    
    try:
        # 创建报告生成器
        rg = ReportGenerator()
        
        # 创建示例数据
        symbol = "000001.SZ"
        data = create_sample_data()
        
        # 计算技术指标
        ti = TechnicalIndicators()
        indicators_data = ti.calculate_all_indicators(data)
        
        print(f"生成技术分析数据: {symbol}")
        print(f"数据周期: {data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")
        print(f"技术指标数量: {len(indicators_data.columns) - len(data.columns)}")
        
        # 生成技术分析报告
        analysis_results = {
            'symbol': symbol,
            'latest_price': data['close'].iloc[-1],
            'price_change': data['close'].iloc[-1] - data['close'].iloc[-2],
            'volume': data['volume'].iloc[-1]
        }
        
        report_path = rg.generate_technical_analysis_report(
            symbol, data, indicators_data, analysis_results
        )
        
        if report_path and Path(report_path).exists():
            print(f"✅ 技术分析报告生成成功: {report_path}")
            
            # 读取并显示部分内容
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print("\n报告内容预览:")
                print("-" * 40)
                for line in lines[:15]:  # 显示前15行
                    print(line)
                print("...")
                print(f"报告总行数: {len(lines)}")
            
            return True
        else:
            print("❌ 技术分析报告生成失败")
            return False
            
    except Exception as e:
        logger.error(f"技术分析报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fundamental_analysis_report():
    """测试基本面分析报告生成"""
    logger = get_logger("TestFundamentalReport")
    
    print("\n" + "=" * 60)
    print("基本面分析报告生成测试")
    print("=" * 60)
    
    try:
        # 创建报告生成器
        rg = ReportGenerator()
        
        # 创建基本面分析数据
        symbol = "000002.SZ"
        fa = FundamentalAnalysis()
        analysis_result = fa.analyze_stock(symbol, "房地产")
        
        print(f"生成基本面分析数据: {symbol}")
        print(f"所属行业: {analysis_result.get('industry', 'N/A')}")
        print(f"综合评级: {analysis_result.get('evaluation', {}).get('综合评级', 'N/A')}")
        
        # 生成基本面分析报告
        report_path = rg.generate_fundamental_analysis_report(symbol, analysis_result)
        
        if report_path and Path(report_path).exists():
            print(f"✅ 基本面分析报告生成成功: {report_path}")
            
            # 读取并显示部分内容
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print("\n报告内容预览:")
                print("-" * 40)
                for line in lines[:15]:  # 显示前15行
                    print(line)
                print("...")
                print(f"报告总行数: {len(lines)}")
            
            return True
        else:
            print("❌ 基本面分析报告生成失败")
            return False
            
    except Exception as e:
        logger.error(f"基本面分析报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_quantitative_analysis_report():
    """测试量化分析报告生成"""
    logger = get_logger("TestQuantitativeReport")
    
    print("\n" + "=" * 60)
    print("量化分析报告生成测试")
    print("=" * 60)
    
    try:
        # 创建报告生成器
        rg = ReportGenerator()
        
        # 创建量化分析数据
        symbols = ['STOCK_A', 'STOCK_B', 'STOCK_C']
        qa = QuantitativeAnalysis()
        
        # 生成市场数据
        market_data = qa.generate_sample_market_data(symbols, days=252)
        
        # 进行风险分析
        risk_analysis = qa.perform_risk_analysis(market_data)
        
        # 计算相关性矩阵
        correlation_matrix = qa.calculate_correlation_matrix(market_data)
        
        # 进行主成分分析
        pca_results = qa.perform_pca_analysis(market_data, n_components=3)
        
        print(f"生成量化分析数据: {len(symbols)} 个标的")
        print(f"分析周期: 252 个交易日")
        print(f"主成分数量: {pca_results.get('n_components', 0)}")
        
        # 生成量化分析报告
        report_path = rg.generate_quantitative_analysis_report(
            symbols, risk_analysis, correlation_matrix, pca_results
        )
        
        if report_path and Path(report_path).exists():
            print(f"✅ 量化分析报告生成成功: {report_path}")
            
            # 读取并显示部分内容
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print("\n报告内容预览:")
                print("-" * 40)
                for line in lines[:15]:  # 显示前15行
                    print(line)
                print("...")
                print(f"报告总行数: {len(lines)}")
            
            return True
        else:
            print("❌ 量化分析报告生成失败")
            return False
            
    except Exception as e:
        logger.error(f"量化分析报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comprehensive_report():
    """测试综合分析报告生成"""
    logger = get_logger("TestComprehensiveReport")
    
    print("\n" + "=" * 60)
    print("综合分析报告生成测试")
    print("=" * 60)
    
    try:
        # 创建报告生成器
        rg = ReportGenerator()
        
        # 准备技术分析数据
        symbol = "600036.SH"
        data = create_sample_data()
        ti = TechnicalIndicators()
        indicators_data = ti.calculate_all_indicators(data)
        
        technical_data = {
            'data': data,
            'indicators_data': indicators_data
        }
        
        # 准备基本面分析数据
        fa = FundamentalAnalysis()
        fundamental_data = fa.analyze_stock(symbol, "银行")
        
        print(f"生成综合分析数据: {symbol}")
        print(f"技术面指标: {len(indicators_data.columns) - len(data.columns)} 个")
        print(f"基本面评级: {fundamental_data.get('evaluation', {}).get('综合评级', 'N/A')}")
        
        # 生成综合分析报告
        report_path = rg.generate_comprehensive_report(
            symbol, technical_data, fundamental_data
        )
        
        if report_path and Path(report_path).exists():
            print(f"✅ 综合分析报告生成成功: {report_path}")
            
            # 读取并显示部分内容
            with open(report_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print("\n报告内容预览:")
                print("-" * 40)
                for line in lines[:20]:  # 显示前20行
                    print(line)
                print("...")
                print(f"报告总行数: {len(lines)}")
            
            return True
        else:
            print("❌ 综合分析报告生成失败")
            return False
            
    except Exception as e:
        logger.error(f"综合分析报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始报告生成功能测试...")
    
    success = True
    
    # 测试技术分析报告
    if not test_technical_analysis_report():
        success = False
    
    # 测试基本面分析报告
    if not test_fundamental_analysis_report():
        success = False
    
    # 测试量化分析报告
    if not test_quantitative_analysis_report():
        success = False
    
    # 测试综合分析报告
    if not test_comprehensive_report():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 报告生成功能测试全部通过！")
        print("\n功能特点:")
        print("✅ 技术分析报告 - 完整的技术指标分析和交易信号")
        print("✅ 基本面分析报告 - 财务比率分析和投资评级")
        print("✅ 量化分析报告 - 风险收益分析和投资组合建议")
        print("✅ 综合分析报告 - 技术面+基本面综合评估")
        
        print("\n报告特色:")
        print("• Markdown格式，易于阅读和分享")
        print("• 专业的投资分析框架")
        print("• 详细的风险提示和免责声明")
        print("• 智能的评级和建议系统")
        
        # 显示报告文件位置
        reports_dir = Path(__file__).parent / "reports" / "output"
        if reports_dir.exists():
            report_files = list(reports_dir.glob("*.md"))
            print(f"\n生成的报告文件 ({len(report_files)} 个):")
            for report_file in sorted(report_files)[-4:]:  # 显示最新的4个文件
                print(f"  📄 {report_file.name}")
        
        print("\n下一步可以:")
        print("1. 集成到主界面的报告导出功能")
        print("2. 添加PDF格式报告生成")
        print("3. 实现报告模板自定义")
        print("4. 添加图表和可视化内容")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("=" * 60)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())

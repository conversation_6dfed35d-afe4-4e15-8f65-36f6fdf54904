# 量化交易系统 v1.0.0

一个功能完整的量化交易系统，支持多种数据源、技术分析、策略回测和风险管理。

## 🎯 项目特色

- **模块化设计** - 易于扩展和维护的架构
- **多数据源支持** - AKShare、Tushare等主流数据接口
- **丰富的技术指标** - 27种技术指标，涵盖趋势、震荡、成交量分析
- **完整的风险管理** - VaR、回撤、夏普比率等风险指标
- **灵活的策略框架** - 支持多种交易策略，易于扩展
- **美观的界面** - PyQt5深色主题，用户体验优秀
- **高性能计算** - 支持大规模数据处理和实时分析

## 📋 系统功能

### 数据采集模块
- ✅ 多数据源支持（AKShare、Tushare Pro、东方财富）
- ✅ 实时和历史数据获取
- ✅ 数据验证和清理
- ✅ 自动数据更新

### 技术分析模块
- ✅ 27种技术指标
  - 趋势指标：SMA、EMA、MACD、DMI、ADX
  - 震荡指标：RSI、KDJ、CCI、Williams %R
  - 成交量指标：OBV、VWAP、A/D Line
  - 波动率指标：Bollinger Bands、ATR
- ✅ 批量指标计算
- ✅ 自定义指标支持

### 策略引擎
- ✅ 策略基类框架
- ✅ 移动平均线策略
- ✅ 双移动平均线策略
- ✅ 信号生成和验证
- ✅ 风险控制机制

### 风险管理
- ✅ 实时风险监控
- ✅ 止损止盈机制
- ✅ 仓位管理
- ✅ 风险指标计算

### 绩效分析
- ✅ 全面的绩效评估
- ✅ 风险指标分析
- ✅ 回测报告生成
- ✅ 图表可视化

### 数据存储
- ✅ SQLite数据库
- ✅ 多表结构设计
- ✅ 数据备份和恢复

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows 10/11

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

## 📁 项目结构

```
quantitative_trading_system/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── config/                    # 配置文件
│   ├── settings.py           # 系统配置
│   └── api_config.py         # API配置
├── data/                     # 数据模块
│   ├── collectors/           # 数据采集器
│   │   ├── akshare_collector.py
│   │   └── base_collector.py
│   └── database/             # 数据库操作
│       ├── models.py
│       └── manager.py
├── strategies/               # 策略模块
│   ├── base_strategy.py      # 策略基类
│   └── technical/            # 技术分析策略
│       └── ma_strategy.py
├── analysis/                 # 分析工具
│   ├── technical_indicators.py
│   ├── risk_metrics.py
│   └── performance.py
├── trading/                  # 交易模块
├── gui/                      # 图形界面
│   ├── main_window.py
│   └── styles/
│       └── dark_theme.py
└── utils/                    # 工具模块
    ├── logger.py
    ├── constants.py
    └── helpers.py
```

## 🔧 配置说明

### 数据源配置
在 `config/settings.py` 中配置数据源参数：

```python
DATA_SOURCES = {
    "tushare": {
        "enabled": True,
        "token": "your_token_here",
        "timeout": 30
    },
    "akshare": {
        "enabled": True,
        "timeout": 30
    }
}
```

### 交易配置
```python
TRADING_CONFIG = {
    "initial_capital": 1000000,  # 初始资金
    "commission_rate": 0.0003,   # 手续费率
    "slippage": 0.001,          # 滑点
    "max_position": 0.1,        # 最大仓位
}
```

## 📊 技术指标列表

### 趋势指标
- SMA - 简单移动平均线
- EMA - 指数移动平均线
- MACD - 移动平均收敛发散
- DMI - 趋向指标
- ADX - 平均趋向指数

### 震荡指标
- RSI - 相对强弱指数
- KDJ - 随机指标
- CCI - 商品通道指数
- Williams %R - 威廉指标

### 成交量指标
- OBV - 能量潮
- VWAP - 成交量加权平均价
- A/D Line - 累积/派发线

### 波动率指标
- Bollinger Bands - 布林带
- ATR - 平均真实波幅

## 📈 策略示例

### 移动平均线策略
```python
from strategies.technical.ma_strategy import MovingAverageStrategy

# 创建策略
strategy = MovingAverageStrategy(
    name="MA策略",
    parameters={
        'fast_period': 5,
        'slow_period': 20,
        'ma_type': 'ema',
        'position_size': 0.1
    }
)

# 启动策略
strategy.start()

# 生成信号
signals = strategy.generate_signals(data)
```

## 🧪 测试

运行完整系统测试：
```bash
python test_complete_system.py
```

运行模块测试：
```bash
python test_data_module.py
python test_analysis_module.py
python test_strategy_module.py
```

## 📋 测试结果

所有模块测试均已通过：
- ✅ 数据采集模块测试
- ✅ 技术分析模块测试
- ✅ 策略引擎模块测试
- ✅ 风险管理模块测试
- ✅ 绩效分析模块测试
- ✅ 完整系统集成测试

## 🔮 未来规划

### 短期目标
- [ ] 更多技术指标
- [ ] 机器学习策略
- [ ] 实盘交易接口
- [ ] 移动端支持

### 长期目标
- [ ] 分布式计算
- [ ] 云端部署
- [ ] 社区策略分享
- [ ] 专业版功能

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- QQ群：123456789

---

**免责声明**：本系统仅供学习和研究使用，不构成投资建议。投资有风险，入市需谨慎。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多数据源管理功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    try:
        print("测试导入模块...")
        
        from data.collectors.multi_source_manager import (
            MultiSourceManager, DataSourceType, DataSourceStatus, MockDataCollector
        )
        print("✓ 多数据源管理器导入成功")
        
        from gui.widgets.data_source_manager_widget import (
            DataSourceManagerWidget, DataSourceStatusWidget, DataSourceConfigWidget
        )
        print("✓ 数据源管理界面组件导入成功")
        
        from gui.widgets.data_center_widget import DataCenterWidget
        print("✓ 数据中心组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_manager():
    """测试多数据源管理器"""
    try:
        print("\n测试多数据源管理器...")
        
        from data.collectors.multi_source_manager import MultiSourceManager
        
        # 创建管理器
        manager = MultiSourceManager()
        print("✓ 多数据源管理器创建成功")
        
        # 检查数据源数量
        source_count = len(manager.data_sources)
        print(f"✓ 已注册 {source_count} 个数据源")
        
        # 检查主要数据源
        if manager.primary_source:
            print(f"✓ 主要数据源: {manager.primary_source}")
        
        # 检查备用数据源
        if manager.fallback_sources:
            print(f"✓ 备用数据源: {manager.fallback_sources}")
        
        # 测试状态检查
        for source_name in list(manager.data_sources.keys())[:2]:  # 只测试前两个
            status = manager.check_source_status(source_name)
            print(f"✓ {source_name} 状态检查完成: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ 多数据源管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_source_fallback():
    """测试数据源备用机制"""
    try:
        print("\n测试数据源备用机制...")
        
        from data.collectors.multi_source_manager import MultiSourceManager
        
        manager = MultiSourceManager()
        
        # 测试获取最佳数据源
        best_source = manager.get_best_source()
        if best_source:
            print(f"✓ 最佳数据源: {best_source}")
        else:
            print("✗ 未找到可用数据源")
            return False
        
        # 测试备用机制获取数据
        data, source_used = manager.get_stock_data_with_fallback("000001", "2024-01-01", "2024-01-31")
        if data is not None and source_used:
            print(f"✓ 成功从 {source_used} 获取数据，共 {len(data)} 条记录")
        else:
            print("✗ 备用机制获取数据失败")
            return False
        
        # 测试实时数据备用机制
        realtime_data, source_used = manager.get_realtime_data_with_fallback(["000001", "000002"])
        if realtime_data is not None and source_used:
            print(f"✓ 成功从 {source_used} 获取实时数据，共 {len(realtime_data)} 条记录")
        else:
            print("✗ 备用机制获取实时数据失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据源备用机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_monitoring():
    """测试性能监控"""
    try:
        print("\n测试性能监控...")
        
        from data.collectors.multi_source_manager import MultiSourceManager
        
        manager = MultiSourceManager()
        
        # 生成性能报告
        report = manager.get_source_performance_report()
        if report:
            print("✓ 性能报告生成成功")
            for source_name, metrics in report.items():
                print(f"  {source_name}: 成功率 {metrics['success_rate']}, 延迟 {metrics['average_latency']}")
        else:
            print("✗ 性能报告生成失败")
            return False
        
        # 测试状态信息获取
        all_status = manager.get_all_source_status()
        if all_status:
            print(f"✓ 获取所有数据源状态成功，共 {len(all_status)} 个数据源")
        else:
            print("✗ 获取数据源状态失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 性能监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """测试GUI组件"""
    try:
        print("\n测试GUI组件...")
        
        # 需要QApplication才能创建Qt组件
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.data_source_manager_widget import (
            DataSourceManagerWidget, DataSourceStatusWidget, DataSourceConfigWidget
        )
        
        # 测试状态监控组件
        status_widget = DataSourceStatusWidget()
        print("✓ 数据源状态监控组件创建成功")
        
        # 测试配置管理组件
        config_widget = DataSourceConfigWidget()
        print("✓ 数据源配置管理组件创建成功")
        
        # 测试主管理组件
        manager_widget = DataSourceManagerWidget()
        print("✓ 数据源管理主组件创建成功")
        
        # 清理资源
        status_widget.stop_status_monitoring()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_center_integration():
    """测试数据中心集成"""
    try:
        print("\n测试数据中心集成...")
        
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from gui.widgets.data_center_widget import DataCenterWidget
        
        data_center = DataCenterWidget()
        print("✓ 数据中心组件创建成功")
        
        # 检查标签页数量
        tab_count = data_center.tab_widget.count()
        print(f"✓ 标签页数量: {tab_count}")
        
        # 检查数据源管理标签页
        source_mgr_tab_index = -1
        for i in range(tab_count):
            if data_center.tab_widget.tabText(i) == "数据源管理":
                source_mgr_tab_index = i
                break
        
        if source_mgr_tab_index >= 0:
            print("✓ 数据源管理标签页集成成功")
            
            # 检查是否是数据源管理组件
            source_mgr_widget = data_center.tab_widget.widget(source_mgr_tab_index)
            if hasattr(source_mgr_widget, 'status_widget') and hasattr(source_mgr_widget, 'config_widget'):
                print("✓ 数据源管理功能集成成功")
            else:
                print("✗ 数据源管理功能集成失败")
                return False
        else:
            print("✗ 数据源管理标签页集成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据中心集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("多数据源管理功能测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("多数据源管理器", test_multi_source_manager),
        ("数据源备用机制", test_data_source_fallback),
        ("性能监控", test_performance_monitoring),
        ("GUI组件", test_gui_components),
        ("数据中心集成", test_data_center_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多数据源管理功能正常")
        print("\n新增功能特性:")
        print("✓ 多数据源支持 - AKShare、Tushare、Wind、Yahoo等")
        print("✓ 智能备用机制 - 主数据源失败时自动切换")
        print("✓ 状态监控 - 实时监控各数据源连接状态")
        print("✓ 性能统计 - 成功率、延迟、请求统计")
        print("✓ 配置管理 - 主数据源和备用数据源配置")
        print("✓ 可视化界面 - 状态表格、配置面板")
        print("✓ 自动重试 - 失败时智能重试机制")
        print("✓ 性能报告 - 详细的数据源性能分析")
        print("✓ 完美集成 - 无缝集成到数据中心")
        return 0
    else:
        print("❌ 部分测试失败，需要修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化机器学习模块
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger


class SimpleClassifier:
    """简单分类器"""

    def __init__(self, method: str = "threshold"):
        self.method = method
        self.is_fitted = False
        self.feature_weights = {}
        self.threshold = 0.0
        self.feature_names = []

    def fit(self, X: pd.DataFrame, y: pd.Series):
        """训练模型"""
        try:
            self.feature_names = X.columns.tolist()

            if self.method == "threshold":
                # 基于阈值的简单分类器
                correlations = X.corrwith(y).abs()
                self.feature_weights = correlations.to_dict()
                self.threshold = 0.5

            elif self.method == "linear":
                # 简单线性组合
                correlations = X.corrwith(y)
                # 归一化权重
                total_abs_corr = correlations.abs().sum()
                if total_abs_corr > 0:
                    self.feature_weights = (correlations / total_abs_corr).to_dict()
                else:
                    self.feature_weights = {col: 0 for col in X.columns}
                self.threshold = 0.0

            self.is_fitted = True
            return self

        except Exception as e:
            print(f"训练失败: {e}")
            return self

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """预测"""
        if not self.is_fitted:
            return np.zeros(len(X))

        try:
            # 确保特征顺序一致
            X = X[self.feature_names]

            # 计算加权得分
            scores = np.zeros(len(X))
            for feature, weight in self.feature_weights.items():
                if feature in X.columns:
                    scores += X[feature].fillna(0) * weight

            # 转换为预测结果
            if self.method == "threshold":
                predictions = (scores > self.threshold).astype(int)
            else:
                predictions = (scores > self.threshold).astype(int)

            return predictions

        except Exception as e:
            print(f"预测失败: {e}")
            return np.zeros(len(X))

    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """预测概率"""
        if not self.is_fitted:
            return np.zeros((len(X), 2))

        try:
            # 确保特征顺序一致
            X = X[self.feature_names]

            # 计算加权得分
            scores = np.zeros(len(X))
            for feature, weight in self.feature_weights.items():
                if feature in X.columns:
                    scores += X[feature].fillna(0) * weight

            # 转换为概率（使用sigmoid函数）
            probabilities = 1 / (1 + np.exp(-scores))

            # 返回两类概率
            proba_matrix = np.column_stack([1 - probabilities, probabilities])

            return proba_matrix

        except Exception as e:
            print(f"预测概率失败: {e}")
            return np.zeros((len(X), 2))


class SimpleMLManager:
    """简化机器学习管理器"""

    def __init__(self, model_dir: str = "models"):
        self.logger = get_logger("SimpleMLManager")
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)

        self.models = {}
        self.feature_names = {}
        self.model_configs = {}

    def create_model(self, model_name: str, model_type: str = "simple_classifier",
                    custom_params: Dict[str, Any] = None) -> bool:
        """创建模型"""
        try:
            if model_type == "simple_classifier":
                method = "threshold"
                if custom_params and "method" in custom_params:
                    method = custom_params["method"]

                model = SimpleClassifier(method=method)
                self.models[model_name] = model
                self.model_configs[model_name] = {
                    'type': model_type,
                    'method': method
                }

                self.logger.info(f"简单模型创建成功: {model_name}")
                return True
            else:
                self.logger.error(f"不支持的模型类型: {model_type}")
                return False

        except Exception as e:
            self.logger.error(f"创建模型失败: {e}")
            return False

    def train_model(self, model_name: str, X: pd.DataFrame, y: pd.Series,
                   test_size: float = 0.2) -> Dict[str, Any]:
        """训练模型"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return {}

            model = self.models[model_name]

            # 数据预处理
            X_clean = X.dropna()
            y_clean = y.loc[X_clean.index].dropna()
            X_clean = X_clean.loc[y_clean.index]

            if len(X_clean) == 0:
                self.logger.error("没有有效的训练数据")
                return {}

            # 保存特征名称
            self.feature_names[model_name] = X_clean.columns.tolist()

            # 分割数据
            split_idx = int(len(X_clean) * (1 - test_size))
            X_train = X_clean.iloc[:split_idx]
            X_test = X_clean.iloc[split_idx:]
            y_train = y_clean.iloc[:split_idx]
            y_test = y_clean.iloc[split_idx:]

            # 训练模型
            model.fit(X_train, y_train)

            # 评估
            train_pred = model.predict(X_train)
            test_pred = model.predict(X_test)

            # 计算准确率
            train_accuracy = np.mean(train_pred == y_train) if len(y_train) > 0 else 0
            test_accuracy = np.mean(test_pred == y_test) if len(y_test) > 0 else 0

            metrics = {
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features_count': X_clean.shape[1]
            }

            # 特征重要性（基于权重）
            if hasattr(model, 'feature_weights'):
                feature_importance = []
                for feature, weight in model.feature_weights.items():
                    feature_importance.append({
                        'feature': feature,
                        'importance': abs(weight)
                    })
                feature_importance.sort(key=lambda x: x['importance'], reverse=True)
                metrics['feature_importance'] = feature_importance[:20]

            self.logger.info(f"模型训练完成: {model_name}")
            return metrics

        except Exception as e:
            self.logger.error(f"训练模型失败: {e}")
            return {}

    def predict(self, model_name: str, X: pd.DataFrame) -> Optional[np.ndarray]:
        """模型预测"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return None

            model = self.models[model_name]

            if not model.is_fitted:
                self.logger.error(f"模型未训练: {model_name}")
                return None

            predictions = model.predict(X)
            return predictions

        except Exception as e:
            self.logger.error(f"模型预测失败: {e}")
            return None

    def predict_proba(self, model_name: str, X: pd.DataFrame) -> Optional[np.ndarray]:
        """预测概率"""
        try:
            if model_name not in self.models:
                return None

            model = self.models[model_name]

            if not model.is_fitted:
                return None

            probabilities = model.predict_proba(X)
            return probabilities

        except Exception as e:
            self.logger.error(f"预测概率失败: {e}")
            return None

    def save_model(self, model_name: str) -> bool:
        """保存模型"""
        try:
            if model_name not in self.models:
                self.logger.error(f"模型不存在: {model_name}")
                return False

            model = self.models[model_name]
            config_path = self.model_dir / f"{model_name}_config.json"

            # 保存模型配置
            config_data = {
                'model_config': self.model_configs[model_name],
                'feature_names': self.feature_names.get(model_name, []),
                'feature_weights': getattr(model, 'feature_weights', {}),
                'threshold': getattr(model, 'threshold', 0.0),
                'is_fitted': getattr(model, 'is_fitted', False),
                'save_time': datetime.now().isoformat()
            }

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"模型保存成功: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            return False

    def load_model(self, model_name: str) -> bool:
        """加载模型"""
        try:
            config_path = self.model_dir / f"{model_name}_config.json"

            if not config_path.exists():
                self.logger.error(f"模型配置文件不存在: {model_name}")
                return False

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 重建模型
            model_config = config_data['model_config']
            method = model_config.get('method', 'threshold')

            model = SimpleClassifier(method=method)
            model.feature_names = config_data['feature_names']
            model.feature_weights = config_data['feature_weights']
            model.threshold = config_data['threshold']
            model.is_fitted = config_data['is_fitted']

            self.models[model_name] = model
            self.model_configs[model_name] = model_config
            self.feature_names[model_name] = config_data['feature_names']

            self.logger.info(f"模型加载成功: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        if model_name not in self.models:
            return {}

        model = self.models[model_name]
        config = self.model_configs.get(model_name, {})

        info = {
            'model_name': model_name,
            'model_type': config.get('type', 'unknown'),
            'method': config.get('method', 'unknown'),
            'is_fitted': getattr(model, 'is_fitted', False),
            'feature_count': len(self.feature_names.get(model_name, [])),
            'feature_names': self.feature_names.get(model_name, [])
        }

        return info

    def list_models(self) -> List[str]:
        """列出所有模型"""
        return list(self.models.keys())

    def delete_model(self, model_name: str) -> bool:
        """删除模型"""
        try:
            if model_name in self.models:
                del self.models[model_name]
                del self.model_configs[model_name]
                if model_name in self.feature_names:
                    del self.feature_names[model_name]

                # 删除配置文件
                config_path = self.model_dir / f"{model_name}_config.json"
                if config_path.exists():
                    config_path.unlink()

                self.logger.info(f"模型删除成功: {model_name}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"删除模型失败: {e}")
            return False

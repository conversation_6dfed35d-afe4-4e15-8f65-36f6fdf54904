#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程模块
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from analysis.technical_indicators import TechnicalIndicators
from utils.logger import get_logger


class FeatureEngineer:
    """特征工程类"""

    def __init__(self):
        self.logger = get_logger("FeatureEngineer")
        self.tech_indicators = TechnicalIndicators()
        self.feature_names = []

    def create_features(self, data: pd.DataFrame, lookback_periods: List[int] = None) -> pd.DataFrame:
        """创建机器学习特征"""
        if lookback_periods is None:
            lookback_periods = [5, 10, 20, 30]

        try:
            features_df = data.copy()
            self.feature_names = []

            # 基础价格特征
            features_df = self._add_price_features(features_df, lookback_periods)

            # 技术指标特征
            features_df = self._add_technical_features(features_df)

            # 成交量特征
            features_df = self._add_volume_features(features_df, lookback_periods)

            # 波动率特征
            features_df = self._add_volatility_features(features_df, lookback_periods)

            # 趋势特征
            features_df = self._add_trend_features(features_df, lookback_periods)

            # 时间特征
            features_df = self._add_time_features(features_df)

            # 滞后特征
            features_df = self._add_lag_features(features_df, lookback_periods)

            # 移除无穷大和NaN值
            features_df = self._clean_features(features_df)

            self.logger.info(f"特征工程完成，共生成{len(self.feature_names)}个特征")
            return features_df

        except Exception as e:
            self.logger.error(f"特征工程失败: {e}")
            return data

    def _add_price_features(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """添加价格相关特征"""
        try:
            # 价格变化率
            for period in periods:
                df[f'price_change_{period}d'] = df['close'].pct_change(period)
                df[f'high_low_ratio_{period}d'] = (df['high'] - df['low']) / df['close']
                df[f'open_close_ratio_{period}d'] = (df['close'] - df['open']) / df['open']

                self.feature_names.extend([
                    f'price_change_{period}d',
                    f'high_low_ratio_{period}d',
                    f'open_close_ratio_{period}d'
                ])

            # 价格位置特征
            for period in periods:
                rolling_high = df['high'].rolling(window=period).max()
                rolling_low = df['low'].rolling(window=period).min()
                df[f'price_position_{period}d'] = (df['close'] - rolling_low) / (rolling_high - rolling_low)
                self.feature_names.append(f'price_position_{period}d')

            return df

        except Exception as e:
            self.logger.error(f"添加价格特征失败: {e}")
            return df

    def _add_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标特征"""
        try:
            # 移动平均线
            for period in [5, 10, 20, 50]:
                ma = df['close'].rolling(window=period).mean()
                df[f'ma_{period}'] = ma
                df[f'price_ma_ratio_{period}'] = df['close'] / ma
                self.feature_names.extend([f'ma_{period}', f'price_ma_ratio_{period}'])

            # RSI
            rsi = self.tech_indicators.rsi(df['close'], 14)
            df['rsi'] = rsi
            df['rsi_oversold'] = (rsi < 30).astype(int)
            df['rsi_overbought'] = (rsi > 70).astype(int)
            self.feature_names.extend(['rsi', 'rsi_oversold', 'rsi_overbought'])

            # MACD
            macd, macd_signal, macd_hist = self.tech_indicators.macd(df['close'])
            df['macd'] = macd
            df['macd_signal'] = macd_signal
            df['macd_histogram'] = macd_hist
            df['macd_bullish'] = (macd > macd_signal).astype(int)
            self.feature_names.extend(['macd', 'macd_signal', 'macd_histogram', 'macd_bullish'])

            # 布林带
            bb_upper, bb_middle, bb_lower = self.tech_indicators.bollinger_bands(df['close'])
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            df['bb_width'] = (bb_upper - bb_lower) / bb_middle
            df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            self.feature_names.extend(['bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_position'])

            # KDJ (如果可用)
            try:
                k, d, j = self.tech_indicators.kdj(df['high'], df['low'], df['close'])
                df['kdj_k'] = k
                df['kdj_d'] = d
                df['kdj_j'] = j
                self.feature_names.extend(['kdj_k', 'kdj_d', 'kdj_j'])
            except AttributeError:
                # KDJ方法不存在，跳过
                pass

            return df

        except Exception as e:
            self.logger.error(f"添加技术指标特征失败: {e}")
            return df

    def _add_volume_features(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """添加成交量特征"""
        try:
            # 成交量变化率
            for period in periods:
                df[f'volume_change_{period}d'] = df['volume'].pct_change(period)
                df[f'volume_ma_{period}d'] = df['volume'].rolling(window=period).mean()
                df[f'volume_ratio_{period}d'] = df['volume'] / df[f'volume_ma_{period}d']

                self.feature_names.extend([
                    f'volume_change_{period}d',
                    f'volume_ma_{period}d',
                    f'volume_ratio_{period}d'
                ])

            # 价量关系
            df['price_volume_trend'] = (df['close'].pct_change() * df['volume'].pct_change()).rolling(5).mean()
            self.feature_names.append('price_volume_trend')

            return df

        except Exception as e:
            self.logger.error(f"添加成交量特征失败: {e}")
            return df

    def _add_volatility_features(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """添加波动率特征"""
        try:
            # 历史波动率
            for period in periods:
                returns = df['close'].pct_change()
                df[f'volatility_{period}d'] = returns.rolling(window=period).std()
                df[f'volatility_ratio_{period}d'] = df[f'volatility_{period}d'] / df[f'volatility_{period}d'].rolling(60).mean()

                self.feature_names.extend([
                    f'volatility_{period}d',
                    f'volatility_ratio_{period}d'
                ])

            # ATR (Average True Range)
            atr = self.tech_indicators.atr(df['high'], df['low'], df['close'])
            df['atr'] = atr
            df['atr_ratio'] = atr / df['close']
            self.feature_names.extend(['atr', 'atr_ratio'])

            return df

        except Exception as e:
            self.logger.error(f"添加波动率特征失败: {e}")
            return df

    def _add_trend_features(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """添加趋势特征"""
        try:
            # 趋势强度
            for period in periods:
                # 线性回归斜率
                def calculate_slope(series):
                    if len(series) < 2:
                        return 0
                    x = np.arange(len(series))
                    y = series.values
                    slope = np.polyfit(x, y, 1)[0]
                    return slope

                df[f'trend_slope_{period}d'] = df['close'].rolling(window=period).apply(calculate_slope)
                self.feature_names.append(f'trend_slope_{period}d')

            # 趋势方向
            df['trend_up'] = (df['close'] > df['close'].shift(1)).astype(int)
            df['trend_strength'] = df['close'].pct_change().rolling(10).sum()
            self.feature_names.extend(['trend_up', 'trend_strength'])

            return df

        except Exception as e:
            self.logger.error(f"添加趋势特征失败: {e}")
            return df

    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        try:
            if df.index.dtype == 'datetime64[ns]':
                df['day_of_week'] = df.index.dayofweek
                df['day_of_month'] = df.index.day
                df['month'] = df.index.month
                df['quarter'] = df.index.quarter

                # 周期性特征
                df['is_month_end'] = (df.index.day > 25).astype(int)
                df['is_quarter_end'] = ((df.index.month % 3 == 0) & (df.index.day > 25)).astype(int)

                self.feature_names.extend([
                    'day_of_week', 'day_of_month', 'month', 'quarter',
                    'is_month_end', 'is_quarter_end'
                ])

            return df

        except Exception as e:
            self.logger.error(f"添加时间特征失败: {e}")
            return df

    def _add_lag_features(self, df: pd.DataFrame, periods: List[int]) -> pd.DataFrame:
        """添加滞后特征"""
        try:
            # 价格滞后
            for lag in [1, 2, 3, 5]:
                df[f'close_lag_{lag}'] = df['close'].shift(lag)
                df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
                self.feature_names.extend([f'close_lag_{lag}', f'volume_lag_{lag}'])

            # 技术指标滞后
            if 'rsi' in df.columns:
                for lag in [1, 2, 3]:
                    df[f'rsi_lag_{lag}'] = df['rsi'].shift(lag)
                    self.feature_names.append(f'rsi_lag_{lag}')

            return df

        except Exception as e:
            self.logger.error(f"添加滞后特征失败: {e}")
            return df

    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理特征数据"""
        try:
            # 替换无穷大值
            df = df.replace([np.inf, -np.inf], np.nan)

            # 前向填充NaN值
            df = df.fillna(method='ffill')

            # 如果还有NaN值，用0填充
            df = df.fillna(0)

            return df

        except Exception as e:
            self.logger.error(f"清理特征数据失败: {e}")
            return df

    def create_target(self, data: pd.DataFrame, target_type: str = "return",
                     periods: int = 5, threshold: float = 0.02) -> pd.Series:
        """创建目标变量"""
        try:
            if target_type == "return":
                # 未来收益率
                target = data['close'].shift(-periods) / data['close'] - 1

            elif target_type == "direction":
                # 未来方向（上涨/下跌）
                future_return = data['close'].shift(-periods) / data['close'] - 1
                target = (future_return > threshold).astype(int)

            elif target_type == "classification":
                # 三分类（上涨/横盘/下跌）
                future_return = data['close'].shift(-periods) / data['close'] - 1
                target = pd.cut(future_return,
                              bins=[-np.inf, -threshold, threshold, np.inf],
                              labels=[0, 1, 2])  # 0:下跌, 1:横盘, 2:上涨
                target = target.astype(int)

            else:
                raise ValueError(f"不支持的目标类型: {target_type}")

            self.logger.info(f"目标变量创建完成，类型: {target_type}")
            return target

        except Exception as e:
            self.logger.error(f"创建目标变量失败: {e}")
            return pd.Series(index=data.index, dtype=float)

    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_names.copy()

    def select_features(self, df: pd.DataFrame, target: pd.Series,
                       method: str = "correlation", top_k: int = 50) -> List[str]:
        """特征选择"""
        try:
            if method == "correlation":
                # 基于相关性的特征选择
                correlations = df[self.feature_names].corrwith(target).abs()
                selected_features = correlations.nlargest(top_k).index.tolist()

            elif method == "variance":
                # 基于方差的特征选择
                variances = df[self.feature_names].var()
                selected_features = variances.nlargest(top_k).index.tolist()

            else:
                selected_features = self.feature_names[:top_k]

            self.logger.info(f"特征选择完成，选择了{len(selected_features)}个特征")
            return selected_features

        except Exception as e:
            self.logger.error(f"特征选择失败: {e}")
            return self.feature_names[:top_k]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据概览仪表板组件
"""

import sys
import random
from pathlib import Path
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QGroupBox,
    QProgressBar, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger

# 暂时禁用matplotlib以避免导入问题
MATPLOTLIB_AVAILABLE = False

# try:
#     import matplotlib.pyplot as plt
#     from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
#     from matplotlib.figure import Figure
#     import matplotlib.dates as mdates
#     # 设置中文字体支持
#     try:
#         plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
#         plt.rcParams['axes.unicode_minus'] = False
#     except:
#         pass  # 如果字体设置失败，继续使用默认字体
#     MATPLOTLIB_AVAILABLE = True
# except ImportError:
#     MATPLOTLIB_AVAILABLE = False


class DataStatCard(QFrame):
    """数据统计卡片"""

    def __init__(self, title, value, unit="", color="#2196F3", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.color = color
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 10px;
            }}
            QFrame:hover {{
                border: 2px solid {self.color};
                background-color: #454545;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)

        # 标题
        title_label = QLabel(self.title)
        title_label.setFont(QFont("微软雅黑", 10))
        title_label.setStyleSheet("color: #cccccc;")
        layout.addWidget(title_label)

        # 数值
        self.value_label = QLabel(f"{self.value} {self.unit}")

        self.value_label.setStyleSheet("color: #ffffff !important; background-color: transparent !important;")
        self.value_label.setFont(QFont("微软雅黑", 18, QFont.Bold))
        self.value_label.setStyleSheet(f"color: {self.color};")
        layout.addWidget(self.value_label)

        # 设置固定大小
        self.setFixedSize(180, 100)

    def update_value(self, value, unit=""):
        """更新数值"""
        self.value = value
        self.unit = unit
        self.value_label.setText(f"{self.value} {self.unit}")


class DataQualityChart(QWidget):
    """数据质量图表"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataQualityChart")
        self.init_ui()

        # 定时更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_chart)
        self.update_timer.start(5000)  # 每5秒更新一次

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        if MATPLOTLIB_AVAILABLE:
            # 创建matplotlib图表
            self.figure = Figure(figsize=(8, 4), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            layout.addWidget(self.canvas)

            # 初始化图表
            self.update_chart()
        else:
            # 如果matplotlib不可用，显示简单的文本信息
            label = QLabel("数据质量监控\n(需要安装matplotlib)")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("color: #888888; font-size: 14px; background-color: transparent;")
            layout.addWidget(label)

    def update_chart(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            self.figure.clear()

            # 创建子图
            ax1 = self.figure.add_subplot(121)
            ax2 = self.figure.add_subplot(122)

            # 数据完整性饼图
            completeness_data = [85, 15]
            completeness_labels = ['完整数据', '缺失数据']
            colors1 = ['#4CAF50', '#F44336']

            ax1.pie(completeness_data, labels=completeness_labels, colors=colors1,
                   autopct='%1.1f%%', startangle=90)
            ax1.set_title('数据完整性', fontsize=12, fontweight='bold')

            # 数据源状态柱状图
            sources = ['AKShare', 'Tushare', 'Wind', 'Yahoo']
            status = [95, 88, 92, 85]  # 模拟数据源状态
            colors2 = ['#2196F3', '#FF9800', '#9C27B0', '#607D8B']

            bars = ax2.bar(sources, status, color=colors2)
            ax2.set_title('数据源状态', fontsize=12, fontweight='bold')
            ax2.set_ylabel('可用性 (%)')
            ax2.set_ylim(0, 100)

            # 添加数值标签
            for bar, value in zip(bars, status):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{value}%', ha='center', va='bottom', fontsize=10)

            # 调整布局
            self.figure.tight_layout()
            self.canvas.draw()

        except Exception as e:
            self.logger.error(f"更新数据质量图表失败: {e}")


class DataSourceStatus(QWidget):
    """数据源状态组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataSourceStatus")
        self.init_ui()

        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(3000)  # 每3秒更新一次

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("数据源状态")
        title_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        title_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(title_label)

        # 状态表格
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(3)
        self.status_table.setHorizontalHeaderLabels(["数据源", "状态", "延迟(ms)"])
        self.status_table.setAlternatingRowColors(True)
        self.status_table.verticalHeader().setVisible(False)

        # 设置列宽
        self.status_table.setColumnWidth(0, 100)
        self.status_table.setColumnWidth(1, 80)
        self.status_table.setColumnWidth(2, 100)

        # 设置表格背景为蓝色，确保白色文字可见
        self.status_table.setStyleSheet("""
            QTableWidget {
                background-color: #0078d4;
                color: #ffffff;
                gridline-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QTableWidget::item {
                background-color: #0078d4;
                color: #ffffff;
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #106ebe;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #005a9e;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #ffffff;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.status_table)

        # 初始化状态
        self.update_status()

    def update_status(self):
        """更新数据源状态"""
        try:
            sources = [
                {"name": "AKShare", "status": "在线", "latency": random.randint(50, 200)},
                {"name": "Tushare", "status": "在线", "latency": random.randint(80, 300)},
                {"name": "Wind", "status": "离线", "latency": 0},
                {"name": "Yahoo", "status": "在线", "latency": random.randint(100, 400)}
            ]

            self.status_table.setRowCount(len(sources))

            for row, source in enumerate(sources):
                # 数据源名称
                name_item = QTableWidgetItem(source["name"])
                self.status_table.setItem(row, 0, name_item)

                # 状态
                status_item = QTableWidgetItem(source["status"])
                if source["status"] == "在线":
                    status_item.setBackground(QColor("#E8F5E8"))
                else:
                    status_item.setBackground(QColor("#FFEBEE"))
                self.status_table.setItem(row, 1, status_item)

                # 延迟
                latency_text = f"{source['latency']}" if source['latency'] > 0 else "N/A"
                latency_item = QTableWidgetItem(latency_text)
                self.status_table.setItem(row, 2, latency_item)

        except Exception as e:
            self.logger.error(f"更新数据源状态失败: {e}")


class DataDashboardWidget(QWidget):
    """数据概览仪表板主组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("DataDashboardWidget")
        self.init_ui()

        # 定时更新统计数据
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(10000)  # 每10秒更新一次

        self.logger.info("数据概览仪表板初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("数据概览仪表板")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #ffffff; background-color: transparent;")
        layout.addWidget(title_label)

        # 统计卡片区域
        self.create_stats_cards(layout)

        # 图表和状态区域
        self.create_charts_area(layout)

        # 最后更新时间
        self.last_update_label = QLabel(f"最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.last_update_label.setAlignment(Qt.AlignCenter)
        self.last_update_label.setStyleSheet("color: #888888; font-size: 12px; background-color: transparent;")
        layout.addWidget(self.last_update_label)

    def create_stats_cards(self, parent_layout):
        """创建统计卡片"""
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(15)

        # 创建统计卡片
        self.total_stocks_card = DataStatCard("股票总数", "1,234", "只", "#2196F3")
        self.total_records_card = DataStatCard("数据记录", "45.6K", "条", "#4CAF50")
        self.db_size_card = DataStatCard("数据库大小", "128.5", "MB", "#FF9800")
        self.update_rate_card = DataStatCard("更新成功率", "98.5", "%", "#9C27B0")

        cards_layout.addWidget(self.total_stocks_card)
        cards_layout.addWidget(self.total_records_card)
        cards_layout.addWidget(self.db_size_card)
        cards_layout.addWidget(self.update_rate_card)
        cards_layout.addStretch()

        parent_layout.addLayout(cards_layout)

    def create_charts_area(self, parent_layout):
        """创建图表区域"""
        charts_layout = QHBoxLayout()
        charts_layout.setSpacing(15)

        # 数据质量图表
        quality_group = QGroupBox("数据质量监控")
        quality_layout = QVBoxLayout(quality_group)
        self.quality_chart = DataQualityChart()
        quality_layout.addWidget(self.quality_chart)
        charts_layout.addWidget(quality_group, 2)

        # 数据源状态
        status_group = QGroupBox("数据源监控")
        status_layout = QVBoxLayout(status_group)
        self.source_status = DataSourceStatus()
        status_layout.addWidget(self.source_status)
        charts_layout.addWidget(status_group, 1)

        parent_layout.addLayout(charts_layout)

    def update_stats(self):
        """更新统计数据"""
        try:
            # 模拟数据更新
            total_stocks = random.randint(1200, 1300)
            total_records = random.randint(40000, 50000)
            db_size = random.uniform(120, 140)
            update_rate = random.uniform(95, 99.5)

            # 更新卡片
            self.total_stocks_card.update_value(f"{total_stocks:,}", "只")
            self.total_records_card.update_value(f"{total_records/1000:.1f}K", "条")
            self.db_size_card.update_value(f"{db_size:.1f}", "MB")
            self.update_rate_card.update_value(f"{update_rate:.1f}", "%")

            # 更新时间
            self.last_update_label.setText(f"最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            self.logger.debug("统计数据已更新")

        except Exception as e:
            self.logger.error(f"更新统计数据失败: {e}")
